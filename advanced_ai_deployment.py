#!/usr/bin/env python3
"""
Advanced AI Deployment and Monitoring System
Deploys, monitors, and maintains all AI systems with full quality assurance
"""

import asyncio
import json
import logging
import subprocess
import time
import os
import sys
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional, Tuple
from pathlib import Path
import threading
import psutil
import requests
from concurrent.futures import ThreadPoolExecutor

# Setup comprehensive logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('ai_deployment.log'),
        logging.StreamHandler()
    ]
)

class AIModelManager:
    """Manages AI model installation, updates, and health monitoring"""
    
    def __init__(self):
        self.logger = logging.getLogger("AIModelManager")
        self.required_models = [
            "nemotron-mini:4b",
            "granite3.3:8b", 
            "hermes3:8b",
            "deepseek-r1:latest",
            "falcon3:10b",
            "gemma3:27b",
            "mistral-small:24b",
            "cogito:32b",
            "marco-o1:7b",
            "command-r:35b",
            "qwen2.5vl:32b",
            "huihui_ai/acereason-nemotron-abliterated:14b",
            "goekdenizguelmez/JOSIEFIED-Qwen3:14b",
            "huihui_ai/homunculus-abliterated:latest"
        ]
        
        self.model_status = {}
        self.installation_progress = {}
        
    async def check_ollama_installation(self) -> Dict[str, Any]:
        """Check if Ollama is properly installed and running"""
        self.logger.info("🔍 Checking Ollama installation...")
        
        try:
            # Check if ollama command exists
            result = subprocess.run(
                ["ollama", "--version"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                version = result.stdout.strip()
                self.logger.info(f"✅ Ollama found: {version}")
                
                # Check if Ollama service is running
                service_status = await self._check_ollama_service()
                
                return {
                    "installed": True,
                    "version": version,
                    "service_running": service_status["running"],
                    "service_details": service_status
                }
            else:
                self.logger.error("❌ Ollama command failed")
                return {"installed": False, "error": result.stderr}
                
        except FileNotFoundError:
            self.logger.error("❌ Ollama not found in PATH")
            return {"installed": False, "error": "Ollama not found"}
        except Exception as e:
            self.logger.error(f"❌ Error checking Ollama: {e}")
            return {"installed": False, "error": str(e)}
    
    async def _check_ollama_service(self) -> Dict[str, Any]:
        """Check if Ollama service is running"""
        try:
            # Try to connect to Ollama API
            response = requests.get("http://localhost:11434/api/tags", timeout=5)
            if response.status_code == 200:
                return {
                    "running": True,
                    "api_accessible": True,
                    "response_time": response.elapsed.total_seconds()
                }
        except requests.exceptions.RequestException:
            pass
        
        # Check if Ollama process is running
        for proc in psutil.process_iter(['pid', 'name', 'cmdline']):
            try:
                if 'ollama' in proc.info['name'].lower():
                    return {
                        "running": True,
                        "api_accessible": False,
                        "process_id": proc.info['pid']
                    }
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                continue
        
        return {"running": False, "api_accessible": False}
    
    async def check_model_availability(self) -> Dict[str, Any]:
        """Check availability of all required models"""
        self.logger.info("📋 Checking model availability...")
        
        try:
            result = subprocess.run(
                ["ollama", "list"],
                capture_output=True,
                text=True,
                timeout=15
            )
            
            if result.returncode != 0:
                return {"error": "Failed to list models", "available_models": []}
            
            available_models = []
            for line in result.stdout.split('\n')[1:]:  # Skip header
                if line.strip():
                    model_name = line.split()[0]
                    available_models.append(model_name)
            
            model_status = {}
            missing_models = []
            
            for model in self.required_models:
                is_available = any(model in available for available in available_models)
                model_status[model] = {
                    "available": is_available,
                    "status": "ready" if is_available else "missing"
                }
                
                if not is_available:
                    missing_models.append(model)
            
            self.model_status = model_status
            
            return {
                "total_required": len(self.required_models),
                "available_count": len(self.required_models) - len(missing_models),
                "missing_count": len(missing_models),
                "missing_models": missing_models,
                "model_status": model_status,
                "all_available": len(missing_models) == 0
            }
            
        except Exception as e:
            self.logger.error(f"❌ Error checking models: {e}")
            return {"error": str(e), "available_models": []}
    
    async def install_missing_models(self, missing_models: List[str]) -> Dict[str, Any]:
        """Install missing AI models"""
        self.logger.info(f"📥 Installing {len(missing_models)} missing models...")
        
        installation_results = {}
        
        for model in missing_models:
            self.logger.info(f"Installing {model}...")
            
            try:
                # Start installation process
                process = subprocess.Popen(
                    ["ollama", "pull", model],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True
                )
                
                # Monitor installation with timeout
                start_time = time.time()
                timeout = 600  # 10 minutes per model
                
                while process.poll() is None:
                    if time.time() - start_time > timeout:
                        process.kill()
                        installation_results[model] = {
                            "success": False,
                            "error": "Installation timeout",
                            "duration": timeout
                        }
                        break
                    
                    await asyncio.sleep(1)
                else:
                    # Process completed
                    stdout, stderr = process.communicate()
                    duration = time.time() - start_time
                    
                    if process.returncode == 0:
                        installation_results[model] = {
                            "success": True,
                            "duration": duration,
                            "size_info": self._extract_size_info(stdout)
                        }
                        self.logger.info(f"✅ {model} installed successfully in {duration:.1f}s")
                    else:
                        installation_results[model] = {
                            "success": False,
                            "error": stderr.strip(),
                            "duration": duration
                        }
                        self.logger.error(f"❌ {model} installation failed: {stderr.strip()}")
                
            except Exception as e:
                installation_results[model] = {
                    "success": False,
                    "error": str(e),
                    "duration": 0
                }
                self.logger.error(f"❌ Error installing {model}: {e}")
        
        successful_installs = sum(1 for r in installation_results.values() if r["success"])
        
        return {
            "total_attempted": len(missing_models),
            "successful_installs": successful_installs,
            "failed_installs": len(missing_models) - successful_installs,
            "installation_results": installation_results,
            "success_rate": f"{successful_installs / len(missing_models) * 100:.1f}%" if missing_models else "100%"
        }
    
    def _extract_size_info(self, stdout: str) -> str:
        """Extract model size information from installation output"""
        for line in stdout.split('\n'):
            if 'pulling' in line.lower() and ('gb' in line.lower() or 'mb' in line.lower()):
                return line.strip()
        return "Size unknown"
    
    async def test_model_functionality(self, models_to_test: Optional[List[str]] = None) -> Dict[str, Any]:
        """Test functionality of installed models"""
        if models_to_test is None:
            models_to_test = self.required_models
        
        self.logger.info(f"🧪 Testing functionality of {len(models_to_test)} models...")
        
        test_prompt = "Hello, please respond with 'AI model working correctly' to confirm functionality."
        functionality_results = {}
        
        for model in models_to_test:
            if model not in self.model_status or not self.model_status[model]["available"]:
                functionality_results[model] = {
                    "functional": False,
                    "error": "Model not available"
                }
                continue
            
            try:
                start_time = time.time()
                
                result = subprocess.run(
                    ["ollama", "run", model, test_prompt],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                response_time = time.time() - start_time
                
                if result.returncode == 0:
                    response = result.stdout.strip()
                    is_functional = len(response) > 10 and "working" in response.lower()
                    
                    functionality_results[model] = {
                        "functional": is_functional,
                        "response_time": response_time,
                        "response_length": len(response),
                        "response_preview": response[:100] + "..." if len(response) > 100 else response
                    }
                    
                    status = "✅" if is_functional else "⚠️"
                    self.logger.info(f"{status} {model}: {response_time:.2f}s")
                else:
                    functionality_results[model] = {
                        "functional": False,
                        "error": result.stderr.strip(),
                        "response_time": response_time
                    }
                    self.logger.error(f"❌ {model}: {result.stderr.strip()}")
                    
            except subprocess.TimeoutExpired:
                functionality_results[model] = {
                    "functional": False,
                    "error": "Response timeout",
                    "response_time": 30
                }
                self.logger.error(f"⏰ {model}: Timeout")
            except Exception as e:
                functionality_results[model] = {
                    "functional": False,
                    "error": str(e),
                    "response_time": 0
                }
                self.logger.error(f"❌ {model}: {e}")
        
        functional_models = sum(1 for r in functionality_results.values() if r["functional"])
        
        return {
            "total_tested": len(models_to_test),
            "functional_models": functional_models,
            "non_functional_models": len(models_to_test) - functional_models,
            "functionality_rate": f"{functional_models / len(models_to_test) * 100:.1f}%" if models_to_test else "100%",
            "model_results": functionality_results,
            "average_response_time": sum(r.get("response_time", 0) for r in functionality_results.values()) / len(functionality_results) if functionality_results else 0
        }

class AISystemDeployer:
    """Deploys and manages AI trading systems"""
    
    def __init__(self):
        self.logger = logging.getLogger("AISystemDeployer")
        self.model_manager = AIModelManager()
        self.deployment_status = {}
        
    async def full_system_deployment(self) -> Dict[str, Any]:
        """Deploy complete AI system with all components"""
        self.logger.info("🚀 Starting Full AI System Deployment...")
        
        deployment_steps = {
            "ollama_check": self._deploy_step_ollama_check,
            "model_availability": self._deploy_step_model_check,
            "model_installation": self._deploy_step_model_installation,
            "functionality_test": self._deploy_step_functionality_test,
            "ai_service_test": self._deploy_step_ai_service_test,
            "integration_test": self._deploy_step_integration_test,
            "performance_validation": self._deploy_step_performance_validation
        }
        
        deployment_results = {
            "start_time": datetime.utcnow().isoformat(),
            "steps": {},
            "overall_success": False,
            "deployment_time": 0
        }
        
        start_time = time.time()
        
        for step_name, step_func in deployment_steps.items():
            self.logger.info(f"\n📋 Deployment Step: {step_name}")
            
            try:
                step_result = await step_func()
                deployment_results["steps"][step_name] = step_result
                
                if not step_result.get("success", False):
                    self.logger.error(f"❌ Deployment step {step_name} failed")
                    if step_result.get("critical", True):
                        deployment_results["failed_step"] = step_name
                        break
                else:
                    self.logger.info(f"✅ Deployment step {step_name} completed")
                    
            except Exception as e:
                self.logger.error(f"❌ Deployment step {step_name} error: {e}")
                deployment_results["steps"][step_name] = {
                    "success": False,
                    "error": str(e),
                    "critical": True
                }
                deployment_results["failed_step"] = step_name
                break
        
        deployment_results["deployment_time"] = time.time() - start_time
        deployment_results["end_time"] = datetime.utcnow().isoformat()
        
        # Check overall success
        successful_steps = sum(1 for step in deployment_results["steps"].values() if step.get("success", False))
        total_steps = len(deployment_steps)
        deployment_results["overall_success"] = successful_steps == total_steps
        deployment_results["success_rate"] = f"{successful_steps / total_steps * 100:.1f}%"
        
        if deployment_results["overall_success"]:
            self.logger.info("🎉 Full AI System Deployment Completed Successfully!")
        else:
            self.logger.error("❌ AI System Deployment Failed")
        
        return deployment_results
    
    async def _deploy_step_ollama_check(self) -> Dict[str, Any]:
        """Check Ollama installation and service"""
        ollama_status = await self.model_manager.check_ollama_installation()
        
        return {
            "success": ollama_status.get("installed", False) and ollama_status.get("service_running", False),
            "critical": True,
            "details": ollama_status,
            "recommendations": self._get_ollama_recommendations(ollama_status)
        }
    
    async def _deploy_step_model_check(self) -> Dict[str, Any]:
        """Check model availability"""
        model_status = await self.model_manager.check_model_availability()
        
        return {
            "success": model_status.get("all_available", False),
            "critical": False,  # Can install missing models
            "details": model_status,
            "missing_models": model_status.get("missing_models", [])
        }
    
    async def _deploy_step_model_installation(self) -> Dict[str, Any]:
        """Install missing models"""
        model_status = await self.model_manager.check_model_availability()
        missing_models = model_status.get("missing_models", [])
        
        if not missing_models:
            return {
                "success": True,
                "critical": False,
                "details": "All models already available",
                "skipped": True
            }
        
        installation_result = await self.model_manager.install_missing_models(missing_models)
        
        return {
            "success": installation_result["successful_installs"] >= len(missing_models) * 0.8,  # 80% success rate
            "critical": False,
            "details": installation_result
        }
    
    async def _deploy_step_functionality_test(self) -> Dict[str, Any]:
        """Test model functionality"""
        functionality_result = await self.model_manager.test_model_functionality()
        
        return {
            "success": functionality_result["functional_models"] >= functionality_result["total_tested"] * 0.7,  # 70% functionality
            "critical": False,
            "details": functionality_result
        }
    
    async def _deploy_step_ai_service_test(self) -> Dict[str, Any]:
        """Test AI service integration"""
        try:
            # Import and test the enhanced AI service
            sys.path.append(os.path.dirname(__file__))
            from timeout_fix_and_ai_enhancement import EnhancedAIService
            
            ai_service = EnhancedAIService()
            
            # Test a few agent types
            test_agents = ["market_watcher", "technical_analyst", "risk_monitor"]
            test_results = {}
            
            for agent_type in test_agents:
                response, metadata = await ai_service.generate_response_with_fallback(
                    agent_type, "Test AI service integration", priority="fast"
                )
                
                test_results[agent_type] = {
                    "success": metadata["success"],
                    "response_length": len(response),
                    "attempts": len(metadata["attempts"]),
                    "total_time": metadata["total_time"]
                }
            
            successful_tests = sum(1 for r in test_results.values() if r["success"])
            
            return {
                "success": successful_tests >= len(test_agents) * 0.8,
                "critical": True,
                "details": {
                    "tested_agents": len(test_agents),
                    "successful_tests": successful_tests,
                    "test_results": test_results
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "critical": True,
                "error": str(e)
            }
    
    async def _deploy_step_integration_test(self) -> Dict[str, Any]:
        """Test system integration"""
        try:
            # Test if the enhanced simulation is still running
            import requests
            
            try:
                response = requests.get("http://localhost:8080/status", timeout=5)
                simulation_running = response.status_code == 200
            except:
                simulation_running = False
            
            return {
                "success": True,  # Integration test is informational
                "critical": False,
                "details": {
                    "simulation_running": simulation_running,
                    "integration_status": "ready" if simulation_running else "simulation_not_running"
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "critical": False,
                "error": str(e)
            }
    
    async def _deploy_step_performance_validation(self) -> Dict[str, Any]:
        """Validate overall system performance"""
        try:
            # Run comprehensive performance test
            from timeout_fix_and_ai_enhancement import ComprehensiveAITester
            
            tester = ComprehensiveAITester()
            
            # Run a subset of tests for deployment validation
            timeout_test = await tester._test_timeout_handling()
            performance_test = await tester._test_performance_benchmark()
            
            overall_success = (
                timeout_test.get("passed", False) and 
                performance_test.get("passed", False)
            )
            
            return {
                "success": overall_success,
                "critical": False,
                "details": {
                    "timeout_test": timeout_test,
                    "performance_test": performance_test,
                    "validation_status": "passed" if overall_success else "needs_optimization"
                }
            }
            
        except Exception as e:
            return {
                "success": False,
                "critical": False,
                "error": str(e)
            }
    
    def _get_ollama_recommendations(self, ollama_status: Dict[str, Any]) -> List[str]:
        """Get recommendations for Ollama setup"""
        recommendations = []
        
        if not ollama_status.get("installed", False):
            recommendations.append("Install Ollama from https://ollama.ai/")
        
        if not ollama_status.get("service_running", False):
            recommendations.append("Start Ollama service: 'ollama serve'")
        
        if not recommendations:
            recommendations.append("Ollama is properly configured")
        
        return recommendations
    
    async def generate_deployment_report(self, deployment_results: Dict[str, Any]) -> str:
        """Generate comprehensive deployment report"""
        report = []
        report.append("# AI System Deployment Report")
        report.append(f"Generated: {datetime.utcnow().isoformat()}")
        report.append("")
        
        # Overall Status
        status_emoji = "✅" if deployment_results["overall_success"] else "❌"
        report.append(f"## Overall Status: {status_emoji} {deployment_results['success_rate']}")
        report.append(f"Deployment Time: {deployment_results['deployment_time']:.2f} seconds")
        report.append("")
        
        # Step Details
        report.append("## Deployment Steps")
        for step_name, step_result in deployment_results["steps"].items():
            step_emoji = "✅" if step_result.get("success", False) else "❌"
            report.append(f"### {step_emoji} {step_name.replace('_', ' ').title()}")
            
            if "details" in step_result:
                details = step_result["details"]
                if isinstance(details, dict):
                    for key, value in details.items():
                        if key not in ["model_results", "test_results", "installation_results"]:
                            report.append(f"- {key}: {value}")
                else:
                    report.append(f"- {details}")
            
            if "error" in step_result:
                report.append(f"- Error: {step_result['error']}")
            
            if "recommendations" in step_result:
                report.append("- Recommendations:")
                for rec in step_result["recommendations"]:
                    report.append(f"  - {rec}")
            
            report.append("")
        
        # System Status Summary
        report.append("## System Status Summary")
        if deployment_results["overall_success"]:
            report.append("🎉 **All AI systems are fully deployed and operational!**")
            report.append("")
            report.append("### Ready Components:")
            report.append("- ✅ Ollama AI Engine")
            report.append("- ✅ AI Model Library")
            report.append("- ✅ Enhanced AI Service")
            report.append("- ✅ Timeout Management")
            report.append("- ✅ Fallback Mechanisms")
            report.append("- ✅ Performance Monitoring")
        else:
            report.append("⚠️ **Deployment requires attention**")
            if "failed_step" in deployment_results:
                report.append(f"Failed at step: {deployment_results['failed_step']}")
        
        return "\n".join(report)

async def main():
    """Main deployment function"""
    print("🚀 Starting Advanced AI System Deployment...")
    
    deployer = AISystemDeployer()
    
    # Run full deployment
    deployment_results = await deployer.full_system_deployment()
    
    # Generate and save report
    report = await deployer.generate_deployment_report(deployment_results)
    
    # Save results and report
    timestamp = int(time.time())
    results_file = f"ai_deployment_results_{timestamp}.json"
    report_file = f"ai_deployment_report_{timestamp}.md"
    
    with open(results_file, 'w') as f:
        json.dump(deployment_results, f, indent=2, default=str)
    
    with open(report_file, 'w') as f:
        f.write(report)
    
    print(f"\n📊 Deployment Results:")
    print(f"Overall Success: {deployment_results['overall_success']}")
    print(f"Success Rate: {deployment_results['success_rate']}")
    print(f"Deployment Time: {deployment_results['deployment_time']:.2f}s")
    
    print(f"\n📄 Files Generated:")
    print(f"- Results: {results_file}")
    print(f"- Report: {report_file}")
    
    if deployment_results["overall_success"]:
        print("\n🎉 AI System Deployment Completed Successfully!")
        print("All AI systems are fully operational and ready for production use.")
    else:
        print("\n⚠️ Deployment needs attention. Check the report for details.")
    
    return deployment_results

if __name__ == "__main__":
    asyncio.run(main())