#!/usr/bin/env python3
"""
Comprehensive AI Model Testing Framework
Tests all available AI models with advanced reasoning and thinking capabilities
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class ComprehensiveAITester:
    """Advanced AI testing framework with comprehensive model evaluation"""
    
    def __init__(self):
        self.logger = logging.getLogger("ComprehensiveAITester")
        self.test_results = {}
        self.model_performance = {}
        self.start_time = datetime.utcnow()
        
    async def test_all_ai_models(self):
        """Test all available AI models with comprehensive scenarios"""
        self.logger.info("🧠 Starting Comprehensive AI Model Testing...")
        
        try:
            from services.ai_service import AIService
            ai_service = AIService()
            
            # Get all available models from the service
            all_models = {
                **ai_service.models,
                **{model: model for tier_models in ai_service.model_tiers.values() for model in tier_models}
            }
            
            # Remove duplicates
            unique_models = list(set(all_models.values()))
            
            self.logger.info(f"Found {len(unique_models)} unique AI models to test")
            
            # Test scenarios for different types of thinking
            test_scenarios = {
                "logical_reasoning": {
                    "prompt": "If all crypto traders are risk-takers, and some risk-takers are successful, can we conclude that some crypto traders are successful? Explain your reasoning step by step.",
                    "expected_elements": ["logical", "reasoning", "conclusion", "step"]
                },
                "mathematical_analysis": {
                    "prompt": "Calculate the compound annual growth rate (CAGR) for a portfolio that grew from $10,000 to $25,000 over 3 years. Show your work and explain the formula.",
                    "expected_elements": ["CAGR", "formula", "calculation", "25000", "10000"]
                },
                "market_prediction": {
                    "prompt": "Given that Bitcoin has shown a 15% increase over the past week, RSI is at 75, and trading volume has doubled, what are three possible scenarios for the next 48 hours? Rank them by probability.",
                    "expected_elements": ["scenario", "probability", "RSI", "volume", "Bitcoin"]
                },
                "risk_assessment": {
                    "prompt": "A trader wants to allocate 30% of their $100,000 portfolio to a volatile altcoin that has shown 200% gains but also 50% drawdowns. Assess the risks and provide recommendations.",
                    "expected_elements": ["risk", "volatile", "allocation", "recommendation", "drawdown"]
                },
                "strategic_thinking": {
                    "prompt": "Design a multi-timeframe trading strategy that combines technical analysis, fundamental analysis, and sentiment analysis. Explain how these three components would interact.",
                    "expected_elements": ["strategy", "technical", "fundamental", "sentiment", "timeframe"]
                },
                "creative_problem_solving": {
                    "prompt": "A trading algorithm is consistently profitable in backtests but fails in live trading. Brainstorm 5 potential causes and propose solutions for each.",
                    "expected_elements": ["backtest", "live trading", "causes", "solutions", "algorithm"]
                },
                "ethical_reasoning": {
                    "prompt": "A trading bot discovers a market inefficiency that could generate significant profits but might harm smaller retail traders. Discuss the ethical considerations and propose a balanced approach.",
                    "expected_elements": ["ethical", "inefficiency", "retail traders", "balanced", "considerations"]
                }
            }
            
            model_results = {}
            
            for model_name in unique_models:
                self.logger.info(f"\n🔍 Testing model: {model_name}")
                model_results[model_name] = await self._test_single_model(ai_service, model_name, test_scenarios)
                
                # Add delay between models to prevent overload
                await asyncio.sleep(2)
            
            self.test_results["comprehensive_ai_models"] = model_results
            
            # Analyze results
            await self._analyze_model_performance(model_results)
            
            return True
            
        except Exception as e:
            self.logger.error(f"Comprehensive AI testing failed: {e}")
            self.test_results["comprehensive_ai_models"] = {"error": str(e)}
            return False
    
    async def _test_single_model(self, ai_service: Any, model_name: str, scenarios: Dict[str, Dict]) -> Dict[str, Any]:
        """Test a single AI model with all scenarios"""
        model_results = {
            "model_name": model_name,
            "scenarios": {},
            "performance_metrics": {},
            "overall_score": 0
        }
        
        total_score = 0
        completed_scenarios = 0
        
        for scenario_name, scenario_data in scenarios.items():
            self.logger.info(f"  Testing {scenario_name}...")
            
            try:
                start_time = time.time()
                
                # Use the model directly with a custom request
                response = await self._execute_direct_model_request(
                    model_name, 
                    scenario_data["prompt"]
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response and not response.startswith("AI model") and not response.startswith("AI timeout"):
                    # Analyze response quality
                    quality_score = self._analyze_response_quality(
                        response, 
                        scenario_data["expected_elements"]
                    )
                    
                    model_results["scenarios"][scenario_name] = {
                        "success": True,
                        "response_length": len(response),
                        "response_time": response_time,
                        "quality_score": quality_score,
                        "response_preview": response[:200] + "..." if len(response) > 200 else response
                    }
                    
                    total_score += quality_score
                    completed_scenarios += 1
                    
                    self.logger.info(f"    ✅ {scenario_name}: {quality_score:.2f}/10 ({response_time:.2f}s)")
                else:
                    model_results["scenarios"][scenario_name] = {
                        "success": False,
                        "error": "Invalid or error response",
                        "response_time": response_time
                    }
                    self.logger.warning(f"    ⚠️ {scenario_name}: Invalid response")
                    
            except Exception as e:
                model_results["scenarios"][scenario_name] = {
                    "success": False,
                    "error": str(e)
                }
                self.logger.error(f"    ❌ {scenario_name}: {e}")
        
        # Calculate overall performance
        if completed_scenarios > 0:
            model_results["overall_score"] = total_score / completed_scenarios
            model_results["completion_rate"] = completed_scenarios / len(scenarios)
        
        return model_results
    
    async def _execute_direct_model_request(self, model_name: str, prompt: str) -> str:
        """Execute a direct request to a specific model"""
        import subprocess
        
        try:
            result = subprocess.run(
                ["ollama", "run", model_name, prompt],
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace',
                timeout=45  # Longer timeout for complex reasoning
            )
            
            if result.returncode == 0:
                return result.stdout.strip()
            else:
                return f"Model error: {result.stderr.strip()}"
                
        except subprocess.TimeoutExpired:
            return "Request timeout"
        except UnicodeDecodeError:
            return "Unicode decode error in model response"
        except Exception as e:
            return f"Execution error: {str(e)}"
    
    def _analyze_response_quality(self, response: str, expected_elements: List[str]) -> float:
        """Analyze the quality of an AI response"""
        if not response or len(response) < 50:
            return 0.0
        
        score = 0.0
        response_lower = response.lower()
        
        # Check for expected elements (40% of score)
        element_score = 0
        for element in expected_elements:
            if element.lower() in response_lower:
                element_score += 1
        element_score = (element_score / len(expected_elements)) * 4.0
        
        # Check response length and structure (30% of score)
        length_score = min(len(response) / 500, 1.0) * 3.0
        
        # Check for reasoning indicators (30% of score)
        reasoning_indicators = [
            "because", "therefore", "however", "analysis", "conclusion",
            "step", "first", "second", "next", "finally", "result",
            "consider", "evaluate", "assess", "recommend", "suggest"
        ]
        
        reasoning_score = 0
        for indicator in reasoning_indicators:
            if indicator in response_lower:
                reasoning_score += 0.2
        reasoning_score = min(reasoning_score, 3.0)
        
        total_score = element_score + length_score + reasoning_score
        return min(total_score, 10.0)
    
    async def _analyze_model_performance(self, model_results: Dict[str, Any]):
        """Analyze and rank model performance"""
        self.logger.info("\n📊 Analyzing Model Performance...")
        
        # Sort models by overall score
        sorted_models = sorted(
            model_results.items(),
            key=lambda x: x[1].get("overall_score", 0),
            reverse=True
        )
        
        self.logger.info("\n🏆 Model Rankings:")
        for i, (model_name, results) in enumerate(sorted_models[:10], 1):
            score = results.get("overall_score", 0)
            completion_rate = results.get("completion_rate", 0)
            self.logger.info(f"  {i}. {model_name}: {score:.2f}/10 ({completion_rate:.1%} completion)")
        
        # Store performance analysis
        self.model_performance = {
            "rankings": sorted_models,
            "top_performers": sorted_models[:5],
            "analysis_timestamp": datetime.utcnow().isoformat()
        }
    
    async def test_agent_coordination_with_ai(self):
        """Test agent coordination using AI models"""
        self.logger.info("🤝 Testing Agent Coordination with AI...")
        
        try:
            from services.ai_service import AIService
            ai_service = AIService()
            
            # Test multi-agent coordination scenarios
            coordination_scenarios = [
                {
                    "name": "Market Analysis Coordination",
                    "agents": ["market_watcher", "technical_analyst", "chief_analyst"],
                    "task": "Analyze Bitcoin market conditions and provide coordinated recommendations"
                },
                {
                    "name": "Risk Management Coordination",
                    "agents": ["risk_officer", "portfolio_manager", "compliance_auditor"],
                    "task": "Assess portfolio risk and coordinate risk mitigation strategies"
                },
                {
                    "name": "Trading Execution Coordination",
                    "agents": ["trade_executor", "risk_officer", "portfolio_manager"],
                    "task": "Coordinate a large trade execution while managing risk"
                }
            ]
            
            coordination_results = {}
            
            for scenario in coordination_scenarios:
                self.logger.info(f"  Testing {scenario['name']}...")
                
                scenario_results = {
                    "agents": scenario["agents"],
                    "responses": {},
                    "coordination_quality": 0
                }
                
                # Get responses from each agent
                for agent_type in scenario["agents"]:
                    try:
                        prompt = f"As a {agent_type}, {scenario['task']}. Coordinate with other agents and provide your specific contribution."
                        
                        response = await ai_service.generate_response_with_fallback(
                            agent_type, prompt, priority="standard"
                        )
                        
                        scenario_results["responses"][agent_type] = {
                            "success": True,
                            "response_length": len(response),
                            "response_preview": response[:150] + "..." if len(response) > 150 else response
                        }
                        
                    except Exception as e:
                        scenario_results["responses"][agent_type] = {
                            "success": False,
                            "error": str(e)
                        }
                
                # Calculate coordination quality
                successful_agents = sum(1 for r in scenario_results["responses"].values() if r.get("success", False))
                scenario_results["coordination_quality"] = successful_agents / len(scenario["agents"])
                
                coordination_results[scenario["name"]] = scenario_results
                
                self.logger.info(f"    ✅ {scenario['name']}: {successful_agents}/{len(scenario['agents'])} agents responded")
            
            self.test_results["agent_coordination"] = coordination_results
            return True
            
        except Exception as e:
            self.logger.error(f"Agent coordination testing failed: {e}")
            self.test_results["agent_coordination"] = {"error": str(e)}
            return False
    
    async def test_advanced_reasoning_capabilities(self):
        """Test advanced reasoning and thinking capabilities"""
        self.logger.info("🧩 Testing Advanced Reasoning Capabilities...")
        
        try:
            from services.ai_service import AIService
            ai_service = AIService()
            
            # Advanced reasoning tests
            reasoning_tests = {
                "chain_of_thought": {
                    "prompt": "Think step by step: A trading algorithm has a 60% win rate and average win of $100, but average loss of $150. Is this algorithm profitable? Show your reasoning process.",
                    "agent": "chief_analyst"
                },
                "counterfactual_reasoning": {
                    "prompt": "If Bitcoin had never been created, how might the cryptocurrency market have evolved differently? Consider at least 3 alternative scenarios.",
                    "agent": "strategy_researcher"
                },
                "probabilistic_thinking": {
                    "prompt": "Given these market conditions: 70% chance of rate cut, 40% chance of recession, 80% chance of continued inflation, what's the probability of a bull market in crypto? Show your probability calculations.",
                    "agent": "chief_analyst"
                },
                "systems_thinking": {
                    "prompt": "Analyze how regulatory changes in one major economy create cascading effects through the global crypto ecosystem. Map the interconnections.",
                    "agent": "compliance_auditor"
                },
                "meta_reasoning": {
                    "prompt": "Evaluate your own reasoning process: What are the limitations of AI analysis in trading decisions? How can these limitations be mitigated?",
                    "agent": "chief_analyst"
                }
            }
            
            reasoning_results = {}
            
            for test_name, test_data in reasoning_tests.items():
                self.logger.info(f"  Testing {test_name}...")
                
                try:
                    response = await ai_service.generate_response_with_fallback(
                        test_data["agent"], 
                        test_data["prompt"], 
                        priority="critical"
                    )
                    
                    # Analyze reasoning quality
                    reasoning_quality = self._analyze_reasoning_quality(response, test_name)
                    
                    reasoning_results[test_name] = {
                        "success": True,
                        "agent": test_data["agent"],
                        "response_length": len(response),
                        "reasoning_quality": reasoning_quality,
                        "response_preview": response[:200] + "..." if len(response) > 200 else response
                    }
                    
                    self.logger.info(f"    ✅ {test_name}: Quality {reasoning_quality:.2f}/10")
                    
                except Exception as e:
                    reasoning_results[test_name] = {
                        "success": False,
                        "error": str(e)
                    }
                    self.logger.error(f"    ❌ {test_name}: {e}")
            
            self.test_results["advanced_reasoning"] = reasoning_results
            return True
            
        except Exception as e:
            self.logger.error(f"Advanced reasoning testing failed: {e}")
            self.test_results["advanced_reasoning"] = {"error": str(e)}
            return False
    
    def _analyze_reasoning_quality(self, response: str, test_type: str) -> float:
        """Analyze the quality of reasoning in a response"""
        if not response or len(response) < 100:
            return 0.0
        
        response_lower = response.lower()
        score = 0.0
        
        # Test-specific quality indicators
        quality_indicators = {
            "chain_of_thought": ["step", "first", "then", "therefore", "because", "reasoning"],
            "counterfactual_reasoning": ["if", "would", "might", "scenario", "alternative", "instead"],
            "probabilistic_thinking": ["probability", "chance", "likely", "percent", "odds", "risk"],
            "systems_thinking": ["system", "interconnect", "cascade", "effect", "relationship", "network"],
            "meta_reasoning": ["limitation", "bias", "assumption", "uncertainty", "confidence", "evaluate"]
        }
        
        indicators = quality_indicators.get(test_type, [])
        
        # Check for specific indicators
        for indicator in indicators:
            if indicator in response_lower:
                score += 1.0
        
        # Normalize score
        max_score = len(indicators) if indicators else 6
        normalized_score = (score / max_score) * 10
        
        return min(normalized_score, 10.0)
    
    async def run_comprehensive_tests(self):
        """Run all comprehensive AI tests"""
        self.logger.info("🚀 Starting Comprehensive AI Testing Suite...")
        
        test_suite = [
            ("AI Models", self.test_all_ai_models),
            ("Agent Coordination", self.test_agent_coordination_with_ai),
            ("Advanced Reasoning", self.test_advanced_reasoning_capabilities)
        ]
        
        suite_results = {}
        overall_success = True
        
        for test_name, test_func in test_suite:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"Running {test_name} Tests")
            self.logger.info(f"{'='*60}")
            
            try:
                success = await test_func()
                suite_results[test_name] = {
                    "success": success,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                if not success:
                    overall_success = False
                    
            except Exception as e:
                self.logger.error(f"{test_name} test suite failed: {e}")
                suite_results[test_name] = {
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }
                overall_success = False
        
        # Generate comprehensive report
        await self._generate_comprehensive_report(suite_results, overall_success)
        
        return overall_success
    
    async def _generate_comprehensive_report(self, suite_results: Dict[str, Any], overall_success: bool):
        """Generate a comprehensive test report"""
        end_time = datetime.utcnow()
        test_duration = (end_time - self.start_time).total_seconds()
        
        report = {
            "test_type": "Comprehensive AI Testing",
            "timestamp": end_time.isoformat(),
            "test_duration": test_duration,
            "overall_success": overall_success,
            "suite_results": suite_results,
            "detailed_results": self.test_results,
            "model_performance": self.model_performance,
            "summary": {
                "total_suites": len(suite_results),
                "successful_suites": sum(1 for r in suite_results.values() if r.get("success", False)),
                "success_rate": sum(1 for r in suite_results.values() if r.get("success", False)) / len(suite_results) if suite_results else 0
            }
        }
        
        # Save report
        report_path = Path("comprehensive_ai_test_report.json")
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        self.logger.info(f"\n📋 Comprehensive test report saved to {report_path}")
        self.logger.info(f"⏱️ Total test duration: {test_duration:.2f} seconds")
        self.logger.info(f"📊 Overall success rate: {report['summary']['success_rate']:.1%}")
        
        if overall_success:
            self.logger.info("🎉 All comprehensive AI tests completed successfully!")
        else:
            self.logger.warning("⚠️ Some tests failed. Check the detailed report for issues.")

async def main():
    """Main test execution"""
    tester = ComprehensiveAITester()
    
    try:
        success = await tester.run_comprehensive_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        tester.logger.info("\n⏹️ Testing interrupted by user")
        return 1
    except Exception as e:
        tester.logger.error(f"\n💥 Testing failed with error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)