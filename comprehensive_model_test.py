#!/usr/bin/env python3
"""
Comprehensive AI Model Performance Testing
Tests all available AI models across multiple dimensions and capabilities
"""

import sys
import os
import json
import logging
import time
import asyncio
from datetime import datetime
from typing import Dict, List, Any, Optional

# Add src directory to path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

from services.ai_service import AIService

class ComprehensiveModelTester:
    """Comprehensive testing of all AI models with detailed performance metrics"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.setup_logging()
        self.results = {
            "test_type": "Comprehensive AI Model Testing",
            "timestamp": datetime.now().isoformat(),
            "models_tested": [],
            "test_scenarios": [],
            "detailed_results": {},
            "performance_metrics": {},
            "model_rankings": {},
            "summary": {}
        }
        
        # Get all available models from AI service
        self.available_models = list(set(self.ai_service.models.values()))
        self.logger.info(f"🤖 Found {len(self.available_models)} unique AI models to test")
        
        # Comprehensive test scenarios
        self.test_scenarios = {
            "reasoning_logic": {
                "prompt": "Solve this logic puzzle: If all roses are flowers, and some flowers are red, can we conclude that some roses are red? Explain your reasoning step by step.",
                "expected_elements": ["logic", "reasoning", "conclusion", "step"],
                "timeout": 45
            },
            "mathematical_calculation": {
                "prompt": "Calculate the compound interest on $10,000 invested at 8% annual interest rate for 5 years, compounded quarterly. Show your work.",
                "expected_elements": ["formula", "calculation", "quarterly", "compound"],
                "timeout": 30
            },
            "market_analysis_complex": {
                "prompt": "Analyze a scenario where Bitcoin drops 15% while gold rises 3% and the VIX increases to 35. What does this suggest about market sentiment and risk appetite?",
                "expected_elements": ["correlation", "sentiment", "risk", "analysis"],
                "timeout": 60
            },
            "creative_problem_solving": {
                "prompt": "Design a trading strategy that could work in both bull and bear markets. Explain the key principles and risk management techniques.",
                "expected_elements": ["strategy", "bull", "bear", "risk management"],
                "timeout": 60
            },
            "technical_explanation": {
                "prompt": "Explain how a neural network learns to recognize patterns in financial data. Use simple terms but be technically accurate.",
                "expected_elements": ["neural", "patterns", "learning", "weights"],
                "timeout": 45
            },
            "quick_decision_making": {
                "prompt": "Stock XYZ just broke above resistance at $50 with high volume. Quick decision: Buy, Sell, or Wait? One sentence explanation.",
                "expected_elements": ["decision", "resistance", "volume"],
                "timeout": 15
            },
            "risk_assessment_detailed": {
                "prompt": "Evaluate the risk of a portfolio with 60% stocks, 30% bonds, 10% crypto during a potential recession. Provide specific risk factors.",
                "expected_elements": ["risk", "portfolio", "recession", "factors"],
                "timeout": 50
            },
            "data_interpretation": {
                "prompt": "GDP growth: 2.1%, Inflation: 3.8%, Unemployment: 4.2%, Interest rates: 5.25%. Interpret this economic data and predict likely Fed actions.",
                "expected_elements": ["interpretation", "fed", "economic", "predict"],
                "timeout": 45
            }
        }
    
    def setup_logging(self):
        """Setup comprehensive logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('comprehensive_model_test.log'),
                logging.StreamHandler()
            ]
        )
        self.logger = logging.getLogger('ComprehensiveModelTester')
    
    async def test_model_performance(self, model_name: str) -> Dict[str, Any]:
        """Test a single model across all scenarios"""
        self.logger.info(f"🧪 Testing model: {model_name}")
        model_results = {
            "model_name": model_name,
            "scenarios": {},
            "overall_score": 0,
            "avg_response_time": 0,
            "success_rate": 0,
            "total_tests": len(self.test_scenarios)
        }
        
        total_score = 0
        total_time = 0
        successful_tests = 0
        
        for scenario_name, scenario_config in self.test_scenarios.items():
            self.logger.info(f"  📝 Testing scenario: {scenario_name}")
            
            start_time = time.time()
            try:
                # Override the model for this specific test
                original_model = self.ai_service.models.get("test_agent")
                self.ai_service.models["test_agent"] = model_name
                
                try:
                    # Test the model with this scenario
                    response = await self.ai_service.generate_response(
                        agent_type="test_agent",
                        prompt=scenario_config["prompt"],
                        context=None
                    )
                finally:
                    # Restore original model assignment
                    if original_model:
                        self.ai_service.models["test_agent"] = original_model
                    else:
                        self.ai_service.models.pop("test_agent", None)
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response and len(response.strip()) > 10:
                    # Calculate quality score based on response content
                    quality_score = self.calculate_quality_score(
                        response, scenario_config["expected_elements"]
                    )
                    
                    model_results["scenarios"][scenario_name] = {
                        "success": True,
                        "response_length": len(response),
                        "response_time": response_time,
                        "quality_score": quality_score,
                        "preview": response[:100] + "..." if len(response) > 100 else response
                    }
                    
                    total_score += quality_score
                    total_time += response_time
                    successful_tests += 1
                    
                    self.logger.info(f"    ✅ Success - Score: {quality_score:.2f}, Time: {response_time:.2f}s")
                else:
                    model_results["scenarios"][scenario_name] = {
                        "success": False,
                        "error": "Empty or insufficient response",
                        "response_time": response_time
                    }
                    self.logger.warning(f"    ❌ Failed - Empty response")
                    
            except Exception as e:
                end_time = time.time()
                response_time = end_time - start_time
                
                model_results["scenarios"][scenario_name] = {
                    "success": False,
                    "error": str(e),
                    "response_time": response_time
                }
                self.logger.error(f"    ❌ Failed - Error: {str(e)}")
        
        # Calculate overall metrics
        if successful_tests > 0:
            model_results["overall_score"] = total_score / successful_tests
            model_results["avg_response_time"] = total_time / successful_tests
        
        model_results["success_rate"] = successful_tests / len(self.test_scenarios)
        
        self.logger.info(f"🏁 Model {model_name} completed - Score: {model_results['overall_score']:.2f}, Success: {model_results['success_rate']:.1%}")
        return model_results
    
    def calculate_quality_score(self, response: str, expected_elements: List[str]) -> float:
        """Calculate quality score based on response content and expected elements"""
        if not response:
            return 0.0
        
        response_lower = response.lower()
        
        # Base score for having a response
        score = 3.0
        
        # Add points for expected elements
        for element in expected_elements:
            if element.lower() in response_lower:
                score += 1.5
        
        # Add points for response length (indicates detail)
        if len(response) > 200:
            score += 1.0
        if len(response) > 500:
            score += 1.0
        if len(response) > 1000:
            score += 0.5
        
        # Add points for structured thinking
        structure_indicators = ["first", "second", "third", "however", "therefore", "because", "step"]
        structure_count = sum(1 for indicator in structure_indicators if indicator in response_lower)
        score += min(structure_count * 0.3, 1.5)
        
        return min(score, 10.0)  # Cap at 10
    
    async def run_comprehensive_test(self):
        """Run comprehensive testing on all available models"""
        self.logger.info("🚀 Starting Comprehensive AI Model Testing")
        self.logger.info(f"📊 Testing {len(self.available_models)} models across {len(self.test_scenarios)} scenarios")
        
        start_time = time.time()
        
        # Test each model
        for model_name in self.available_models:
            try:
                model_results = await self.test_model_performance(model_name)
                self.results["detailed_results"][model_name] = model_results
                self.results["models_tested"].append(model_name)
            except Exception as e:
                self.logger.error(f"❌ Failed to test model {model_name}: {str(e)}")
        
        end_time = time.time()
        total_duration = end_time - start_time
        
        # Generate performance metrics and rankings
        self.generate_performance_metrics()
        self.generate_model_rankings()
        
        # Update results summary
        self.results["test_duration"] = total_duration
        self.results["test_scenarios"] = list(self.test_scenarios.keys())
        self.results["summary"] = {
            "total_models_tested": len(self.results["models_tested"]),
            "total_scenarios": len(self.test_scenarios),
            "total_tests_run": len(self.results["models_tested"]) * len(self.test_scenarios),
            "test_duration_minutes": total_duration / 60
        }
        
        # Save results
        self.save_results()
        
        self.logger.info(f"🎉 Comprehensive testing completed in {total_duration/60:.1f} minutes")
        self.logger.info(f"📈 Tested {len(self.results['models_tested'])} models across {len(self.test_scenarios)} scenarios")
    
    def generate_performance_metrics(self):
        """Generate detailed performance metrics"""
        metrics = {
            "by_scenario": {},
            "by_model": {},
            "overall_stats": {}
        }
        
        # Metrics by scenario
        for scenario_name in self.test_scenarios.keys():
            scenario_scores = []
            scenario_times = []
            scenario_successes = 0
            
            for model_name, model_results in self.results["detailed_results"].items():
                if scenario_name in model_results["scenarios"]:
                    scenario_result = model_results["scenarios"][scenario_name]
                    if scenario_result["success"]:
                        scenario_scores.append(scenario_result["quality_score"])
                        scenario_times.append(scenario_result["response_time"])
                        scenario_successes += 1
            
            if scenario_scores:
                metrics["by_scenario"][scenario_name] = {
                    "avg_score": sum(scenario_scores) / len(scenario_scores),
                    "avg_time": sum(scenario_times) / len(scenario_times),
                    "success_rate": scenario_successes / len(self.results["models_tested"])
                }
        
        # Metrics by model
        for model_name, model_results in self.results["detailed_results"].items():
            metrics["by_model"][model_name] = {
                "overall_score": model_results["overall_score"],
                "avg_response_time": model_results["avg_response_time"],
                "success_rate": model_results["success_rate"]
            }
        
        self.results["performance_metrics"] = metrics
    
    def generate_model_rankings(self):
        """Generate model rankings by different criteria"""
        rankings = {
            "by_overall_score": [],
            "by_speed": [],
            "by_reliability": [],
            "by_efficiency": []  # Score per second
        }
        
        model_data = []
        for model_name, model_results in self.results["detailed_results"].items():
            if model_results["success_rate"] > 0:
                efficiency = model_results["overall_score"] / max(model_results["avg_response_time"], 0.1)
                model_data.append({
                    "name": model_name,
                    "score": model_results["overall_score"],
                    "speed": 1 / max(model_results["avg_response_time"], 0.1),  # Inverse for ranking
                    "reliability": model_results["success_rate"],
                    "efficiency": efficiency
                })
        
        # Sort and rank
        rankings["by_overall_score"] = sorted(model_data, key=lambda x: x["score"], reverse=True)
        rankings["by_speed"] = sorted(model_data, key=lambda x: x["speed"], reverse=True)
        rankings["by_reliability"] = sorted(model_data, key=lambda x: x["reliability"], reverse=True)
        rankings["by_efficiency"] = sorted(model_data, key=lambda x: x["efficiency"], reverse=True)
        
        self.results["model_rankings"] = rankings
    
    def save_results(self):
        """Save comprehensive test results"""
        filename = "comprehensive_model_test_report.json"
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)
        
        self.logger.info(f"📋 Comprehensive test report saved to {filename}")
        
        # Also create a summary report
        self.create_summary_report()
    
    def create_summary_report(self):
        """Create a human-readable summary report"""
        summary_lines = []
        summary_lines.append("=" * 80)
        summary_lines.append("COMPREHENSIVE AI MODEL TESTING SUMMARY")
        summary_lines.append("=" * 80)
        summary_lines.append(f"Test Date: {self.results['timestamp']}")
        summary_lines.append(f"Duration: {self.results['test_duration']/60:.1f} minutes")
        summary_lines.append(f"Models Tested: {len(self.results['models_tested'])}")
        summary_lines.append(f"Scenarios: {len(self.test_scenarios)}")
        summary_lines.append("")
        
        # Top performers
        if "model_rankings" in self.results:
            summary_lines.append("🏆 TOP PERFORMERS:")
            summary_lines.append("-" * 40)
            
            for i, model in enumerate(self.results["model_rankings"]["by_overall_score"][:3]):
                summary_lines.append(f"{i+1}. {model['name']} - Score: {model['score']:.2f}")
            
            summary_lines.append("")
            summary_lines.append("⚡ FASTEST MODELS:")
            summary_lines.append("-" * 40)
            
            for i, model in enumerate(self.results["model_rankings"]["by_speed"][:3]):
                avg_time = 1 / model['speed']
                summary_lines.append(f"{i+1}. {model['name']} - Avg Time: {avg_time:.2f}s")
        
        summary_content = "\n".join(summary_lines)
        
        with open("comprehensive_model_test_summary.txt", 'w', encoding='utf-8') as f:
            f.write(summary_content)
        
        self.logger.info("📄 Summary report saved to comprehensive_model_test_summary.txt")
        print("\n" + summary_content)

async def main():
    """Main execution function"""
    tester = ComprehensiveModelTester()
    await tester.run_comprehensive_test()

if __name__ == "__main__":
    asyncio.run(main())