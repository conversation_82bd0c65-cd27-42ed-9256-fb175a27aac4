#!/usr/bin/env python3
"""
Comprehensive System Validation for Noryon V2
Real testing of all components - NO theoretical or simulation tests
"""

import asyncio
import json
import logging
import subprocess
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from services.ai_service import AIService

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemValidator:
    """Comprehensive system validation with real tests"""
    
    def __init__(self):
        self.ai_service = AIService()
        self.test_results = {
            'timestamp': datetime.utcnow().isoformat(),
            'model_availability': {},
            'model_functionality': {},
            'agent_performance': {},
            'security_validation': {},
            'system_integration': {},
            'performance_metrics': {},
            'errors': [],
            'summary': {}
        }
        
    async def validate_all_models(self) -> Dict[str, Any]:
        """Test every single model configured in the system"""
        logger.info("🔍 Starting comprehensive model validation...")
        
        # Get list of installed models
        installed_models = self._get_installed_models()
        configured_models = set()
        
        # Collect all configured models
        for agent_models in self.ai_service.models.values():
            configured_models.add(agent_models)
        
        for tier_models in self.ai_service.model_tiers.values():
            configured_models.update(tier_models)
            
        for security_models in self.ai_service.security_levels.values():
            configured_models.update(security_models)
        
        logger.info(f"📊 Found {len(installed_models)} installed models")
        logger.info(f"📊 Found {len(configured_models)} configured models")
        
        # Check model availability
        for model in configured_models:
            is_available = model in installed_models
            self.test_results['model_availability'][model] = {
                'available': is_available,
                'size_gb': self._get_model_size(model, installed_models) if is_available else None
            }
            
            if not is_available:
                self.test_results['errors'].append(f"Model {model} is configured but not installed")
                
        return self.test_results['model_availability']
    
    async def test_model_functionality(self) -> Dict[str, Any]:
        """Test actual functionality of each available model"""
        logger.info("🧠 Testing model functionality with real prompts...")
        
        test_prompt = "Analyze this simple market scenario: BTC price is $45,000, up 2% in 24h. Provide a brief 1-sentence analysis."
        
        available_models = [model for model, data in self.test_results['model_availability'].items() 
                          if data['available']]
        
        for model in available_models:
            logger.info(f"Testing model: {model}")
            start_time = time.time()
            
            try:
                # Direct model test
                result = subprocess.run(
                    ["ollama", "run", model, test_prompt],
                    capture_output=True,
                    text=True,
                    timeout=30
                )
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if result.returncode == 0:
                    response = result.stdout.strip()
                    self.test_results['model_functionality'][model] = {
                        'status': 'success',
                        'response_time_seconds': round(response_time, 2),
                        'response_length': len(response),
                        'response_preview': response[:100] + '...' if len(response) > 100 else response
                    }
                    logger.info(f"✅ {model}: {response_time:.2f}s, {len(response)} chars")
                else:
                    error = result.stderr.strip()
                    self.test_results['model_functionality'][model] = {
                        'status': 'error',
                        'error': error,
                        'response_time_seconds': round(response_time, 2)
                    }
                    self.test_results['errors'].append(f"Model {model} failed: {error}")
                    logger.error(f"❌ {model}: {error}")
                    
            except subprocess.TimeoutExpired:
                self.test_results['model_functionality'][model] = {
                    'status': 'timeout',
                    'response_time_seconds': 30.0
                }
                self.test_results['errors'].append(f"Model {model} timed out")
                logger.error(f"⏰ {model}: Timeout")
                
            except Exception as e:
                self.test_results['model_functionality'][model] = {
                    'status': 'exception',
                    'error': str(e)
                }
                self.test_results['errors'].append(f"Model {model} exception: {str(e)}")
                logger.error(f"💥 {model}: {str(e)}")
                
        return self.test_results['model_functionality']
    
    async def test_agent_performance(self) -> Dict[str, Any]:
        """Test each agent type with real AI service calls"""
        logger.info("🤖 Testing agent performance with real scenarios...")
        
        test_scenarios = {
            'market_watcher': {
                'prompt': 'Monitor BTC/USD: Price $45000, Volume 1.2B, RSI 65. Provide market status.',
                'context': {'symbol': 'BTC/USD', 'price': 45000, 'volume': 1200000000, 'rsi': 65}
            },
            'strategy_researcher': {
                'prompt': 'Research optimal strategy for current market: trending up, moderate volatility.',
                'context': {'trend': 'up', 'volatility': 'moderate'}
            },
            'risk_officer': {
                'prompt': 'Assess risk for 10 BTC position at $45000 entry, current price $46000.',
                'context': {'position_size': 10, 'entry_price': 45000, 'current_price': 46000}
            },
            'technical_analyst': {
                'prompt': 'Analyze: RSI 65, MACD bullish, MA50 > MA200. Provide technical outlook.',
                'context': {'rsi': 65, 'macd': 'bullish', 'ma_signal': 'golden_cross'}
            },
            'intelligence_analyst': {
                'prompt': 'Analyze market intelligence: institutional buying, regulatory clarity improving.',
                'context': {'institutional_flow': 'buying', 'regulatory_sentiment': 'positive'}
            }
        }
        
        for agent_type, scenario in test_scenarios.items():
            if agent_type in self.ai_service.models:
                logger.info(f"Testing agent: {agent_type}")
                start_time = time.time()
                
                try:
                    response = await self.ai_service.generate_response(
                        agent_type=agent_type,
                        prompt=scenario['prompt'],
                        context=scenario['context'],
                        priority='standard',
                        security_clearance='public'
                    )
                    
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    self.test_results['agent_performance'][agent_type] = {
                        'status': 'success',
                        'response_time_seconds': round(response_time, 2),
                        'response_length': len(response),
                        'model_used': self.ai_service.models[agent_type],
                        'response_preview': response[:150] + '...' if len(response) > 150 else response
                    }
                    logger.info(f"✅ {agent_type}: {response_time:.2f}s")
                    
                except Exception as e:
                    self.test_results['agent_performance'][agent_type] = {
                        'status': 'error',
                        'error': str(e),
                        'model_used': self.ai_service.models.get(agent_type, 'unknown')
                    }
                    self.test_results['errors'].append(f"Agent {agent_type} failed: {str(e)}")
                    logger.error(f"❌ {agent_type}: {str(e)}")
        
        return self.test_results['agent_performance']
    
    async def test_security_levels(self) -> Dict[str, Any]:
        """Test security clearance system with real model access"""
        logger.info("🔒 Testing security clearance system...")
        
        security_tests = {
            'public_access': {
                'clearance': 'public',
                'expected_models': self.ai_service.security_levels['public']
            },
            'restricted_access': {
                'clearance': 'restricted', 
                'expected_models': self.ai_service.security_levels['restricted']
            },
            'classified_access': {
                'clearance': 'classified',
                'expected_models': self.ai_service.security_levels['classified']
            },
            'top_secret_access': {
                'clearance': 'top_secret',
                'expected_models': self.ai_service.security_levels['top_secret']
            }
        }
        
        for test_name, test_config in security_tests.items():
            logger.info(f"Testing security level: {test_config['clearance']}")
            
            # Test with uncensored agent that requires high clearance
            try:
                response = await self.ai_service.generate_response(
                    agent_type='uncensored_analyst',
                    prompt='Analyze market sentiment without restrictions.',
                    security_clearance=test_config['clearance']
                )
                
                self.test_results['security_validation'][test_name] = {
                    'status': 'success',
                    'clearance_level': test_config['clearance'],
                    'response_received': len(response) > 0,
                    'available_models': len(test_config['expected_models'])
                }
                
            except Exception as e:
                self.test_results['security_validation'][test_name] = {
                    'status': 'error',
                    'error': str(e),
                    'clearance_level': test_config['clearance']
                }
        
        return self.test_results['security_validation']
    
    def _get_installed_models(self) -> List[str]:
        """Get list of actually installed Ollama models"""
        try:
            result = subprocess.run(["ollama", "list"], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')[1:]  # Skip header
                models = []
                for line in lines:
                    if line.strip():
                        # Extract model name (first column)
                        model_name = line.split()[0]
                        models.append(model_name)
                return models
            return []
        except Exception as e:
            logger.error(f"Failed to get installed models: {e}")
            return []
    
    def _get_model_size(self, model: str, installed_models: List[str]) -> Optional[str]:
        """Extract model size from ollama list output"""
        try:
            result = subprocess.run(["ollama", "list"], capture_output=True, text=True, timeout=10)
            if result.returncode == 0:
                for line in result.stdout.split('\n'):
                    if model in line:
                        parts = line.split()
                        if len(parts) >= 3:
                            return parts[2]  # Size column
            return None
        except:
            return None
    
    async def generate_performance_report(self) -> Dict[str, Any]:
        """Generate comprehensive performance metrics"""
        logger.info("📊 Generating performance report...")
        
        # Calculate summary statistics
        total_models = len(self.test_results['model_availability'])
        available_models = sum(1 for data in self.test_results['model_availability'].values() if data['available'])
        working_models = sum(1 for data in self.test_results['model_functionality'].values() if data['status'] == 'success')
        
        successful_agents = sum(1 for data in self.test_results['agent_performance'].values() if data['status'] == 'success')
        total_agents_tested = len(self.test_results['agent_performance'])
        
        # Calculate average response times
        response_times = [data['response_time_seconds'] for data in self.test_results['model_functionality'].values() 
                         if 'response_time_seconds' in data and data['status'] == 'success']
        avg_response_time = sum(response_times) / len(response_times) if response_times else 0
        
        self.test_results['performance_metrics'] = {
            'model_availability_rate': round((available_models / total_models) * 100, 2) if total_models > 0 else 0,
            'model_functionality_rate': round((working_models / available_models) * 100, 2) if available_models > 0 else 0,
            'agent_success_rate': round((successful_agents / total_agents_tested) * 100, 2) if total_agents_tested > 0 else 0,
            'average_response_time_seconds': round(avg_response_time, 2),
            'total_errors': len(self.test_results['errors']),
            'models_tested': {
                'total_configured': total_models,
                'available': available_models,
                'working': working_models
            },
            'agents_tested': {
                'total': total_agents_tested,
                'successful': successful_agents
            }
        }
        
        # Generate summary
        self.test_results['summary'] = {
            'overall_status': 'PASS' if len(self.test_results['errors']) == 0 and working_models > 0 else 'FAIL',
            'system_health': 'EXCELLENT' if working_models >= available_models * 0.9 else 
                           'GOOD' if working_models >= available_models * 0.7 else 
                           'POOR',
            'recommendations': self._generate_recommendations()
        }
        
        return self.test_results['performance_metrics']
    
    def _generate_recommendations(self) -> List[str]:
        """Generate actionable recommendations based on test results"""
        recommendations = []
        
        # Check for missing models
        missing_models = [model for model, data in self.test_results['model_availability'].items() 
                         if not data['available']]
        if missing_models:
            recommendations.append(f"Install missing models: {', '.join(missing_models[:5])}")
        
        # Check for slow models
        slow_models = [model for model, data in self.test_results['model_functionality'].items() 
                      if data.get('response_time_seconds', 0) > 10]
        if slow_models:
            recommendations.append(f"Optimize slow models (>10s): {', '.join(slow_models[:3])}")
        
        # Check error rate
        if len(self.test_results['errors']) > 5:
            recommendations.append("High error rate detected - review system configuration")
        
        # Check agent performance
        failed_agents = [agent for agent, data in self.test_results['agent_performance'].items() 
                        if data['status'] != 'success']
        if failed_agents:
            recommendations.append(f"Fix failing agents: {', '.join(failed_agents)}")
        
        if not recommendations:
            recommendations.append("System is performing optimally - no immediate actions required")
        
        return recommendations
    
    async def run_full_validation(self) -> Dict[str, Any]:
        """Run complete system validation"""
        logger.info("🚀 Starting comprehensive system validation...")
        
        try:
            # Run all validation tests
            await self.validate_all_models()
            await self.test_model_functionality()
            await self.test_agent_performance()
            await self.test_security_levels()
            await self.generate_performance_report()
            
            logger.info("✅ Validation complete!")
            return self.test_results
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            self.test_results['errors'].append(f"Validation failed: {str(e)}")
            return self.test_results

async def main():
    """Main validation function"""
    validator = SystemValidator()
    results = await validator.run_full_validation()
    
    # Save results
    timestamp = datetime.utcnow().strftime('%Y%m%d_%H%M%S')
    filename = f'system_validation_report_{timestamp}.json'
    
    with open(filename, 'w') as f:
        json.dump(results, f, indent=2, default=str)
    
    # Print summary
    print("\n" + "="*80)
    print("🔍 NORYON V2 SYSTEM VALIDATION REPORT")
    print("="*80)
    print(f"📅 Timestamp: {results['timestamp']}")
    print(f"📊 Overall Status: {results['summary']['overall_status']}")
    print(f"🏥 System Health: {results['summary']['system_health']}")
    print(f"📈 Model Availability: {results['performance_metrics']['model_availability_rate']}%")
    print(f"⚡ Model Functionality: {results['performance_metrics']['model_functionality_rate']}%")
    print(f"🤖 Agent Success Rate: {results['performance_metrics']['agent_success_rate']}%")
    print(f"⏱️  Average Response Time: {results['performance_metrics']['average_response_time_seconds']}s")
    print(f"❌ Total Errors: {results['performance_metrics']['total_errors']}")
    
    if results['errors']:
        print("\n🚨 ERRORS DETECTED:")
        for error in results['errors'][:5]:  # Show first 5 errors
            print(f"  • {error}")
        if len(results['errors']) > 5:
            print(f"  ... and {len(results['errors']) - 5} more errors")
    
    print("\n💡 RECOMMENDATIONS:")
    for rec in results['summary']['recommendations']:
        print(f"  • {rec}")
    
    print(f"\n📄 Full report saved to: {filename}")
    print("="*80)
    
    return results

if __name__ == "__main__":
    asyncio.run(main())