#!/usr/bin/env python3
"""
Noryon V2 - Enhanced Realistic Trading Simulation
A comprehensive realistic simulation without heavy database dependencies

This provides:
- Realistic market simulation with advanced features
- Multiple AI agent types with sophisticated strategies
- Real-time web dashboard
- Advanced order execution with slippage and market impact
- Risk management and portfolio analytics
- Performance monitoring and reporting
"""

import asyncio
import argparse
import sys
import os
import json
import logging
import random
import time
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
from decimal import Decimal
from dataclasses import dataclass, asdict
import math

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    from fastapi import FastAPI, Request
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.staticfiles import StaticFiles
    import uvicorn
except ImportError:
    print("Installing required packages...")
    os.system("pip install fastapi uvicorn")
    from fastapi import FastAPI, Request
    from fastapi.responses import HTMLResponse, JSONResponse
    from fastapi.staticfiles import StaticFiles
    import uvicorn

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

@dataclass
class MarketCondition:
    """Market condition parameters"""
    volatility: float = 0.02
    trend: float = 0.0  # -1 to 1
    liquidity: float = 1.0
    news_impact: float = 0.0
    session: str = "regular"  # regular, pre_market, after_hours

@dataclass
class OrderExecution:
    """Order execution result"""
    order_id: str
    symbol: str
    side: str
    quantity: float
    price: float
    executed_price: float
    slippage: float
    commission: float
    timestamp: datetime
    status: str

class RealisticMarketSimulator:
    """Advanced market simulator with realistic features"""
    
    def __init__(self, symbols: List[str]):
        self.symbols = symbols
        self.prices = {symbol: 50000.0 if 'BTC' in symbol else 3000.0 if 'ETH' in symbol else 1.0 for symbol in symbols}
        self.volumes = {symbol: 0.0 for symbol in symbols}
        self.order_books = {symbol: {'bids': [], 'asks': []} for symbol in symbols}
        self.market_conditions = {symbol: MarketCondition() for symbol in symbols}
        self.price_history = {symbol: [] for symbol in symbols}
        self.running = False
        self.tick_count = 0
        
        # Market sessions
        self.sessions = {
            'pre_market': (4, 9),    # 4 AM - 9 AM
            'regular': (9, 16),      # 9 AM - 4 PM
            'after_hours': (16, 20)  # 4 PM - 8 PM
        }
    
    def get_current_session(self) -> str:
        """Get current market session"""
        current_hour = datetime.now().hour
        for session, (start, end) in self.sessions.items():
            if start <= current_hour < end:
                return session
        return 'closed'
    
    def calculate_slippage(self, symbol: str, side: str, quantity: float) -> float:
        """Calculate realistic slippage based on market conditions"""
        base_slippage = 0.001  # 0.1%
        
        # Adjust for market conditions
        condition = self.market_conditions[symbol]
        volatility_factor = condition.volatility * 2
        liquidity_factor = (2 - condition.liquidity) * 0.5
        
        # Adjust for order size (market impact)
        size_factor = min(quantity / 1000, 0.01)  # Cap at 1%
        
        total_slippage = base_slippage * (1 + volatility_factor + liquidity_factor + size_factor)
        
        # Apply direction (buying = positive slippage, selling = negative)
        return total_slippage if side == 'buy' else -total_slippage
    
    def update_market_conditions(self, symbol: str):
        """Update market conditions based on various factors"""
        condition = self.market_conditions[symbol]
        
        # Random market events
        if random.random() < 0.01:  # 1% chance per tick
            condition.news_impact = random.uniform(-0.05, 0.05)
            logger.info(f"📰 News event for {symbol}: {condition.news_impact:.3f} impact")
        
        # Volatility clustering
        condition.volatility = max(0.005, condition.volatility * 0.99 + random.uniform(-0.001, 0.001))
        
        # Trend persistence with mean reversion
        condition.trend = condition.trend * 0.95 + random.uniform(-0.1, 0.1)
        condition.trend = max(-1, min(1, condition.trend))
        
        # Session-based liquidity
        session = self.get_current_session()
        if session == 'regular':
            condition.liquidity = random.uniform(0.8, 1.2)
        elif session in ['pre_market', 'after_hours']:
            condition.liquidity = random.uniform(0.3, 0.7)
        else:
            condition.liquidity = 0.1
        
        # Decay news impact
        condition.news_impact *= 0.95
    
    async def generate_price_tick(self, symbol: str):
        """Generate realistic price movement"""
        condition = self.market_conditions[symbol]
        current_price = self.prices[symbol]
        
        # Base random walk
        random_change = random.gauss(0, condition.volatility)
        
        # Add trend component
        trend_change = condition.trend * 0.001
        
        # Add news impact
        news_change = condition.news_impact * 0.1
        
        # Combine all factors
        total_change = random_change + trend_change + news_change
        
        # Apply change
        new_price = current_price * (1 + total_change)
        new_price = max(new_price, current_price * 0.9)  # Circuit breaker
        new_price = min(new_price, current_price * 1.1)  # Circuit breaker
        
        self.prices[symbol] = new_price
        
        # Update price history
        self.price_history[symbol].append({
            'timestamp': datetime.now(timezone.utc),
            'price': new_price,
            'volume': random.uniform(100, 1000) * condition.liquidity
        })
        
        # Keep only last 1000 ticks
        if len(self.price_history[symbol]) > 1000:
            self.price_history[symbol] = self.price_history[symbol][-1000:]
    
    async def start(self):
        """Start market simulation"""
        self.running = True
        logger.info("🏪 Realistic market simulator started")
        
        while self.running:
            for symbol in self.symbols:
                self.update_market_conditions(symbol)
                await self.generate_price_tick(symbol)
            
            self.tick_count += 1
            await asyncio.sleep(0.1)  # 10 ticks per second
    
    async def stop(self):
        """Stop market simulation"""
        self.running = False
        logger.info("🏪 Market simulator stopped")
    
    def get_market_data(self) -> Dict[str, Any]:
        """Get current market data"""
        return {
            'prices': self.prices.copy(),
            'volumes': self.volumes.copy(),
            'conditions': {symbol: asdict(condition) for symbol, condition in self.market_conditions.items()},
            'session': self.get_current_session(),
            'tick_count': self.tick_count
        }

class EnhancedTradingAgent:
    """Enhanced trading agent with sophisticated strategies"""
    
    def __init__(self, agent_id: str, agent_type: str, initial_capital: float = 100000):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.initial_capital = initial_capital
        self.portfolio = {
            'cash': initial_capital,
            'positions': {},
            'total_value': initial_capital,
            'pnl': 0.0,
            'pnl_percent': 0.0
        }
        self.orders = []
        self.trades = []
        self.risk_limits = {
            'max_position_size': 0.1,  # 10% of portfolio
            'max_daily_loss': 0.05,    # 5% daily loss limit
            'max_leverage': 1.0        # No leverage
        }
        self.strategy_params = self._initialize_strategy_params()
        self.last_action_time = datetime.now()
        
    def _initialize_strategy_params(self) -> Dict[str, Any]:
        """Initialize strategy-specific parameters"""
        if self.agent_type == 'scalper':
            return {
                'profit_target': 0.002,  # 0.2%
                'stop_loss': 0.001,      # 0.1%
                'holding_time': 300,     # 5 minutes
                'min_spread': 0.0005     # 0.05%
            }
        elif self.agent_type == 'swing_trader':
            return {
                'profit_target': 0.05,   # 5%
                'stop_loss': 0.02,       # 2%
                'holding_time': 86400,   # 1 day
                'trend_threshold': 0.01  # 1%
            }
        elif self.agent_type == 'momentum':
            return {
                'momentum_threshold': 0.01,  # 1%
                'profit_target': 0.03,       # 3%
                'stop_loss': 0.015,          # 1.5%
                'lookback_period': 20
            }
        elif self.agent_type == 'mean_reversion':
            return {
                'deviation_threshold': 0.02,  # 2%
                'profit_target': 0.01,        # 1%
                'stop_loss': 0.005,           # 0.5%
                'lookback_period': 50
            }
        elif self.agent_type == 'arbitrage':
            return {
                'min_spread': 0.001,     # 0.1%
                'max_exposure': 0.05,    # 5%
                'execution_speed': 0.1   # 100ms
            }
        else:
            return {}
    
    def calculate_portfolio_value(self, market_data: Dict[str, Any]) -> float:
        """Calculate current portfolio value"""
        total_value = self.portfolio['cash']
        
        for symbol, quantity in self.portfolio['positions'].items():
            if symbol in market_data['prices']:
                total_value += quantity * market_data['prices'][symbol]
        
        self.portfolio['total_value'] = total_value
        self.portfolio['pnl'] = total_value - self.initial_capital
        self.portfolio['pnl_percent'] = (total_value / self.initial_capital - 1) * 100
        
        return total_value
    
    def can_trade(self, symbol: str, side: str, quantity: float, price: float) -> bool:
        """Check if trade is allowed based on risk limits"""
        # Check position size limit
        position_value = quantity * price
        if position_value > self.portfolio['total_value'] * self.risk_limits['max_position_size']:
            return False
        
        # Check cash availability for buy orders
        if side == 'buy' and position_value > self.portfolio['cash']:
            return False
        
        # Check position availability for sell orders
        if side == 'sell':
            current_position = self.portfolio['positions'].get(symbol, 0)
            if quantity > current_position:
                return False
        
        return True
    
    async def make_trading_decision(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Make trading decision based on strategy"""
        # Update portfolio value
        self.calculate_portfolio_value(market_data)
        
        # Check if enough time has passed since last action
        if (datetime.now() - self.last_action_time).seconds < 10:
            return None
        
        # Strategy-specific logic
        if self.agent_type == 'scalper':
            return await self._scalping_strategy(market_data)
        elif self.agent_type == 'swing_trader':
            return await self._swing_trading_strategy(market_data)
        elif self.agent_type == 'momentum':
            return await self._momentum_strategy(market_data)
        elif self.agent_type == 'mean_reversion':
            return await self._mean_reversion_strategy(market_data)
        elif self.agent_type == 'arbitrage':
            return await self._arbitrage_strategy(market_data)
        
        return None
    
    async def _scalping_strategy(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Scalping strategy implementation"""
        # Look for quick profit opportunities
        for symbol in market_data['prices']:
            current_price = market_data['prices'][symbol]
            condition = market_data['conditions'][symbol]
            
            # Check if spread is wide enough
            if condition['volatility'] > self.strategy_params['min_spread']:
                # Random decision with bias towards current trend
                if random.random() < 0.1:  # 10% chance to trade
                    side = 'buy' if condition['trend'] > 0 else 'sell'
                    quantity = min(1.0, self.portfolio['cash'] / current_price * 0.1)
                    
                    if self.can_trade(symbol, side, quantity, current_price):
                        self.last_action_time = datetime.now()
                        return {
                            'symbol': symbol,
                            'side': side,
                            'quantity': quantity,
                            'order_type': 'market',
                            'strategy': 'scalping'
                        }
        return None
    
    async def _swing_trading_strategy(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Swing trading strategy implementation"""
        # Look for trend-following opportunities
        for symbol in market_data['prices']:
            condition = market_data['conditions'][symbol]
            
            # Strong trend signal
            if abs(condition['trend']) > self.strategy_params['trend_threshold']:
                if random.random() < 0.05:  # 5% chance to trade
                    side = 'buy' if condition['trend'] > 0 else 'sell'
                    current_price = market_data['prices'][symbol]
                    quantity = min(2.0, self.portfolio['cash'] / current_price * 0.2)
                    
                    if self.can_trade(symbol, side, quantity, current_price):
                        self.last_action_time = datetime.now()
                        return {
                            'symbol': symbol,
                            'side': side,
                            'quantity': quantity,
                            'order_type': 'market',
                            'strategy': 'swing_trading'
                        }
        return None
    
    async def _momentum_strategy(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Momentum strategy implementation"""
        # Follow strong price movements
        for symbol in market_data['prices']:
            condition = market_data['conditions'][symbol]
            
            # High momentum signal
            if condition['volatility'] > 0.02 and abs(condition['trend']) > 0.5:
                if random.random() < 0.08:  # 8% chance to trade
                    side = 'buy' if condition['trend'] > 0 else 'sell'
                    current_price = market_data['prices'][symbol]
                    quantity = min(1.5, self.portfolio['cash'] / current_price * 0.15)
                    
                    if self.can_trade(symbol, side, quantity, current_price):
                        self.last_action_time = datetime.now()
                        return {
                            'symbol': symbol,
                            'side': side,
                            'quantity': quantity,
                            'order_type': 'market',
                            'strategy': 'momentum'
                        }
        return None
    
    async def _mean_reversion_strategy(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Mean reversion strategy implementation"""
        # Trade against extreme movements
        for symbol in market_data['prices']:
            condition = market_data['conditions'][symbol]
            
            # Extreme deviation signal
            if condition['volatility'] > 0.03:
                if random.random() < 0.06:  # 6% chance to trade
                    # Trade against the trend
                    side = 'sell' if condition['trend'] > 0 else 'buy'
                    current_price = market_data['prices'][symbol]
                    quantity = min(1.0, self.portfolio['cash'] / current_price * 0.1)
                    
                    if self.can_trade(symbol, side, quantity, current_price):
                        self.last_action_time = datetime.now()
                        return {
                            'symbol': symbol,
                            'side': side,
                            'quantity': quantity,
                            'order_type': 'market',
                            'strategy': 'mean_reversion'
                        }
        return None
    
    async def _arbitrage_strategy(self, market_data: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """Arbitrage strategy implementation"""
        # Look for price discrepancies (simplified)
        if len(market_data['prices']) > 1:
            symbols = list(market_data['prices'].keys())
            if random.random() < 0.03:  # 3% chance to trade
                symbol = random.choice(symbols)
                side = random.choice(['buy', 'sell'])
                current_price = market_data['prices'][symbol]
                quantity = min(0.5, self.portfolio['cash'] / current_price * 0.05)
                
                if self.can_trade(symbol, side, quantity, current_price):
                    self.last_action_time = datetime.now()
                    return {
                        'symbol': symbol,
                        'side': side,
                        'quantity': quantity,
                        'order_type': 'market',
                        'strategy': 'arbitrage'
                    }
        return None

class EnhancedOrderEngine:
    """Enhanced order execution engine with realistic features"""
    
    def __init__(self, market_simulator: RealisticMarketSimulator):
        self.market_simulator = market_simulator
        self.pending_orders = []
        self.executed_orders = []
        self.commission_rate = 0.001  # 0.1%
        self.order_id_counter = 0
    
    def generate_order_id(self) -> str:
        """Generate unique order ID"""
        self.order_id_counter += 1
        return f"ORD_{self.order_id_counter:06d}"
    
    async def submit_order(self, agent_id: str, order: Dict[str, Any]) -> OrderExecution:
        """Submit and execute order with realistic features"""
        order_id = self.generate_order_id()
        symbol = order['symbol']
        side = order['side']
        quantity = order['quantity']
        
        # Get current market price
        market_price = self.market_simulator.prices[symbol]
        
        # Calculate slippage
        slippage = self.market_simulator.calculate_slippage(symbol, side, quantity)
        executed_price = market_price * (1 + slippage)
        
        # Calculate commission
        commission = quantity * executed_price * self.commission_rate
        
        # Create execution result
        execution = OrderExecution(
            order_id=order_id,
            symbol=symbol,
            side=side,
            quantity=quantity,
            price=market_price,
            executed_price=executed_price,
            slippage=slippage,
            commission=commission,
            timestamp=datetime.now(timezone.utc),
            status='filled'
        )
        
        self.executed_orders.append(execution)
        
        logger.info(f"📋 Order executed: {agent_id} {side} {quantity:.4f} {symbol} @ {executed_price:.2f} (slippage: {slippage*100:.3f}%)")
        
        return execution
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get order execution statistics"""
        if not self.executed_orders:
            return {}
        
        total_orders = len(self.executed_orders)
        total_volume = sum(order.quantity * order.executed_price for order in self.executed_orders)
        avg_slippage = sum(abs(order.slippage) for order in self.executed_orders) / total_orders
        total_commission = sum(order.commission for order in self.executed_orders)
        
        return {
            'total_orders': total_orders,
            'total_volume': total_volume,
            'average_slippage': avg_slippage,
            'total_commission': total_commission,
            'orders_per_minute': total_orders / max(1, (datetime.now(timezone.utc) - self.executed_orders[0].timestamp).seconds / 60)
        }

class EnhancedRealisticSimulation:
    """Main enhanced realistic simulation coordinator"""
    
    def __init__(self, symbols: List[str], num_agents: int = 15, initial_capital: float = 100000):
        self.symbols = symbols
        self.market_simulator = RealisticMarketSimulator(symbols)
        self.order_engine = EnhancedOrderEngine(self.market_simulator)
        self.agents = []
        
        # Create diverse agent types
        agent_types = ['scalper', 'swing_trader', 'momentum', 'mean_reversion', 'arbitrage']
        for i in range(num_agents):
            agent_type = agent_types[i % len(agent_types)]
            agent = EnhancedTradingAgent(f"agent_{i:02d}", agent_type, initial_capital)
            self.agents.append(agent)
        
        self.running = False
        self.start_time = None
        self.stats = {
            'total_trades': 0,
            'total_volume': 0.0,
            'total_pnl': 0.0,
            'best_performer': None,
            'worst_performer': None
        }
        
        logger.info(f"🎯 Enhanced realistic simulation initialized with {num_agents} agents")
        logger.info(f"💰 Total initial capital: ${num_agents * initial_capital:,.2f}")
        logger.info(f"📈 Trading symbols: {', '.join(symbols)}")
    
    async def start(self):
        """Start the enhanced simulation"""
        self.running = True
        self.start_time = datetime.now(timezone.utc)
        
        logger.info("🚀 Starting enhanced realistic trading simulation...")
        
        # Start market simulator
        market_task = asyncio.create_task(self.market_simulator.start())
        
        # Start agent trading loop
        trading_task = asyncio.create_task(self._trading_loop())
        
        # Start statistics update loop
        stats_task = asyncio.create_task(self._update_statistics())
        
        # Wait for all tasks
        await asyncio.gather(market_task, trading_task, stats_task)
    
    async def _trading_loop(self):
        """Main trading loop for all agents"""
        while self.running:
            market_data = self.market_simulator.get_market_data()
            
            # Process each agent
            for agent in self.agents:
                try:
                    decision = await agent.make_trading_decision(market_data)
                    if decision:
                        # Execute the trade
                        execution = await self.order_engine.submit_order(agent.agent_id, decision)
                        
                        # Update agent portfolio
                        await self._update_agent_portfolio(agent, execution)
                        
                        self.stats['total_trades'] += 1
                        self.stats['total_volume'] += execution.quantity * execution.executed_price
                        
                except Exception as e:
                    logger.error(f"❌ Error in agent {agent.agent_id}: {e}")
            
            await asyncio.sleep(1)  # 1 second between trading cycles
    
    async def _update_agent_portfolio(self, agent: EnhancedTradingAgent, execution: OrderExecution):
        """Update agent portfolio after trade execution"""
        symbol = execution.symbol
        side = execution.side
        quantity = execution.quantity
        total_cost = execution.quantity * execution.executed_price + execution.commission
        
        if side == 'buy':
            # Add to position, subtract cash
            agent.portfolio['cash'] -= total_cost
            current_position = agent.portfolio['positions'].get(symbol, 0)
            agent.portfolio['positions'][symbol] = current_position + quantity
        else:
            # Remove from position, add cash
            agent.portfolio['cash'] += (execution.quantity * execution.executed_price - execution.commission)
            current_position = agent.portfolio['positions'].get(symbol, 0)
            agent.portfolio['positions'][symbol] = max(0, current_position - quantity)
            
            # Remove position if zero
            if agent.portfolio['positions'][symbol] == 0:
                del agent.portfolio['positions'][symbol]
        
        # Add to trades history
        agent.trades.append({
            'timestamp': execution.timestamp,
            'symbol': symbol,
            'side': side,
            'quantity': quantity,
            'price': execution.executed_price,
            'commission': execution.commission,
            'strategy': execution.order_id
        })
    
    async def _update_statistics(self):
        """Update simulation statistics"""
        while self.running:
            # Calculate total PnL
            market_data = self.market_simulator.get_market_data()
            total_pnl = 0
            best_pnl = float('-inf')
            worst_pnl = float('inf')
            best_agent = None
            worst_agent = None
            
            for agent in self.agents:
                agent.calculate_portfolio_value(market_data)
                pnl = agent.portfolio['pnl']
                total_pnl += pnl
                
                if pnl > best_pnl:
                    best_pnl = pnl
                    best_agent = agent.agent_id
                
                if pnl < worst_pnl:
                    worst_pnl = pnl
                    worst_agent = agent.agent_id
            
            self.stats['total_pnl'] = total_pnl
            self.stats['best_performer'] = {'agent': best_agent, 'pnl': best_pnl}
            self.stats['worst_performer'] = {'agent': worst_agent, 'pnl': worst_pnl}
            
            await asyncio.sleep(5)  # Update every 5 seconds
    
    async def stop(self):
        """Stop the simulation"""
        self.running = False
        await self.market_simulator.stop()
        logger.info("🛑 Enhanced realistic simulation stopped")
    
    def get_status(self) -> Dict[str, Any]:
        """Get comprehensive simulation status"""
        market_data = self.market_simulator.get_market_data()
        
        # Update all agent portfolios
        for agent in self.agents:
            agent.calculate_portfolio_value(market_data)
        
        agent_summaries = []
        for agent in self.agents:
            agent_summaries.append({
                'id': agent.agent_id,
                'type': agent.agent_type,
                'portfolio_value': agent.portfolio['total_value'],
                'pnl': agent.portfolio['pnl'],
                'pnl_percent': agent.portfolio['pnl_percent'],
                'cash': agent.portfolio['cash'],
                'positions': len(agent.portfolio['positions']),
                'trades': len(agent.trades)
            })
        
        uptime = (datetime.now(timezone.utc) - self.start_time).total_seconds() if self.start_time else 0
        
        return {
            'running': self.running,
            'uptime_seconds': uptime,
            'symbols': self.symbols,
            'market_data': market_data,
            'agents': agent_summaries,
            'stats': self.stats,
            'order_stats': self.order_engine.get_execution_statistics(),
            'total_agents': len(self.agents),
            'active_positions': sum(len(agent.portfolio['positions']) for agent in self.agents)
        }

# Global simulation instance
simulation = None

# FastAPI app for enhanced dashboard
app = FastAPI(title="Enhanced Realistic Trading Simulation Dashboard")

@app.get("/", response_class=HTMLResponse)
async def dashboard():
    """Enhanced dashboard with comprehensive features"""
    return """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Enhanced Realistic Trading Simulation</title>
        <style>
            body { 
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; 
                margin: 0; 
                padding: 20px; 
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
            }
            .container { 
                max-width: 1400px; 
                margin: 0 auto; 
                background: rgba(255,255,255,0.1);
                border-radius: 15px;
                padding: 30px;
                backdrop-filter: blur(10px);
            }
            .header {
                text-align: center;
                margin-bottom: 30px;
                border-bottom: 2px solid rgba(255,255,255,0.3);
                padding-bottom: 20px;
            }
            .header h1 {
                font-size: 2.5em;
                margin: 0;
                text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
            }
            .header p {
                font-size: 1.2em;
                margin: 10px 0;
                opacity: 0.9;
            }
            .controls {
                text-align: center;
                margin-bottom: 30px;
            }
            .btn {
                background: linear-gradient(45deg, #4CAF50, #45a049);
                color: white;
                border: none;
                padding: 12px 24px;
                margin: 0 10px;
                border-radius: 25px;
                cursor: pointer;
                font-size: 16px;
                transition: all 0.3s;
                box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            }
            .btn:hover {
                transform: translateY(-2px);
                box-shadow: 0 6px 20px rgba(0,0,0,0.3);
            }
            .btn.stop {
                background: linear-gradient(45deg, #f44336, #d32f2f);
            }
            .grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
                gap: 20px;
                margin-bottom: 30px;
            }
            .card {
                background: rgba(255,255,255,0.15);
                border-radius: 10px;
                padding: 20px;
                backdrop-filter: blur(5px);
                border: 1px solid rgba(255,255,255,0.2);
            }
            .card h3 {
                margin-top: 0;
                color: #fff;
                border-bottom: 1px solid rgba(255,255,255,0.3);
                padding-bottom: 10px;
            }
            .stat-grid {
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
                gap: 15px;
            }
            .stat-item {
                text-align: center;
                background: rgba(255,255,255,0.1);
                padding: 15px;
                border-radius: 8px;
            }
            .stat-item h4 {
                margin: 0 0 5px 0;
                font-size: 0.9em;
                opacity: 0.8;
            }
            .stat-item p {
                margin: 0;
                font-size: 1.4em;
                font-weight: bold;
            }
            .positive { color: #4CAF50; }
            .negative { color: #f44336; }
            .neutral { color: #FFC107; }
            .agents-table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 15px;
            }
            .agents-table th,
            .agents-table td {
                padding: 8px 12px;
                text-align: left;
                border-bottom: 1px solid rgba(255,255,255,0.2);
            }
            .agents-table th {
                background: rgba(255,255,255,0.2);
                font-weight: bold;
            }
            .agents-table tr:hover {
                background: rgba(255,255,255,0.1);
            }
            .market-prices {
                display: flex;
                justify-content: space-around;
                flex-wrap: wrap;
                gap: 15px;
            }
            .price-item {
                background: rgba(255,255,255,0.1);
                padding: 15px;
                border-radius: 8px;
                text-align: center;
                min-width: 120px;
            }
            .price-item h4 {
                margin: 0 0 5px 0;
                font-size: 1.1em;
            }
            .price-item p {
                margin: 0;
                font-size: 1.3em;
                font-weight: bold;
            }
            .status-indicator {
                display: inline-block;
                width: 12px;
                height: 12px;
                border-radius: 50%;
                margin-right: 8px;
            }
            .status-running { background: #4CAF50; }
            .status-stopped { background: #f44336; }
            .footer {
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid rgba(255,255,255,0.3);
                opacity: 0.8;
            }
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <h1>🎯 Enhanced Realistic Trading Simulation</h1>
                <p>🤖 Multi-Agent AI Trading System with Advanced Market Simulation</p>
                <p>💹 Real-time Order Execution • 📊 Risk Management • 🔄 Live Market Data</p>
            </div>
            
            <div class="controls">
                <button class="btn" onclick="startSimulation()">🚀 Start Simulation</button>
                <button class="btn stop" onclick="stopSimulation()">🛑 Stop Simulation</button>
                <button class="btn" onclick="refreshData()">🔄 Refresh Data</button>
            </div>
            
            <div class="grid">
                <div class="card">
                    <h3>📊 System Status</h3>
                    <div class="stat-grid">
                        <div class="stat-item">
                            <h4>Status</h4>
                            <p id="status">🔄 Loading...</p>
                        </div>
                        <div class="stat-item">
                            <h4>Uptime</h4>
                            <p id="uptime">0s</p>
                        </div>
                        <div class="stat-item">
                            <h4>Market Session</h4>
                            <p id="session">-</p>
                        </div>
                        <div class="stat-item">
                            <h4>Tick Count</h4>
                            <p id="tick_count">0</p>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>💹 Market Prices</h3>
                    <div class="market-prices" id="market_prices">
                        <div class="price-item">
                            <h4>Loading...</h4>
                            <p>$0.00</p>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>📈 Trading Statistics</h3>
                    <div class="stat-grid">
                        <div class="stat-item">
                            <h4>Total Trades</h4>
                            <p id="total_trades">0</p>
                        </div>
                        <div class="stat-item">
                            <h4>Total Volume</h4>
                            <p id="total_volume">$0</p>
                        </div>
                        <div class="stat-item">
                            <h4>Total P&L</h4>
                            <p id="total_pnl" class="neutral">$0</p>
                        </div>
                        <div class="stat-item">
                            <h4>Active Positions</h4>
                            <p id="active_positions">0</p>
                        </div>
                    </div>
                </div>
                
                <div class="card">
                    <h3>⚡ Order Execution</h3>
                    <div class="stat-grid">
                        <div class="stat-item">
                            <h4>Orders/Min</h4>
                            <p id="orders_per_minute">0</p>
                        </div>
                        <div class="stat-item">
                            <h4>Avg Slippage</h4>
                            <p id="avg_slippage">0.00%</p>
                        </div>
                        <div class="stat-item">
                            <h4>Total Commission</h4>
                            <p id="total_commission">$0</p>
                        </div>
                        <div class="stat-item">
                            <h4>Fill Rate</h4>
                            <p class="positive">100%</p>
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="card">
                <h3>🤖 Agent Performance</h3>
                <table class="agents-table">
                    <thead>
                        <tr>
                            <th>Agent ID</th>
                            <th>Type</th>
                            <th>Portfolio Value</th>
                            <th>P&L</th>
                            <th>P&L %</th>
                            <th>Cash</th>
                            <th>Positions</th>
                            <th>Trades</th>
                        </tr>
                    </thead>
                    <tbody id="agents_table">
                        <tr>
                            <td colspan="8" style="text-align: center;">Loading agent data...</td>
                        </tr>
                    </tbody>
                </table>
            </div>
            
            <div class="footer">
                <p>🎯 Noryon V2 Enhanced Realistic Trading Simulation</p>
                <p>⚡ Real-time AI Trading • 📊 Advanced Analytics • 🔒 Risk Management</p>
            </div>
        </div>
        
        <script>
            let refreshInterval;
            
            async function startSimulation() {
                try {
                    const response = await fetch('/start', { method: 'POST' });
                    const data = await response.json();
                    alert(data.message);
                    if (!refreshInterval) {
                        refreshInterval = setInterval(refreshData, 2000);
                    }
                } catch (error) {
                    alert('Error starting simulation: ' + error.message);
                }
            }
            
            async function stopSimulation() {
                try {
                    const response = await fetch('/stop', { method: 'POST' });
                    const data = await response.json();
                    alert(data.message);
                    if (refreshInterval) {
                        clearInterval(refreshInterval);
                        refreshInterval = null;
                    }
                } catch (error) {
                    alert('Error stopping simulation: ' + error.message);
                }
            }
            
            async function refreshData() {
                try {
                    const response = await fetch('/status');
                    const data = await response.json();
                    updateDashboard(data);
                } catch (error) {
                    console.error('Error fetching data:', error);
                }
            }
            
            function updateDashboard(data) {
                // System status
                document.getElementById('status').innerHTML = 
                    `<span class="status-indicator ${data.running ? 'status-running' : 'status-stopped'}"></span>${data.running ? 'Running' : 'Stopped'}`;
                document.getElementById('uptime').textContent = formatTime(data.uptime_seconds);
                document.getElementById('session').textContent = data.market_data?.session || '-';
                document.getElementById('tick_count').textContent = data.market_data?.tick_count || 0;
                
                // Market prices
                const marketPricesDiv = document.getElementById('market_prices');
                if (data.market_data?.prices) {
                    marketPricesDiv.innerHTML = '';
                    Object.entries(data.market_data.prices).forEach(([symbol, price]) => {
                        const priceItem = document.createElement('div');
                        priceItem.className = 'price-item';
                        priceItem.innerHTML = `
                            <h4>${symbol}</h4>
                            <p>$${price.toFixed(2)}</p>
                        `;
                        marketPricesDiv.appendChild(priceItem);
                    });
                }
                
                // Trading statistics
                document.getElementById('total_trades').textContent = data.stats?.total_trades || 0;
                document.getElementById('total_volume').textContent = '$' + (data.stats?.total_volume || 0).toFixed(0);
                
                const totalPnl = data.stats?.total_pnl || 0;
                const pnlElement = document.getElementById('total_pnl');
                pnlElement.textContent = '$' + totalPnl.toFixed(2);
                pnlElement.className = totalPnl >= 0 ? 'positive' : 'negative';
                
                document.getElementById('active_positions').textContent = data.active_positions || 0;
                
                // Order execution stats
                const orderStats = data.order_stats || {};
                document.getElementById('orders_per_minute').textContent = (orderStats.orders_per_minute || 0).toFixed(1);
                document.getElementById('avg_slippage').textContent = ((orderStats.average_slippage || 0) * 100).toFixed(3) + '%';
                document.getElementById('total_commission').textContent = '$' + (orderStats.total_commission || 0).toFixed(2);
                
                // Agents table
                const agentsTable = document.getElementById('agents_table');
                if (data.agents && data.agents.length > 0) {
                    agentsTable.innerHTML = '';
                    data.agents.forEach(agent => {
                        const row = document.createElement('tr');
                        const pnlClass = agent.pnl >= 0 ? 'positive' : 'negative';
                        row.innerHTML = `
                            <td>${agent.id}</td>
                            <td>${agent.type}</td>
                            <td>$${agent.portfolio_value.toFixed(2)}</td>
                            <td class="${pnlClass}">$${agent.pnl.toFixed(2)}</td>
                            <td class="${pnlClass}">${agent.pnl_percent.toFixed(2)}%</td>
                            <td>$${agent.cash.toFixed(2)}</td>
                            <td>${agent.positions}</td>
                            <td>${agent.trades}</td>
                        `;
                        agentsTable.appendChild(row);
                    });
                }
            }
            
            function formatTime(seconds) {
                const hours = Math.floor(seconds / 3600);
                const minutes = Math.floor((seconds % 3600) / 60);
                const secs = Math.floor(seconds % 60);
                return `${hours}h ${minutes}m ${secs}s`;
            }
            
            // Start auto-refresh
            refreshData();
            refreshInterval = setInterval(refreshData, 2000);
        </script>
    </body>
    </html>
    """

@app.get("/status")
async def get_status():
    """Get simulation status"""
    global simulation
    if simulation:
        return simulation.get_status()
    else:
        return {
            'running': False,
            'uptime_seconds': 0,
            'symbols': [],
            'market_data': {},
            'agents': [],
            'stats': {},
            'order_stats': {},
            'total_agents': 0,
            'active_positions': 0
        }

@app.post("/start")
async def start_simulation():
    """Start the enhanced simulation"""
    global simulation
    if not simulation or not simulation.running:
        symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT', 'DOTUSDT', 'LINKUSDT']
        simulation = EnhancedRealisticSimulation(symbols, num_agents=15, initial_capital=100000)
        
        # Start simulation in background
        asyncio.create_task(simulation.start())
        
        return {'message': '🚀 Enhanced realistic simulation started with 15 AI agents!'}
    else:
        return {'message': '⚠️ Simulation already running'}

@app.post("/stop")
async def stop_simulation():
    """Stop the simulation"""
    global simulation
    if simulation and simulation.running:
        await simulation.stop()
        return {'message': '🛑 Simulation stopped successfully'}
    else:
        return {'message': '⚠️ No simulation running'}

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Enhanced Realistic Trading Simulation",
        formatter_class=argparse.RawDescriptionHelpFormatter
    )
    
    parser.add_argument(
        '--port',
        type=int,
        default=8080,
        help='Dashboard port (default: 8080)'
    )
    
    parser.add_argument(
        '--symbols',
        type=str,
        default='BTCUSDT,ETHUSDT,ADAUSDT,DOTUSDT,LINKUSDT',
        help='Trading symbols (default: BTCUSDT,ETHUSDT,ADAUSDT,DOTUSDT,LINKUSDT)'
    )
    
    parser.add_argument(
        '--agents',
        type=int,
        default=15,
        help='Number of agents (default: 15)'
    )
    
    parser.add_argument(
        '--capital',
        type=float,
        default=100000,
        help='Initial capital per agent (default: 100000)'
    )
    
    return parser.parse_args()

def main():
    """Main entry point"""
    args = parse_arguments()
    
    print("" + "="*80)
    print("🎯 NORYON V2 - ENHANCED REALISTIC TRADING SIMULATION")
    print("" + "="*80)
    print("🚀 Professional AI trading simulation with advanced features")
    print("🤖 Multi-agent autonomous trading system")
    print("📊 Real-time monitoring and analytics")
    print("💹 Realistic market conditions and execution")
    print("🌐 Enhanced web dashboard")
    print("⚡ Advanced order execution with slippage")
    print("🔒 Risk management and portfolio analytics")
    print("" + "="*80)
    print(f"🌐 Dashboard will be available at: http://localhost:{args.port}")
    print(f"📈 Trading symbols: {args.symbols}")
    print(f"🤖 Number of agents: {args.agents}")
    print(f"💰 Initial capital per agent: ${args.capital:,.2f}")
    print(f"💰 Total initial capital: ${args.agents * args.capital:,.2f}")
    print("" + "="*80)
    print("")
    
    # Run the dashboard
    uvicorn.run(app, host="0.0.0.0", port=args.port, log_level="info")

if __name__ == "__main__":
    main()