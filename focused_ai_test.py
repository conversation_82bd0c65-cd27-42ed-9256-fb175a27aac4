#!/usr/bin/env python3
"""
Focused AI Model Testing
Tests specific AI models with robust error handling
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, Any, List
from pathlib import Path
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

class FocusedAITester:
    """Focused AI testing with robust error handling"""
    
    def __init__(self):
        self.logger = logging.getLogger("FocusedAITester")
        self.test_results = {}
        self.start_time = datetime.utcnow()
        
    async def test_core_ai_models(self):
        """Test core AI models with focused scenarios"""
        self.logger.info("🧠 Testing Core AI Models...")
        
        try:
            from services.ai_service import AIService
            ai_service = AIService()
            
            # Focus on key models that are most likely to work
            core_models = [
                "marco-o1:7b",
                "nemotron-mini:4b", 
                "hermes3:8b",
                "granite3.3:8b"
            ]
            
            # Simple but effective test scenarios
            test_scenarios = {
                "basic_reasoning": {
                    "prompt": "Explain in 2 sentences why diversification is important in trading.",
                    "min_length": 50
                },
                "market_analysis": {
                    "prompt": "Bitcoin price is $45,000 with RSI at 70. What does this suggest?",
                    "min_length": 40
                },
                "risk_assessment": {
                    "prompt": "A trader risks 5% per trade with 60% win rate. Is this sustainable?",
                    "min_length": 30
                },
                "quick_decision": {
                    "prompt": "Should I buy, sell, or hold if volume doubles but price drops 3%?",
                    "min_length": 20
                }
            }
            
            model_results = {}
            
            for model_name in core_models:
                self.logger.info(f"\n🔍 Testing model: {model_name}")
                model_results[model_name] = await self._test_model_safely(model_name, test_scenarios)
                
                # Short delay between models
                await asyncio.sleep(1)
            
            self.test_results["core_ai_models"] = model_results
            
            # Calculate success rates
            total_tests = len(core_models) * len(test_scenarios)
            successful_tests = sum(
                sum(1 for scenario in model_data.get("scenarios", {}).values() 
                    if scenario.get("success", False))
                for model_data in model_results.values()
            )
            
            success_rate = successful_tests / total_tests if total_tests > 0 else 0
            self.logger.info(f"\n📊 Core AI Models: {successful_tests}/{total_tests} tests passed ({success_rate:.1%})")
            
            return success_rate > 0.5
            
        except Exception as e:
            self.logger.error(f"Core AI model testing failed: {e}")
            self.test_results["core_ai_models"] = {"error": str(e)}
            return False
    
    async def _test_model_safely(self, model_name: str, scenarios: Dict[str, Dict]) -> Dict[str, Any]:
        """Test a model with safe error handling"""
        model_results = {
            "model_name": model_name,
            "scenarios": {},
            "overall_score": 0,
            "response_count": 0
        }
        
        total_score = 0
        successful_scenarios = 0
        
        for scenario_name, scenario_data in scenarios.items():
            self.logger.info(f"  Testing {scenario_name}...")
            
            try:
                start_time = time.time()
                
                # Use AI service with fallback
                response = await self._safe_model_request(model_name, scenario_data["prompt"])
                
                end_time = time.time()
                response_time = end_time - start_time
                
                if response and len(response) >= scenario_data["min_length"] and not "error" in response.lower():
                    score = min(len(response) / 100, 10)  # Simple scoring
                    
                    model_results["scenarios"][scenario_name] = {
                        "success": True,
                        "response_length": len(response),
                        "response_time": response_time,
                        "score": score,
                        "preview": response[:100] + "..." if len(response) > 100 else response
                    }
                    
                    total_score += score
                    successful_scenarios += 1
                    
                    self.logger.info(f"    ✅ {scenario_name}: {score:.1f}/10 ({response_time:.1f}s)")
                else:
                    model_results["scenarios"][scenario_name] = {
                        "success": False,
                        "error": "Invalid or short response",
                        "response_time": response_time,
                        "response_preview": response[:50] if response else "No response"
                    }
                    self.logger.warning(f"    ⚠️ {scenario_name}: Invalid response")
                    
            except Exception as e:
                model_results["scenarios"][scenario_name] = {
                    "success": False,
                    "error": str(e)
                }
                self.logger.error(f"    ❌ {scenario_name}: {e}")
        
        # Calculate overall performance
        if successful_scenarios > 0:
            model_results["overall_score"] = total_score / successful_scenarios
            model_results["success_rate"] = successful_scenarios / len(scenarios)
        
        return model_results
    
    async def _safe_model_request(self, model_name: str, prompt: str) -> str:
        """Make a safe request to a model using AI service"""
        try:
            from services.ai_service import AIService
            ai_service = AIService()
            
            # Override the model temporarily
            original_model = ai_service.models.get("quick_responder", "nemotron-mini:4b")
            ai_service.models["quick_responder"] = model_name
            
            # Use the AI service with shorter timeout
            response = await ai_service.generate_response_with_fallback(
                "quick_responder", 
                prompt, 
                priority="fast",
                max_retries=1
            )
            
            # Restore original model
            ai_service.models["quick_responder"] = original_model
            
            return response
            
        except Exception as e:
            return f"Request failed: {str(e)}"
    
    async def test_agent_ai_integration(self):
        """Test AI integration with different agent types"""
        self.logger.info("🤖 Testing Agent AI Integration...")
        
        try:
            from services.ai_service import AIService
            ai_service = AIService()
            
            # Test different agent types with their assigned models
            agent_tests = {
                "market_watcher": "Analyze current BTC market sentiment in one sentence.",
                "risk_officer": "Rate the risk of a 10% portfolio allocation to altcoins (1-10).",
                "trade_executor": "Should I execute a buy order for ETH at current levels? Yes/No with reason.",
                "portfolio_manager": "Suggest one portfolio rebalancing action for today.",
                "chief_analyst": "What's the most important market factor to watch this week?"
            }
            
            integration_results = {}
            
            for agent_type, prompt in agent_tests.items():
                self.logger.info(f"  Testing {agent_type}...")
                
                try:
                    start_time = time.time()
                    
                    response = await ai_service.generate_response_with_fallback(
                        agent_type, 
                        prompt, 
                        priority="standard",
                        max_retries=2
                    )
                    
                    end_time = time.time()
                    response_time = end_time - start_time
                    
                    if response and len(response) > 20 and not "error" in response.lower():
                        integration_results[agent_type] = {
                            "success": True,
                            "response_length": len(response),
                            "response_time": response_time,
                            "model_used": ai_service.models.get(agent_type, "unknown"),
                            "preview": response[:80] + "..." if len(response) > 80 else response
                        }
                        
                        self.logger.info(f"    ✅ {agent_type}: Success ({response_time:.1f}s)")
                    else:
                        integration_results[agent_type] = {
                            "success": False,
                            "error": "Invalid response",
                            "response_preview": response[:50] if response else "No response"
                        }
                        self.logger.warning(f"    ⚠️ {agent_type}: Invalid response")
                        
                except Exception as e:
                    integration_results[agent_type] = {
                        "success": False,
                        "error": str(e)
                    }
                    self.logger.error(f"    ❌ {agent_type}: {e}")
            
            self.test_results["agent_ai_integration"] = integration_results
            
            # Calculate success rate
            successful_agents = sum(1 for r in integration_results.values() if r.get("success", False))
            total_agents = len(integration_results)
            success_rate = successful_agents / total_agents if total_agents > 0 else 0
            
            self.logger.info(f"\n📊 Agent AI Integration: {successful_agents}/{total_agents} agents successful ({success_rate:.1%})")
            
            return success_rate > 0.6
            
        except Exception as e:
            self.logger.error(f"Agent AI integration testing failed: {e}")
            self.test_results["agent_ai_integration"] = {"error": str(e)}
            return False
    
    async def test_ai_reasoning_quality(self):
        """Test AI reasoning quality with focused scenarios"""
        self.logger.info("🧩 Testing AI Reasoning Quality...")
        
        try:
            from services.ai_service import AIService
            ai_service = AIService()
            
            # Focused reasoning tests
            reasoning_tests = {
                "logical_thinking": {
                    "prompt": "If crypto market cap increases but Bitcoin dominance decreases, what can we conclude about altcoins?",
                    "agent": "chief_analyst",
                    "keywords": ["altcoin", "increase", "market", "dominance"]
                },
                "risk_calculation": {
                    "prompt": "Calculate: If I risk 2% per trade with 65% win rate, what's my expected return per 100 trades?",
                    "agent": "risk_officer",
                    "keywords": ["2%", "65%", "100", "return", "calculate"]
                },
                "strategic_planning": {
                    "prompt": "Design a simple DCA strategy for Bitcoin over 6 months with $1000 monthly budget.",
                    "agent": "portfolio_manager",
                    "keywords": ["DCA", "monthly", "1000", "6 months", "strategy"]
                }
            }
            
            reasoning_results = {}
            
            for test_name, test_data in reasoning_tests.items():
                self.logger.info(f"  Testing {test_name}...")
                
                try:
                    response = await ai_service.generate_response_with_fallback(
                        test_data["agent"], 
                        test_data["prompt"], 
                        priority="standard",
                        max_retries=2
                    )
                    
                    # Check reasoning quality
                    quality_score = self._assess_reasoning_quality(response, test_data["keywords"])
                    
                    reasoning_results[test_name] = {
                        "success": True,
                        "agent": test_data["agent"],
                        "response_length": len(response),
                        "quality_score": quality_score,
                        "preview": response[:120] + "..." if len(response) > 120 else response
                    }
                    
                    self.logger.info(f"    ✅ {test_name}: Quality {quality_score:.1f}/10")
                    
                except Exception as e:
                    reasoning_results[test_name] = {
                        "success": False,
                        "error": str(e)
                    }
                    self.logger.error(f"    ❌ {test_name}: {e}")
            
            self.test_results["ai_reasoning_quality"] = reasoning_results
            
            # Calculate average quality
            successful_tests = [r for r in reasoning_results.values() if r.get("success", False)]
            if successful_tests:
                avg_quality = sum(r.get("quality_score", 0) for r in successful_tests) / len(successful_tests)
                self.logger.info(f"\n📊 Average Reasoning Quality: {avg_quality:.1f}/10")
                return avg_quality > 5.0
            else:
                return False
            
        except Exception as e:
            self.logger.error(f"AI reasoning quality testing failed: {e}")
            self.test_results["ai_reasoning_quality"] = {"error": str(e)}
            return False
    
    def _assess_reasoning_quality(self, response: str, keywords: List[str]) -> float:
        """Assess the quality of reasoning in a response"""
        if not response or len(response) < 30:
            return 0.0
        
        score = 0.0
        response_lower = response.lower()
        
        # Check for keywords (40% of score)
        keyword_score = 0
        for keyword in keywords:
            if keyword.lower() in response_lower:
                keyword_score += 1
        keyword_score = (keyword_score / len(keywords)) * 4.0
        
        # Check response length and structure (30% of score)
        length_score = min(len(response) / 200, 1.0) * 3.0
        
        # Check for reasoning indicators (30% of score)
        reasoning_words = ["because", "therefore", "since", "if", "then", "result", "conclusion"]
        reasoning_score = 0
        for word in reasoning_words:
            if word in response_lower:
                reasoning_score += 0.5
        reasoning_score = min(reasoning_score, 3.0)
        
        total_score = keyword_score + length_score + reasoning_score
        return min(total_score, 10.0)
    
    async def run_focused_tests(self):
        """Run all focused AI tests"""
        self.logger.info("🚀 Starting Focused AI Testing Suite...")
        
        test_suite = [
            ("Core AI Models", self.test_core_ai_models),
            ("Agent AI Integration", self.test_agent_ai_integration),
            ("AI Reasoning Quality", self.test_ai_reasoning_quality)
        ]
        
        suite_results = {}
        overall_success = True
        
        for test_name, test_func in test_suite:
            self.logger.info(f"\n{'='*50}")
            self.logger.info(f"Running {test_name} Tests")
            self.logger.info(f"{'='*50}")
            
            try:
                success = await test_func()
                suite_results[test_name] = {
                    "success": success,
                    "timestamp": datetime.utcnow().isoformat()
                }
                
                if not success:
                    overall_success = False
                    
            except Exception as e:
                self.logger.error(f"{test_name} test suite failed: {e}")
                suite_results[test_name] = {
                    "success": False,
                    "error": str(e),
                    "timestamp": datetime.utcnow().isoformat()
                }
                overall_success = False
        
        # Generate report
        await self._generate_focused_report(suite_results, overall_success)
        
        return overall_success
    
    async def _generate_focused_report(self, suite_results: Dict[str, Any], overall_success: bool):
        """Generate a focused test report"""
        end_time = datetime.utcnow()
        test_duration = (end_time - self.start_time).total_seconds()
        
        report = {
            "test_type": "Focused AI Testing",
            "timestamp": end_time.isoformat(),
            "test_duration": test_duration,
            "overall_success": overall_success,
            "suite_results": suite_results,
            "detailed_results": self.test_results,
            "summary": {
                "total_suites": len(suite_results),
                "successful_suites": sum(1 for r in suite_results.values() if r.get("success", False)),
                "success_rate": sum(1 for r in suite_results.values() if r.get("success", False)) / len(suite_results) if suite_results else 0
            }
        }
        
        # Save report
        report_path = Path("focused_ai_test_report.json")
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2, default=str)
        
        self.logger.info(f"\n📋 Focused test report saved to {report_path}")
        self.logger.info(f"⏱️ Total test duration: {test_duration:.1f} seconds")
        self.logger.info(f"📊 Overall success rate: {report['summary']['success_rate']:.1%}")
        
        if overall_success:
            self.logger.info("🎉 All focused AI tests completed successfully!")
        else:
            self.logger.warning("⚠️ Some tests failed. Check the detailed report for issues.")

async def main():
    """Main test execution"""
    tester = FocusedAITester()
    
    try:
        success = await tester.run_focused_tests()
        return 0 if success else 1
    except KeyboardInterrupt:
        tester.logger.info("\n⏹️ Testing interrupted by user")
        return 1
    except Exception as e:
        tester.logger.error(f"\n💥 Testing failed with error: {e}")
        return 1

if __name__ == "__main__":
    exit_code = asyncio.run(main())
    exit(exit_code)