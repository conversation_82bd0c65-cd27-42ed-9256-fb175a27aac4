#!/usr/bin/env python3
"""
Intelligence Operations Demo for Noryon V2
Demonstrates advanced AI capabilities for intelligence analysis
"""

import asyncio
import logging
import json
from datetime import datetime
from src.services.intelligence_ops import IntelligenceOperations
from src.services.ai_service import AIService

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class IntelligenceDemo:
    """Demonstration of intelligence operations capabilities"""
    
    def __init__(self):
        self.intel_ops = IntelligenceOperations()
        self.ai_service = AIService()
        self.demo_results = []
    
    async def demo_basic_intelligence_analysis(self):
        """Demo basic intelligence analysis capabilities"""
        logger.info("🔍 Demo: Basic Intelligence Analysis")
        
        threat_data = """
        Market volatility indicators showing unusual patterns:
        - VIX spike to 35+ levels
        - Unusual options flow in financial sector
        - Increased correlation between crypto and traditional markets
        - Geopolitical tensions affecting energy markets
        - Central bank policy divergence creating currency instability
        """
        
        result = await self.intel_ops.conduct_threat_analysis(threat_data, "classified")
        self.demo_results.append({"demo": "basic_intelligence", "result": result})
        
        print("\n" + "="*80)
        print("🔍 BASIC INTELLIGENCE ANALYSIS RESULTS")
        print("="*80)
        print(f"Threat Level: {result['threat_level']}")
        print(f"Classification: {result['classification']}")
        print("\nKey Recommendations:")
        for i, rec in enumerate(result['recommendations'][:3], 1):
            print(f"{i}. {rec}")
        print("\nAnalysis Preview:")
        print(result['analysis'][:300] + "...")
    
    async def demo_uncensored_research(self):
        """Demo uncensored research capabilities"""
        logger.info("🕵️ Demo: Uncensored Research")
        
        query = """
        Analyze unconventional trading strategies that exploit:
        1. Regulatory arbitrage opportunities between jurisdictions
        2. Market microstructure inefficiencies
        3. Information asymmetries in emerging markets
        4. Cross-asset correlation breakdowns
        5. Alternative data sources for alpha generation
        
        Include strategies that traditional analysis might overlook.
        """
        
        result = await self.ai_service.uncensored_research(query, depth="deep")
        self.demo_results.append({"demo": "uncensored_research", "result": result})
        
        print("\n" + "="*80)
        print("🕵️ UNCENSORED RESEARCH RESULTS")
        print("="*80)
        print("Research Query: Unconventional Trading Strategies")
        print("Security Clearance: TOP SECRET")
        print("\nResearch Results Preview:")
        print(result[:400] + "...")
    
    async def demo_cognitive_reasoning(self):
        """Demo advanced cognitive reasoning"""
        logger.info("🧠 Demo: Cognitive Reasoning")
        
        problem = """
        Complex multi-factor scenario:
        
        A major central bank is considering unprecedented monetary policy changes
        while geopolitical tensions escalate, cryptocurrency adoption accelerates,
        and traditional financial institutions face regulatory pressure.
        
        Simultaneously, AI and automation are disrupting labor markets,
        climate change is affecting commodity prices, and social unrest
        is increasing in key economic regions.
        
        How should a sophisticated trading system position itself to:
        1. Protect against systemic risks
        2. Capitalize on emerging opportunities
        3. Maintain operational resilience
        4. Adapt to rapid market evolution
        """
        
        result = await self.ai_service.cognitive_reasoning(problem, complexity="ultra")
        self.demo_results.append({"demo": "cognitive_reasoning", "result": result})
        
        print("\n" + "="*80)
        print("🧠 COGNITIVE REASONING RESULTS")
        print("="*80)
        print("Problem Complexity: ULTRA")
        print("AI Model: Cognitive Analyst (Classified)")
        print("\nReasoning Results Preview:")
        print(result[:400] + "...")
    
    async def demo_market_intelligence(self):
        """Demo deep market intelligence"""
        logger.info("📊 Demo: Market Intelligence")
        
        market_data = """
        Current market conditions:
        - S&P 500 at resistance levels with declining volume
        - Bond yields inverting across multiple maturities
        - Dollar strength pressuring emerging markets
        - Commodity supercycle indicators mixed
        - Tech sector showing rotation patterns
        - Energy sector benefiting from geopolitical premiums
        """
        
        competitors = [
            "Renaissance Technologies",
            "Citadel Securities",
            "Two Sigma",
            "DE Shaw",
            "Bridgewater Associates"
        ]
        
        result = await self.intel_ops.deep_market_intelligence(market_data, competitors)
        self.demo_results.append({"demo": "market_intelligence", "result": result})
        
        print("\n" + "="*80)
        print("📊 MARKET INTELLIGENCE RESULTS")
        print("="*80)
        print(f"Competitors Analyzed: {len(competitors)}")
        print("Security Level: TOP SECRET (Uncensored)")
        print("\nCompetitive Insights:")
        for i, insight in enumerate(result['competitive_insights'][:3], 1):
            print(f"{i}. {insight}")
        print("\nIdentified Opportunities:")
        for i, opp in enumerate(result['opportunities'][:3], 1):
            print(f"{i}. {opp}")
    
    async def demo_vision_analysis(self):
        """Demo vision analysis capabilities"""
        logger.info("👁️ Demo: Vision Analysis")
        
        image_description = """
        Financial chart showing:
        - Unusual candlestick patterns in major currency pairs
        - Volume spikes coinciding with news events
        - Technical indicator divergences
        - Support/resistance level breaks
        - Cross-market correlation patterns
        """
        
        context = "Real-time trading floor analysis for pattern recognition"
        
        result = await self.intel_ops.visual_intelligence_analysis(image_description, context)
        self.demo_results.append({"demo": "vision_analysis", "result": result})
        
        print("\n" + "="*80)
        print("👁️ VISION ANALYSIS RESULTS")
        print("="*80)
        print(f"Analysis Type: Intelligence")
        print(f"Security Assessment: {result['security_assessment']}")
        print("\nKey Findings:")
        for i, finding in enumerate(result['key_findings'][:3], 1):
            print(f"{i}. {finding}")
    
    async def demo_comprehensive_report(self):
        """Demo comprehensive intelligence report"""
        logger.info("📋 Demo: Comprehensive Intelligence Report")
        
        target = "Emerging Market Currency Manipulation"
        
        result = await self.intel_ops.comprehensive_intelligence_report(target, scope="strategic")
        self.demo_results.append({"demo": "comprehensive_report", "result": result})
        
        print("\n" + "="*80)
        print("📋 COMPREHENSIVE INTELLIGENCE REPORT")
        print("="*80)
        print(f"Target: {result['target']}")
        print(f"Classification: {result['classification']}")
        print(f"Confidence Level: {result['confidence_level']}")
        print("\nExecutive Summary Preview:")
        print(result['executive_summary'][:300] + "...")
        print("\nNext Steps:")
        for i, step in enumerate(result['next_steps'][:3], 1):
            print(f"{i}. {step}")
    
    async def demo_model_capabilities(self):
        """Demo different AI model capabilities"""
        logger.info("🤖 Demo: AI Model Capabilities")
        
        models_demo = {
            "Ultra Premium Models": [
                "command-r:35b - Advanced command and control analysis",
                "exaone-deep:32b - Deep strategic reasoning",
                "qwen2.5vl:32b - Multimodal vision analysis",
                "cogito:32b - Cognitive reasoning tasks"
            ],
            "Uncensored Models": [
                "huihui_ai/am-thinking-abliterate:latest - Unrestricted analysis",
                "huihui_ai/magistral-abliterated:24b - Uncensored strategic thinking",
                "huihui_ai/homunculus-abliterated:latest - Uncensored research",
                "huihui_ai/acereason-nemotron-abliterated:14b - Specialized reasoning"
            ],
            "Specialized Models": [
                "phi4-reasoning:plus - Advanced reasoning engine",
                "deepseek-r1:latest - Deep research capabilities",
                "magistral:24b - Premium strategic analysis"
            ]
        }
        
        print("\n" + "="*80)
        print("🤖 AI MODEL CAPABILITIES OVERVIEW")
        print("="*80)
        
        for category, models in models_demo.items():
            print(f"\n{category}:")
            for model in models:
                print(f"  • {model}")
        
        # Test a few models with simple queries
        test_query = "Analyze current market sentiment and provide strategic insights."
        
        print("\n" + "-"*60)
        print("MODEL RESPONSE SAMPLES")
        print("-"*60)
        
        # Test premium model
        try:
            response = await self.ai_service.generate_response(
                "intelligence_analyst", 
                test_query, 
                security_clearance="classified"
            )
            print(f"\n🔹 Intelligence Analyst (command-r:35b):")
            print(response[:200] + "...")
        except Exception as e:
            print(f"\n🔹 Intelligence Analyst: Model not available ({e})")
        
        # Test uncensored model
        try:
            response = await self.ai_service.generate_response(
                "uncensored_analyst", 
                test_query, 
                security_clearance="top_secret"
            )
            print(f"\n🔹 Uncensored Analyst (abliterated model):")
            print(response[:200] + "...")
        except Exception as e:
            print(f"\n🔹 Uncensored Analyst: Model not available ({e})")
    
    async def generate_demo_report(self):
        """Generate comprehensive demo report"""
        logger.info("📊 Generating Demo Report")
        
        # Get operation summary
        ops_summary = self.intel_ops.get_operation_summary()
        
        report = {
            "demo_timestamp": datetime.utcnow().isoformat(),
            "total_demonstrations": len(self.demo_results),
            "operations_summary": ops_summary,
            "capabilities_tested": [
                "Basic Intelligence Analysis",
                "Uncensored Research",
                "Cognitive Reasoning",
                "Market Intelligence",
                "Vision Analysis",
                "Comprehensive Reporting",
                "Multi-Model Integration"
            ],
            "security_levels_demonstrated": [
                "PUBLIC",
                "RESTRICTED", 
                "CLASSIFIED",
                "TOP SECRET"
            ],
            "ai_models_utilized": [
                "command-r:35b",
                "cogito:32b",
                "qwen2.5vl:32b",
                "exaone-deep:32b",
                "phi4-reasoning:plus",
                "huihui_ai/am-thinking-abliterate:latest",
                "huihui_ai/magistral-abliterated:24b",
                "huihui_ai/homunculus-abliterated:latest"
            ],
            "demo_results": self.demo_results
        }
        
        # Save report
        with open("intelligence_demo_report.json", "w") as f:
            json.dump(report, f, indent=2, default=str)
        
        print("\n" + "="*80)
        print("📊 INTELLIGENCE OPERATIONS DEMO COMPLETE")
        print("="*80)
        print(f"Total Demonstrations: {report['total_demonstrations']}")
        print(f"Operations Conducted: {ops_summary['total_operations']}")
        print(f"Success Rate: {ops_summary['success_rate']}")
        print(f"\nCapabilities Demonstrated: {len(report['capabilities_tested'])}")
        print(f"Security Levels: {len(report['security_levels_demonstrated'])}")
        print(f"AI Models Utilized: {len(report['ai_models_utilized'])}")
        print("\n📋 Detailed report saved to: intelligence_demo_report.json")
        
        return report
    
    async def run_full_demo(self):
        """Run complete intelligence operations demo"""
        print("\n" + "="*80)
        print("🚀 NORYON V2 INTELLIGENCE OPERATIONS DEMO")
        print("="*80)
        print("Demonstrating advanced AI capabilities for intelligence analysis")
        print("Including uncensored models for sensitive intelligence work")
        print("="*80)
        
        try:
            # Run all demonstrations
            await self.demo_basic_intelligence_analysis()
            await self.demo_uncensored_research()
            await self.demo_cognitive_reasoning()
            await self.demo_market_intelligence()
            await self.demo_vision_analysis()
            await self.demo_comprehensive_report()
            await self.demo_model_capabilities()
            
            # Generate final report
            await self.generate_demo_report()
            
        except Exception as e:
            logger.error(f"Demo error: {e}")
            print(f"\n❌ Demo encountered an error: {e}")
            print("This may be due to model availability or configuration issues.")

async def main():
    """Main demo execution"""
    demo = IntelligenceDemo()
    await demo.run_full_demo()

if __name__ == "__main__":
    asyncio.run(main())