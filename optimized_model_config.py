#!/usr/bin/env python3
"""
Optimized Model Configuration for Noryon V2
Based on real validation results - only proven working models
"""

from typing import Dict, List, Set
import json
from datetime import datetime

class OptimizedModelConfig:
    """Optimized configuration based on validation results"""
    
    def __init__(self):
        # PROVEN WORKING MODELS (from validation)
        self.working_models = {
            # Fast and reliable models (< 5 seconds response time)
            'fast_tier': [
                'nemotron-mini:4b',           # 3.07s - excellent for quick responses
                'granite3.3:8b',              # 4.95s - reliable mid-size model
                'hermes3:8b',                 # 4.7s - good general purpose
                'marco-o1:7b',                # 4.7s - reasoning capabilities
                'deepseek-r1:latest'          # 5.2s - latest reasoning model
            ],
            
            # Standard tier models (5-10 seconds)
            'standard_tier': [
                'falcon3:10b',                # 6.3s - solid performance
                'mistral-small:24b',          # Good balance of size/speed
                'cogito:32b'                  # Reliable large model
            ],
            
            # Advanced tier models (10-15 seconds) - for complex tasks
            'advanced_tier': [
                'gemma3:27b',                 # Proven reliable
                'huihui_ai/acereason-nemotron-abliterated:14b',  # Reasoning specialist
                'goekdenizguelmez/JOSIEFIED-Qwen3:14b'           # Specialized model
            ],
            
            # Premium tier models (15+ seconds) - for highest quality
            'premium_tier': [
                'huihui_ai/homunculus-abliterated:latest',  # 16.86s - uncensored
                'command-r:35b',             # Large context, high quality
                'qwen2.5vl:32b'              # Vision + language capabilities
            ]
        }
        
        # PROBLEMATIC MODELS (timed out or too slow) - REMOVED
        self.removed_models = [
            'magistral:24b',                              # Timed out
            'huihui_ai/magistral-abliterated:24b',       # Timed out
            'exaone-deep:32b',                           # Timed out
            'qwen3:32b',                                 # Timed out
            'huihui_ai/am-thinking-abliterate:latest',   # Timed out
            'phi4-reasoning:plus'                        # Timed out
        ]
        
    def get_optimized_agent_assignments(self) -> Dict[str, str]:
        """Assign proven working models to agents based on performance"""
        return {
            # Core Trading Agents - Fast response required
            'market_watcher': 'nemotron-mini:4b',           # Fastest model for real-time monitoring
            'order_manager': 'granite3.3:8b',              # Reliable for order execution
            'portfolio_tracker': 'hermes3:8b',             # Good for portfolio calculations
            'risk_monitor': 'deepseek-r1:latest',          # Latest reasoning for risk assessment
            
            # Strategy Agents - Balance speed and quality
            'strategy_researcher': 'falcon3:10b',          # Good research capabilities
            'technical_analyst': 'gemma3:27b',             # Proven technical analysis
            'fundamental_analyst': 'mistral-small:24b',    # Strong analytical capabilities
            'sentiment_analyzer': 'cogito:32b',            # Good for sentiment analysis
            
            # Risk Management - Reliability critical
            'risk_officer': 'granite3.3:8b',              # Proven reliable
            'compliance_monitor': 'hermes3:8b',            # Good for rule checking
            'position_sizer': 'marco-o1:7b',              # Reasoning for position sizing
            
            # Advanced Intelligence - Quality over speed
            'intelligence_analyst': 'command-r:35b',       # Large context for intelligence
            'threat_assessor': 'huihui_ai/acereason-nemotron-abliterated:14b',  # Specialized reasoning
            'strategic_planner': 'gemma3:27b',             # Strategic thinking
            'visual_analyst': 'qwen2.5vl:32b',            # Vision capabilities
            
            # Uncensored Operations - Specialized models
            'uncensored_analyst': 'huihui_ai/homunculus-abliterated:latest',  # Uncensored analysis
            'deep_researcher': 'command-r:35b',           # Deep research capabilities
            'alternative_intel': 'goekdenizguelmez/JOSIEFIED-Qwen3:14b',  # Alternative perspectives
            
            # Specialized Agents
            'data_processor': 'nemotron-mini:4b',         # Fast data processing
            'report_generator': 'mistral-small:24b',      # Good for report writing
            'alert_manager': 'granite3.3:8b',            # Reliable for alerts
            'system_coordinator': 'hermes3:8b',          # System coordination
            'performance_analyzer': 'marco-o1:7b',       # Performance analysis
            'market_predictor': 'cogito:32b',             # Market predictions
            'news_analyzer': 'falcon3:10b'               # News analysis
        }
    
    def get_performance_tiers(self) -> Dict[str, List[str]]:
        """Performance-based model tiers"""
        return {
            'ultra_fast': ['nemotron-mini:4b'],                    # < 4 seconds
            'fast': ['granite3.3:8b', 'hermes3:8b', 'marco-o1:7b', 'deepseek-r1:latest'],  # 4-6 seconds
            'standard': ['falcon3:10b', 'mistral-small:24b', 'cogito:32b'],  # 6-10 seconds
            'advanced': ['gemma3:27b', 'huihui_ai/acereason-nemotron-abliterated:14b', 'goekdenizguelmez/JOSIEFIED-Qwen3:14b'],  # 10-15 seconds
            'premium': ['huihui_ai/homunculus-abliterated:latest', 'command-r:35b', 'qwen2.5vl:32b']  # 15+ seconds
        }
    
    def get_security_levels(self) -> Dict[str, List[str]]:
        """Security clearance based model access"""
        return {
            'public': [
                'nemotron-mini:4b',
                'granite3.3:8b', 
                'hermes3:8b',
                'marco-o1:7b',
                'falcon3:10b'
            ],
            'restricted': [
                'mistral-small:24b',
                'cogito:32b',
                'gemma3:27b',
                'deepseek-r1:latest'
            ],
            'classified': [
                'huihui_ai/acereason-nemotron-abliterated:14b',
                'goekdenizguelmez/JOSIEFIED-Qwen3:14b',
                'qwen2.5vl:32b',
                'command-r:35b'
            ],
            'top_secret': [
                'huihui_ai/homunculus-abliterated:latest'
            ]
        }
    
    def get_use_case_assignments(self) -> Dict[str, str]:
        """Specific use case to model assignments"""
        return {
            # Real-time operations
            'real_time_monitoring': 'nemotron-mini:4b',
            'quick_alerts': 'granite3.3:8b',
            'fast_calculations': 'hermes3:8b',
            
            # Analysis tasks
            'technical_analysis': 'gemma3:27b',
            'fundamental_analysis': 'mistral-small:24b',
            'sentiment_analysis': 'cogito:32b',
            'risk_analysis': 'deepseek-r1:latest',
            
            # Research and intelligence
            'market_research': 'falcon3:10b',
            'strategic_research': 'command-r:35b',
            'threat_analysis': 'huihui_ai/acereason-nemotron-abliterated:14b',
            'visual_analysis': 'qwen2.5vl:32b',
            
            # Specialized operations
            'uncensored_analysis': 'huihui_ai/homunculus-abliterated:latest',
            'reasoning_tasks': 'marco-o1:7b',
            'alternative_perspectives': 'goekdenizguelmez/JOSIEFIED-Qwen3:14b'
        }
    
    def generate_updated_ai_service_config(self) -> str:
        """Generate updated AI service configuration code"""
        agent_assignments = self.get_optimized_agent_assignments()
        performance_tiers = self.get_performance_tiers()
        security_levels = self.get_security_levels()
        
        config_code = f'''
# OPTIMIZED AI SERVICE CONFIGURATION
# Generated on {datetime.utcnow().isoformat()}
# Based on real validation results - 70% model functionality rate achieved

class OptimizedAIService:
    def __init__(self):
        # PROVEN WORKING MODELS ONLY
        self.models = {{
'''
        
        for agent, model in agent_assignments.items():
            config_code += f'            "{agent}": "{model}",\n'
        
        config_code += '''        }
        
        # PERFORMANCE-BASED TIERS
        self.model_tiers = {
'''
        
        for tier, models in performance_tiers.items():
            config_code += f'            "{tier}": {models},\n'
        
        config_code += '''        }
        
        # SECURITY CLEARANCE LEVELS
        self.security_levels = {
'''
        
        for level, models in security_levels.items():
            config_code += f'            "{level}": {models},\n'
        
        config_code += '''        }
        
        # REMOVED PROBLEMATIC MODELS
        self.removed_models = [
'''
        
        for model in self.removed_models:
            config_code += f'            "{model}",  # Timed out or too slow\n'
        
        config_code += '''        ]
        
        # PERFORMANCE METRICS (from validation)
        self.performance_stats = {
            "model_availability_rate": 100.0,
            "model_functionality_rate": 70.0,
            "agent_success_rate": 100.0,
            "average_response_time_seconds": 11.29,
            "working_models": 14,
            "total_models": 20
        }
'''
        
        return config_code
    
    def save_optimized_config(self, filename: str = 'optimized_ai_config.py'):
        """Save the optimized configuration to file"""
        config_code = self.generate_updated_ai_service_config()
        
        with open(filename, 'w') as f:
            f.write(config_code)
        
        print(f"✅ Optimized configuration saved to {filename}")
        return filename
    
    def get_validation_summary(self) -> Dict[str, any]:
        """Get summary of validation results and optimizations"""
        all_working_models = []
        for tier_models in self.working_models.values():
            all_working_models.extend(tier_models)
        
        return {
            'total_working_models': len(all_working_models),
            'total_removed_models': len(self.removed_models),
            'working_models_by_tier': {tier: len(models) for tier, models in self.working_models.items()},
            'agent_assignments': len(self.get_optimized_agent_assignments()),
            'security_levels': len(self.get_security_levels()),
            'performance_improvement': 'Removed 6 problematic models, optimized for 70% functionality rate',
            'system_status': 'OPTIMIZED - Real tested and proven models only'
        }

def main():
    """Generate optimized configuration"""
    optimizer = OptimizedModelConfig()
    
    # Save optimized configuration
    config_file = optimizer.save_optimized_config()
    
    # Print summary
    summary = optimizer.get_validation_summary()
    
    print("\n" + "="*60)
    print("🚀 NORYON V2 OPTIMIZED CONFIGURATION")
    print("="*60)
    print(f"✅ Working Models: {summary['total_working_models']}")
    print(f"❌ Removed Models: {summary['total_removed_models']}")
    print(f"🤖 Agent Assignments: {summary['agent_assignments']}")
    print(f"🔒 Security Levels: {summary['security_levels']}")
    print(f"📊 Status: {summary['system_status']}")
    
    print("\n📈 WORKING MODELS BY PERFORMANCE TIER:")
    for tier, count in summary['working_models_by_tier'].items():
        print(f"  {tier.upper()}: {count} models")
    
    print("\n🎯 OPTIMIZATION RESULTS:")
    print(f"  • {summary['performance_improvement']}")
    print(f"  • All agents have 100% success rate")
    print(f"  • Average response time: 11.29 seconds")
    print(f"  • System health: GOOD (70% functionality rate)")
    
    print(f"\n📄 Configuration saved to: {config_file}")
    print("="*60)
    
    return summary

if __name__ == "__main__":
    main()