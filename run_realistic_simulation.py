#!/usr/bin/env python3
"""
Noryon V2 - Realistic Trading Simulation Runner
Main entry point for running the complete realistic trading simulation

This script provides:
- Complete system initialization
- Realistic trading environment setup
- AI agent coordination
- Real-time web dashboard
- Comprehensive logging and monitoring
- Professional-grade simulation management

Usage:
    python run_realistic_simulation.py [options]

Options:
    --config-file: Path to configuration file
    --dashboard-port: Port for web dashboard (default: 8080)
    --simulation-duration: Duration in hours (default: unlimited)
    --initial-capital: Initial capital per agent (default: 100000)
    --symbols: Trading symbols (default: BTC/USDT,ETH/USDT,ADA/USDT)
    --agents: Number of agents per type (default: 2)
    --risk-limit: Maximum portfolio risk (default: 0.02)
    --verbose: Enable verbose logging
"""

import asyncio
import argparse
import signal
import sys
import os
import logging
from datetime import datetime, timezone
from typing import Optional
from decimal import Decimal

# Add project root to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.core.config import Config
from src.core.logger import setup_logging, get_logger
from src.db.database_manager import DatabaseManager
from src.services.realistic_trading_simulator import RealisticTradingSimulator
from src.web.dashboard import TradingDashboard, create_dashboard_template

class RealisticSimulationRunner:
    """
    Main runner for the realistic trading simulation
    """
    
    def __init__(self, args):
        self.args = args
        self.config = None
        self.simulator = None
        self.dashboard = None
        self.logger = None
        self.running = False
        
        # Graceful shutdown handling
        signal.signal(signal.SIGINT, self._signal_handler)
        signal.signal(signal.SIGTERM, self._signal_handler)
    
    def _signal_handler(self, signum, frame):
        """Handle shutdown signals gracefully"""
        if self.logger:
            self.logger.info(f"Received signal {signum}, initiating graceful shutdown...")
        self.running = False
    
    async def initialize(self):
        """Initialize the complete simulation system"""
        try:
            print("🚀 Initializing Noryon V2 Realistic Trading Simulation...")
            
            # Setup logging
            setup_logging(
                level=logging.DEBUG if self.args.verbose else logging.INFO,
                log_file=f"logs/simulation_{datetime.now().strftime('%Y%m%d_%H%M%S')}.log"
            )
            self.logger = get_logger(__name__)
            
            self.logger.info("" + "="*80)
            self.logger.info("🎯 NORYON V2 REALISTIC TRADING SIMULATION STARTUP")
            self.logger.info("" + "="*80)
            
            # Load configuration
            await self._load_configuration()
            
            # Override config with command line arguments
            await self._apply_cli_overrides()
            
            # Create dashboard template
            create_dashboard_template()
            
            # Initialize core components
            self.logger.info("🔧 Initializing core components...")
            
            # Initialize simulator
            self.simulator = RealisticTradingSimulator(self.config)
            await self.simulator.initialize()
            
            # Initialize dashboard
            self.dashboard = TradingDashboard(self.config, self.simulator)
            
            self.logger.info("✅ All components initialized successfully")
            
            # Log configuration summary
            await self._log_configuration_summary()
            
        except Exception as e:
            if self.logger:
                self.logger.error(f"❌ Failed to initialize simulation: {e}")
            else:
                print(f"❌ Failed to initialize simulation: {e}")
            raise
    
    async def _load_configuration(self):
        """Load system configuration"""
        try:
            if self.args.config_file and os.path.exists(self.args.config_file):
                self.logger.info(f"📄 Loading configuration from: {self.args.config_file}")
                self.config = Config.from_file(self.args.config_file)
            else:
                self.logger.info("📄 Using default configuration")
                self.config = Config()
            
            # Ensure required directories exist
            os.makedirs("logs", exist_ok=True)
            os.makedirs("simulation_results", exist_ok=True)
            os.makedirs("src/web/templates", exist_ok=True)
            
        except Exception as e:
            raise Exception(f"Failed to load configuration: {e}")
    
    async def _apply_cli_overrides(self):
        """Apply command line argument overrides to configuration"""
        try:
            # Dashboard port
            if hasattr(self.args, 'dashboard_port') and self.args.dashboard_port:
                self.config.WEB_DASHBOARD_PORT = self.args.dashboard_port
            
            # Initial capital
            if hasattr(self.args, 'initial_capital') and self.args.initial_capital:
                self.config.TRADING_INITIAL_CAPITAL = Decimal(str(self.args.initial_capital))
            
            # Trading symbols
            if hasattr(self.args, 'symbols') and self.args.symbols:
                self.config.TRADING_SYMBOLS = [s.strip() for s in self.args.symbols.split(',')]
            
            # Risk limit
            if hasattr(self.args, 'risk_limit') and self.args.risk_limit:
                self.config.RISK_MAX_PORTFOLIO_RISK = float(self.args.risk_limit)
            
            # Number of agents
            if hasattr(self.args, 'agents') and self.args.agents:
                self.config.AGENTS_PER_TYPE = int(self.args.agents)
            
            self.logger.info("⚙️ Applied command line configuration overrides")
            
        except Exception as e:
            raise Exception(f"Failed to apply CLI overrides: {e}")
    
    async def _log_configuration_summary(self):
        """Log comprehensive configuration summary"""
        self.logger.info("" + "-"*60)
        self.logger.info("📋 SIMULATION CONFIGURATION SUMMARY")
        self.logger.info("" + "-"*60)
        
        # Trading configuration
        self.logger.info("💹 Trading Configuration:")
        self.logger.info(f"   • Symbols: {getattr(self.config, 'TRADING_SYMBOLS', ['BTC/USDT', 'ETH/USDT', 'ADA/USDT'])}")
        self.logger.info(f"   • Initial Capital: ${getattr(self.config, 'TRADING_INITIAL_CAPITAL', 100000):,}")
        self.logger.info(f"   • Commission Rate: {getattr(self.config, 'TRADING_COMMISSION_RATE', 0.001)*100:.3f}%")
        self.logger.info(f"   • Max Portfolio Risk: {getattr(self.config, 'RISK_MAX_PORTFOLIO_RISK', 0.02)*100:.1f}%")
        
        # Agent configuration
        agents_per_type = getattr(self.config, 'AGENTS_PER_TYPE', 2)
        total_agents = agents_per_type * 8  # 8 agent types
        self.logger.info("🤖 Agent Configuration:")
        self.logger.info(f"   • Agents per Type: {agents_per_type}")
        self.logger.info(f"   • Total Agents: {total_agents}")
        self.logger.info(f"   • Total Initial Capital: ${getattr(self.config, 'TRADING_INITIAL_CAPITAL', 100000) * total_agents:,}")
        
        # System configuration
        self.logger.info("🖥️ System Configuration:")
        self.logger.info(f"   • Dashboard Port: {getattr(self.config, 'WEB_DASHBOARD_PORT', 8080)}")
        self.logger.info(f"   • Simulation Duration: {'Unlimited' if not self.args.simulation_duration else f'{self.args.simulation_duration} hours'}")
        self.logger.info(f"   • Verbose Logging: {'Enabled' if self.args.verbose else 'Disabled'}")
        
        # Database configuration
        self.logger.info("🗄️ Database Configuration:")
        self.logger.info(f"   • PostgreSQL: {getattr(self.config, 'POSTGRES_HOST', 'localhost')}:{getattr(self.config, 'POSTGRES_PORT', 5432)}")
        self.logger.info(f"   • Redis: {getattr(self.config, 'REDIS_HOST', 'localhost')}:{getattr(self.config, 'REDIS_PORT', 6379)}")
        self.logger.info(f"   • ClickHouse: {getattr(self.config, 'CLICKHOUSE_HOST', 'localhost')}:{getattr(self.config, 'CLICKHOUSE_PORT', 8123)}")
        
        self.logger.info("" + "-"*60)
    
    async def run(self):
        """Run the complete simulation system"""
        try:
            self.running = True
            
            self.logger.info("🎬 Starting Noryon V2 Realistic Trading Simulation...")
            
            # Start the trading simulation
            await self.simulator.start()
            
            # Start the web dashboard in background
            dashboard_port = getattr(self.config, 'WEB_DASHBOARD_PORT', 8080)
            dashboard_task = asyncio.create_task(
                self.dashboard.start_server(host="0.0.0.0", port=dashboard_port)
            )
            
            self.logger.info("" + "="*80)
            self.logger.info("🎯 NORYON V2 SIMULATION IS NOW FULLY OPERATIONAL!")
            self.logger.info("" + "="*80)
            self.logger.info(f"🌐 Web Dashboard: http://localhost:{dashboard_port}")
            self.logger.info("📊 Real-time monitoring and controls available")
            self.logger.info("🤖 AI agents are actively trading")
            self.logger.info("📈 Market data is streaming")
            self.logger.info("💹 Orders are being executed")
            self.logger.info("")
            self.logger.info("Press Ctrl+C to stop the simulation gracefully")
            self.logger.info("" + "="*80)
            
            # Run simulation for specified duration or indefinitely
            if self.args.simulation_duration:
                self.logger.info(f"⏱️ Simulation will run for {self.args.simulation_duration} hours")
                await asyncio.sleep(self.args.simulation_duration * 3600)
                self.logger.info("⏰ Simulation duration completed")
                self.running = False
            else:
                # Run indefinitely until interrupted
                while self.running:
                    await asyncio.sleep(1)
            
        except KeyboardInterrupt:
            self.logger.info("⌨️ Keyboard interrupt received")
        except Exception as e:
            self.logger.error(f"❌ Simulation error: {e}")
            raise
        finally:
            await self.shutdown()
    
    async def shutdown(self):
        """Gracefully shutdown the simulation"""
        try:
            self.logger.info("🛑 Initiating graceful shutdown...")
            
            # Stop the trading simulation
            if self.simulator:
                await self.simulator.stop()
            
            # Dashboard will be stopped automatically when the server task is cancelled
            
            self.logger.info("✅ Shutdown completed successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Error during shutdown: {e}")

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Noryon V2 Realistic Trading Simulation",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run with default settings
  python run_realistic_simulation.py
  
  # Run with custom configuration
  python run_realistic_simulation.py --config-file config/production.yaml
  
  # Run with specific parameters
  python run_realistic_simulation.py --initial-capital 50000 --symbols "BTC/USDT,ETH/USDT" --agents 3
  
  # Run for specific duration with verbose logging
  python run_realistic_simulation.py --simulation-duration 2 --verbose
  
  # Run with custom dashboard port
  python run_realistic_simulation.py --dashboard-port 9090
"""
    )
    
    parser.add_argument(
        '--config-file',
        type=str,
        help='Path to configuration file (YAML or JSON)'
    )
    
    parser.add_argument(
        '--dashboard-port',
        type=int,
        default=8080,
        help='Port for web dashboard (default: 8080)'
    )
    
    parser.add_argument(
        '--simulation-duration',
        type=float,
        help='Duration in hours (default: unlimited)'
    )
    
    parser.add_argument(
        '--initial-capital',
        type=float,
        default=100000,
        help='Initial capital per agent in USD (default: 100000)'
    )
    
    parser.add_argument(
        '--symbols',
        type=str,
        default='BTC/USDT,ETH/USDT,ADA/USDT,DOT/USDT,LINK/USDT',
        help='Trading symbols, comma-separated (default: BTC/USDT,ETH/USDT,ADA/USDT,DOT/USDT,LINK/USDT)'
    )
    
    parser.add_argument(
        '--agents',
        type=int,
        default=2,
        help='Number of agents per type (default: 2)'
    )
    
    parser.add_argument(
        '--risk-limit',
        type=float,
        default=0.02,
        help='Maximum portfolio risk as decimal (default: 0.02 = 2%%)'
    )
    
    parser.add_argument(
        '--verbose',
        action='store_true',
        help='Enable verbose logging'
    )
    
    return parser.parse_args()

async def main():
    """Main entry point"""
    try:
        # Parse command line arguments
        args = parse_arguments()
        
        # Print startup banner
        print("" + "="*80)
        print("🎯 NORYON V2 - REALISTIC TRADING SIMULATION")
        print("" + "="*80)
        print("🚀 Professional-grade AI trading simulation environment")
        print("🤖 Multi-agent autonomous trading system")
        print("📊 Real-time monitoring and analytics")
        print("💹 Realistic market conditions and execution")
        print("🌐 Web-based dashboard and controls")
        print("" + "="*80)
        print("")
        
        # Create and run simulation
        runner = RealisticSimulationRunner(args)
        await runner.initialize()
        await runner.run()
        
    except KeyboardInterrupt:
        print("\n⌨️ Simulation interrupted by user")
    except Exception as e:
        print(f"\n❌ Fatal error: {e}")
        sys.exit(1)
    finally:
        print("\n👋 Thank you for using Noryon V2!")
        print("📊 Check the simulation_results/ directory for detailed reports")
        print("📝 Logs are available in the logs/ directory")

if __name__ == "__main__":
    # Ensure we're running with Python 3.8+
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        sys.exit(1)
    
    # Run the simulation
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        pass
    except Exception as e:
        print(f"❌ Unexpected error: {e}")
        sys.exit(1)