#!/usr/bin/env python3
"""
Simplified Realistic Trading Simulation Runner
A minimal version to test the core simulation functionality
"""

import asyncio
import argparse
import sys
import os
from datetime import datetime, timedelta
from typing import List, Dict, Any
import json
import logging

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

try:
    import uvicorn
    from fastapi import FastAPI, WebSocket, WebSocketDisconnect
    from fastapi.responses import HTMLResponse
    from fastapi.staticfiles import StaticFiles
except ImportError as e:
    print(f"Missing required packages: {e}")
    print("Please install: pip install fastapi uvicorn websockets")
    sys.exit(1)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class SimpleMarketSimulator:
    """Simplified market data simulator"""
    
    def __init__(self, symbols: List[str]):
        self.symbols = symbols
        self.prices = {symbol: 50000.0 for symbol in symbols}  # Starting prices
        self.running = False
        self.subscribers = []
        
    async def start(self):
        """Start the market simulation"""
        self.running = True
        logger.info("Market simulator started")
        
        while self.running:
            # Generate price updates
            for symbol in self.symbols:
                # Simple random walk
                import random
                change_pct = random.uniform(-0.01, 0.01)  # ±1% change
                self.prices[symbol] *= (1 + change_pct)
                
                # Create market tick
                tick = {
                    'symbol': symbol,
                    'price': round(self.prices[symbol], 2),
                    'timestamp': datetime.now().isoformat(),
                    'volume': random.uniform(1, 100)
                }
                
                # Send to subscribers
                await self._notify_subscribers(tick)
            
            await asyncio.sleep(1)  # Update every second
    
    async def stop(self):
        """Stop the market simulation"""
        self.running = False
        logger.info("Market simulator stopped")
    
    def subscribe(self, callback):
        """Subscribe to market updates"""
        self.subscribers.append(callback)
    
    async def _notify_subscribers(self, tick):
        """Notify all subscribers of market updates"""
        for callback in self.subscribers:
            try:
                await callback(tick)
            except Exception as e:
                logger.error(f"Error notifying subscriber: {e}")

class SimpleAgent:
    """Simplified trading agent"""
    
    def __init__(self, agent_id: str, agent_type: str):
        self.agent_id = agent_id
        self.agent_type = agent_type
        self.portfolio = {'cash': 10000.0, 'positions': {}}
        self.orders = []
        self.trades = []
        
    async def process_market_data(self, tick: Dict[str, Any]):
        """Process incoming market data and make trading decisions"""
        # Simple trading logic based on agent type
        symbol = tick['symbol']
        price = tick['price']
        
        if self.agent_type == 'scalper':
            # Scalping strategy - quick trades
            if len(self.orders) == 0:  # No open orders
                import random
                if random.random() < 0.1:  # 10% chance to trade
                    side = 'buy' if random.random() < 0.5 else 'sell'
                    quantity = random.uniform(0.1, 1.0)
                    
                    order = {
                        'id': f"{self.agent_id}_{len(self.orders)}",
                        'symbol': symbol,
                        'side': side,
                        'quantity': quantity,
                        'price': price,
                        'timestamp': datetime.now().isoformat(),
                        'status': 'filled'  # Simplified - assume immediate fill
                    }
                    
                    self.orders.append(order)
                    self._execute_order(order)
                    
                    logger.info(f"Agent {self.agent_id} placed {side} order for {quantity} {symbol} at {price}")
    
    def _execute_order(self, order: Dict[str, Any]):
        """Execute a trade order"""
        symbol = order['symbol']
        side = order['side']
        quantity = order['quantity']
        price = order['price']
        
        if side == 'buy':
            cost = quantity * price
            if self.portfolio['cash'] >= cost:
                self.portfolio['cash'] -= cost
                if symbol not in self.portfolio['positions']:
                    self.portfolio['positions'][symbol] = 0
                self.portfolio['positions'][symbol] += quantity
        else:  # sell
            if symbol in self.portfolio['positions'] and self.portfolio['positions'][symbol] >= quantity:
                self.portfolio['positions'][symbol] -= quantity
                self.portfolio['cash'] += quantity * price
        
        # Record trade
        trade = {
            'id': f"trade_{len(self.trades)}",
            'order_id': order['id'],
            'symbol': symbol,
            'side': side,
            'quantity': quantity,
            'price': price,
            'timestamp': order['timestamp']
        }
        self.trades.append(trade)

class SimpleSimulation:
    """Main simulation coordinator"""
    
    def __init__(self, symbols: List[str], num_agents: int = 5):
        self.symbols = symbols
        self.market_simulator = SimpleMarketSimulator(symbols)
        self.agents = []
        
        # Create agents
        agent_types = ['scalper', 'swing_trader', 'momentum', 'mean_reversion', 'arbitrage']
        for i in range(num_agents):
            agent_type = agent_types[i % len(agent_types)]
            agent = SimpleAgent(f"agent_{i}", agent_type)
            self.agents.append(agent)
        
        self.running = False
        self.stats = {
            'start_time': None,
            'total_trades': 0,
            'total_volume': 0.0
        }
    
    async def start(self):
        """Start the simulation"""
        self.running = True
        self.stats['start_time'] = datetime.now()
        
        logger.info(f"Starting simulation with {len(self.agents)} agents and symbols: {self.symbols}")
        
        # Subscribe agents to market data
        async def market_data_handler(tick):
            for agent in self.agents:
                await agent.process_market_data(tick)
            
            # Update stats
            self.stats['total_trades'] = sum(len(agent.trades) for agent in self.agents)
            self.stats['total_volume'] = sum(
                sum(trade['quantity'] for trade in agent.trades) 
                for agent in self.agents
            )
        
        self.market_simulator.subscribe(market_data_handler)
        
        # Start market simulator
        await self.market_simulator.start()
    
    async def stop(self):
        """Stop the simulation"""
        self.running = False
        await self.market_simulator.stop()
        logger.info("Simulation stopped")
    
    def get_status(self):
        """Get current simulation status"""
        return {
            'running': self.running,
            'symbols': self.symbols,
            'agents': len(self.agents),
            'stats': self.stats,
            'agent_portfolios': [
                {
                    'id': agent.agent_id,
                    'type': agent.agent_type,
                    'portfolio': agent.portfolio,
                    'orders': len(agent.orders),
                    'trades': len(agent.trades)
                }
                for agent in self.agents
            ]
        }

# Global simulation instance
simulation = None

# FastAPI app for dashboard
app = FastAPI(title="Simple Trading Simulation Dashboard")

@app.get("/")
async def dashboard():
    """Serve the dashboard HTML"""
    html_content = """
    <!DOCTYPE html>
    <html>
    <head>
        <title>Trading Simulation Dashboard</title>
        <style>
            body { font-family: Arial, sans-serif; margin: 20px; background: #f0f0f0; }
            .container { max-width: 1200px; margin: 0 auto; }
            .card { background: white; padding: 20px; margin: 10px 0; border-radius: 8px; box-shadow: 0 2px 4px rgba(0,0,0,0.1); }
            .status { font-size: 18px; font-weight: bold; }
            .running { color: green; }
            .stopped { color: red; }
            .stats { display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 10px; }
            .stat-item { text-align: center; padding: 10px; background: #f8f9fa; border-radius: 4px; }
            .agents { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 10px; }
            .agent { padding: 15px; background: #f8f9fa; border-radius: 4px; }
            button { padding: 10px 20px; margin: 5px; border: none; border-radius: 4px; cursor: pointer; }
            .start-btn { background: #28a745; color: white; }
            .stop-btn { background: #dc3545; color: white; }
        </style>
    </head>
    <body>
        <div class="container">
            <h1>🚀 Realistic Trading Simulation Dashboard</h1>
            
            <div class="card">
                <h2>Simulation Control</h2>
                <button class="start-btn" onclick="startSimulation()">Start Simulation</button>
                <button class="stop-btn" onclick="stopSimulation()">Stop Simulation</button>
            </div>
            
            <div class="card">
                <h2>Status</h2>
                <div id="status" class="status">Loading...</div>
            </div>
            
            <div class="card">
                <h2>Statistics</h2>
                <div id="stats" class="stats">Loading...</div>
            </div>
            
            <div class="card">
                <h2>Agents</h2>
                <div id="agents" class="agents">Loading...</div>
            </div>
        </div>
        
        <script>
            async function updateDashboard() {
                try {
                    const response = await fetch('/status');
                    const data = await response.json();
                    
                    // Update status
                    const statusEl = document.getElementById('status');
                    statusEl.textContent = data.running ? 'RUNNING' : 'STOPPED';
                    statusEl.className = data.running ? 'status running' : 'status stopped';
                    
                    // Update stats
                    const statsEl = document.getElementById('stats');
                    statsEl.innerHTML = `
                        <div class="stat-item">
                            <h3>Symbols</h3>
                            <p>${data.symbols.join(', ')}</p>
                        </div>
                        <div class="stat-item">
                            <h3>Agents</h3>
                            <p>${data.agents}</p>
                        </div>
                        <div class="stat-item">
                            <h3>Total Trades</h3>
                            <p>${data.stats.total_trades}</p>
                        </div>
                        <div class="stat-item">
                            <h3>Total Volume</h3>
                            <p>${data.stats.total_volume.toFixed(2)}</p>
                        </div>
                    `;
                    
                    // Update agents
                    const agentsEl = document.getElementById('agents');
                    agentsEl.innerHTML = data.agent_portfolios.map(agent => `
                        <div class="agent">
                            <h4>${agent.id} (${agent.type})</h4>
                            <p><strong>Cash:</strong> $${agent.portfolio.cash.toFixed(2)}</p>
                            <p><strong>Orders:</strong> ${agent.orders}</p>
                            <p><strong>Trades:</strong> ${agent.trades}</p>
                            <p><strong>Positions:</strong> ${JSON.stringify(agent.portfolio.positions)}</p>
                        </div>
                    `).join('');
                    
                } catch (error) {
                    console.error('Error updating dashboard:', error);
                }
            }
            
            async function startSimulation() {
                try {
                    await fetch('/start', { method: 'POST' });
                } catch (error) {
                    console.error('Error starting simulation:', error);
                }
            }
            
            async function stopSimulation() {
                try {
                    await fetch('/stop', { method: 'POST' });
                } catch (error) {
                    console.error('Error stopping simulation:', error);
                }
            }
            
            // Update dashboard every 2 seconds
            setInterval(updateDashboard, 2000);
            updateDashboard();
        </script>
    </body>
    </html>
    """
    return HTMLResponse(content=html_content)

@app.get("/status")
async def get_status():
    """Get simulation status"""
    global simulation
    if simulation:
        return simulation.get_status()
    else:
        return {
            'running': False,
            'symbols': [],
            'agents': 0,
            'stats': {'start_time': None, 'total_trades': 0, 'total_volume': 0.0},
            'agent_portfolios': []
        }

@app.post("/start")
async def start_simulation():
    """Start the simulation"""
    global simulation
    if not simulation or not simulation.running:
        symbols = ['BTCUSDT', 'ETHUSDT', 'ADAUSDT']
        simulation = SimpleSimulation(symbols, num_agents=5)
        
        # Start simulation in background
        asyncio.create_task(simulation.start())
        
        return {'message': 'Simulation started'}
    else:
        return {'message': 'Simulation already running'}

@app.post("/stop")
async def stop_simulation():
    """Stop the simulation"""
    global simulation
    if simulation and simulation.running:
        await simulation.stop()
        return {'message': 'Simulation stopped'}
    else:
        return {'message': 'Simulation not running'}

def main():
    """Main entry point"""
    parser = argparse.ArgumentParser(description='Simple Realistic Trading Simulation')
    parser.add_argument('--port', type=int, default=8000, help='Dashboard port')
    parser.add_argument('--symbols', type=str, default='BTCUSDT,ETHUSDT,ADAUSDT', 
                       help='Trading symbols (comma-separated)')
    parser.add_argument('--agents', type=int, default=5, help='Number of agents')
    
    args = parser.parse_args()
    
    print(f"🚀 Starting Simple Trading Simulation Dashboard on port {args.port}")
    print(f"📊 Symbols: {args.symbols}")
    print(f"🤖 Agents: {args.agents}")
    print(f"🌐 Dashboard: http://localhost:{args.port}")
    
    # Run the FastAPI server
    uvicorn.run(app, host="0.0.0.0", port=args.port, log_level="info")

if __name__ == "__main__":
    main()