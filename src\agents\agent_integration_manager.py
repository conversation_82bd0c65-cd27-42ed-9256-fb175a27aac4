"""Agent Integration Manager
Coordinates specialized agents and manages their interactions, collaboration, and system-wide operations.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Set, Tuple
from enum import Enum
from dataclasses import dataclass, asdict
import uuid

from .enhanced_base_agent import Enhanced<PERSON><PERSON><PERSON><PERSON>, DecisionType, Decision
from .specialized_agents import (
    AdvancedMarketWatcher, IntelligentTrader, AdaptiveRiskManager,
    create_specialized_agent, MarketCondition, TradingSignal, RiskLevel
)
from .agent_tools import AgentToolkit, initialize_toolkit
from ..services.ai_service import AIService
from ..core.config import Config
from ..db.database_manager import DatabaseManager

class SystemState(Enum):
    INITIALIZING = "initializing"
    ACTIVE = "active"
    MONITORING = "monitoring"
    EMERGENCY = "emergency"
    MAINTENANCE = "maintenance"
    SHUTDOWN = "shutdown"

class CoordinationMode(Enum):
    AUTONOMOUS = "autonomous"
    COLLABORATIVE = "collaborative"
    SUPERVISED = "supervised"
    EMERGENCY_OVERRIDE = "emergency_override"

@dataclass
class SystemMetrics:
    """System-wide performance metrics"""
    total_agents: int
    active_agents: int
    decisions_made_today: int
    successful_trades: int
    total_pnl: float
    risk_level: str
    system_uptime: float
    avg_response_time: float
    collaboration_events: int
    learning_records: int
    timestamp: datetime

@dataclass
class CoordinationTask:
    """Task requiring agent coordination"""
    task_id: str
    task_type: str
    priority: int
    required_agents: List[str]
    assigned_agents: List[str]
    status: str
    created_at: datetime
    deadline: Optional[datetime]
    context: Dict[str, Any]
    results: Dict[str, Any]

class AgentIntegrationManager:
    """Manages integration and coordination of all specialized agents"""
    
    def __init__(self, ai_service: AIService, config: Config, db_manager: DatabaseManager):
        self.ai_service = ai_service
        self.config = config
        self.db_manager = db_manager
        self.logger = logging.getLogger("AgentIntegrationManager")
        
        # System state
        self.system_state = SystemState.INITIALIZING
        self.coordination_mode = CoordinationMode.AUTONOMOUS
        self.start_time = datetime.utcnow()
        
        # Agent management
        self.agents: Dict[str, EnhancedBaseAgent] = {}
        self.agent_capabilities: Dict[str, List[str]] = {}
        self.agent_workloads: Dict[str, int] = {}
        self.agent_performance: Dict[str, Dict[str, float]] = {}
        
        # Coordination and collaboration
        self.active_tasks: Dict[str, CoordinationTask] = {}
        self.collaboration_network: Dict[str, Set[str]] = {}
        self.message_queues: Dict[str, List[Dict[str, Any]]] = {}
        
        # System monitoring
        self.system_metrics = SystemMetrics(
            total_agents=0, active_agents=0, decisions_made_today=0,
            successful_trades=0, total_pnl=0.0, risk_level="unknown",
            system_uptime=0.0, avg_response_time=0.0,
            collaboration_events=0, learning_records=0,
            timestamp=datetime.utcnow()
        )
        
        # Toolkit for system-wide operations
        self.toolkit: Optional[AgentToolkit] = None
        
        # Emergency protocols
        self.emergency_thresholds = {
            "max_daily_loss": 0.05,  # 5% max daily loss
            "max_position_risk": 0.15,  # 15% max position risk
            "min_agent_availability": 0.7,  # 70% min agent availability
            "max_response_time": 30.0  # 30 seconds max response time
        }
    
    async def initialize(self) -> bool:
        """Initialize the agent integration system"""
        try:
            self.logger.info("🚀 Initializing Agent Integration Manager...")
            
            # Initialize toolkit
            self.toolkit = initialize_toolkit(self.ai_service, self.db_manager, self.config)
            
            # Create specialized agents
            await self._create_specialized_agents()
            
            # Initialize agent capabilities mapping
            await self._map_agent_capabilities()
            
            # Set up collaboration network
            await self._setup_collaboration_network()
            
            # Start monitoring tasks
            await self._start_monitoring_tasks()
            
            self.system_state = SystemState.ACTIVE
            self.logger.info(f"✅ Agent Integration Manager initialized with {len(self.agents)} agents")
            
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Agent Integration Manager: {e}")
            self.system_state = SystemState.EMERGENCY
            return False
    
    async def _create_specialized_agents(self):
        """Create and initialize all specialized agents"""
        agent_configs = [
            {"type": "market_watcher", "name": "primary_market_watcher"},
            {"type": "market_watcher", "name": "secondary_market_watcher"},
            {"type": "intelligent_trader", "name": "primary_trader"},
            {"type": "intelligent_trader", "name": "scalping_trader"},
            {"type": "risk_manager", "name": "portfolio_risk_manager"},
            {"type": "risk_manager", "name": "trade_risk_manager"}
        ]
        
        for agent_config in agent_configs:
            try:
                agent = create_specialized_agent(
                    agent_config["type"], 
                    self.ai_service, 
                    self.config, 
                    self.db_manager
                )
                
                # Override agent name
                agent.agent_name = agent_config["name"]
                
                # Initialize agent
                await agent.initialize()
                
                # Register agent
                self.agents[agent_config["name"]] = agent
                self.agent_workloads[agent_config["name"]] = 0
                self.agent_performance[agent_config["name"]] = {
                    "success_rate": 0.0,
                    "avg_response_time": 0.0,
                    "decisions_made": 0,
                    "collaborations": 0
                }
                
                self.logger.info(f"  ✅ Created agent: {agent_config['name']} ({agent_config['type']})")
                
            except Exception as e:
                self.logger.error(f"  ❌ Failed to create agent {agent_config['name']}: {e}")
    
    async def _map_agent_capabilities(self):
        """Map agent capabilities for task assignment"""
        for agent_name, agent in self.agents.items():
            capabilities = [cap.name for cap in agent.capabilities]
            self.agent_capabilities[agent_name] = capabilities
            
            self.logger.info(f"  📋 {agent_name}: {len(capabilities)} capabilities")
    
    async def _setup_collaboration_network(self):
        """Set up agent collaboration network"""
        # Define collaboration relationships
        collaborations = {
            "primary_market_watcher": ["primary_trader", "portfolio_risk_manager"],
            "secondary_market_watcher": ["scalping_trader", "trade_risk_manager"],
            "primary_trader": ["portfolio_risk_manager", "primary_market_watcher"],
            "scalping_trader": ["trade_risk_manager", "secondary_market_watcher"],
            "portfolio_risk_manager": ["primary_trader", "primary_market_watcher"],
            "trade_risk_manager": ["scalping_trader", "secondary_market_watcher"]
        }
        
        for agent_name, collaborators in collaborations.items():
            self.collaboration_network[agent_name] = set(collaborators)
            self.message_queues[agent_name] = []
        
        self.logger.info(f"  🤝 Collaboration network established with {len(collaborations)} relationships")
    
    async def _start_monitoring_tasks(self):
        """Start background monitoring tasks"""
        # Start system monitoring
        asyncio.create_task(self._system_monitor_loop())
        
        # Start agent health monitoring
        asyncio.create_task(self._agent_health_monitor())
        
        # Start coordination task processor
        asyncio.create_task(self._coordination_task_processor())
        
        # Start emergency monitoring
        asyncio.create_task(self._emergency_monitor())
        
        self.logger.info("  📊 Background monitoring tasks started")
    
    async def coordinate_market_analysis(self, symbols: List[str]) -> Dict[str, Any]:
        """Coordinate comprehensive market analysis across multiple agents"""
        task_id = str(uuid.uuid4())
        
        # Create coordination task
        task = CoordinationTask(
            task_id=task_id,
            task_type="market_analysis",
            priority=2,
            required_agents=["primary_market_watcher", "secondary_market_watcher"],
            assigned_agents=[],
            status="pending",
            created_at=datetime.utcnow(),
            deadline=datetime.utcnow() + timedelta(minutes=5),
            context={"symbols": symbols, "analysis_type": "comprehensive"},
            results={}
        )
        
        self.active_tasks[task_id] = task
        
        # Assign agents
        assigned_agents = await self._assign_agents_to_task(task)
        
        if not assigned_agents:
            return {"success": False, "message": "No agents available for market analysis"}
        
        # Execute coordinated analysis
        analysis_results = {}
        
        for agent_name in assigned_agents:
            agent = self.agents[agent_name]
            
            try:
                # Execute market analysis
                result = await agent.execute_specialized_task({
                    "type": "analyze_symbol",
                    "symbol": symbols[0] if symbols else "BTC/USD",
                    "coordination_id": task_id
                })
                
                analysis_results[agent_name] = result
                
            except Exception as e:
                self.logger.error(f"Agent {agent_name} failed market analysis: {e}")
                analysis_results[agent_name] = {"success": False, "error": str(e)}
        
        # Synthesize results using AI
        synthesis_result = await self._synthesize_analysis_results(analysis_results, symbols)
        
        # Update task
        task.status = "completed"
        task.results = {
            "individual_analyses": analysis_results,
            "synthesized_analysis": synthesis_result,
            "agents_used": assigned_agents
        }
        
        # Record collaboration event
        self.system_metrics.collaboration_events += 1
        
        return {
            "success": True,
            "task_id": task_id,
            "agents_used": assigned_agents,
            "analysis_results": analysis_results,
            "synthesized_analysis": synthesis_result
        }
    
    async def coordinate_trading_decision(self, market_signal: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate trading decision across trader and risk manager"""
        task_id = str(uuid.uuid4())
        
        # Create coordination task
        task = CoordinationTask(
            task_id=task_id,
            task_type="trading_decision",
            priority=1,  # High priority
            required_agents=["primary_trader", "portfolio_risk_manager"],
            assigned_agents=[],
            status="pending",
            created_at=datetime.utcnow(),
            deadline=datetime.utcnow() + timedelta(minutes=2),
            context={"market_signal": market_signal},
            results={}
        )
        
        self.active_tasks[task_id] = task
        
        # Assign agents
        assigned_agents = await self._assign_agents_to_task(task)
        
        if len(assigned_agents) < 2:
            return {"success": False, "message": "Insufficient agents for trading decision"}
        
        # Get risk assessment first
        risk_manager = self.agents["portfolio_risk_manager"]
        risk_assessment = await risk_manager.execute_specialized_task({
            "type": "assess_portfolio",
            "context": market_signal
        })
        
        # If risk is too high, reject trade
        if risk_assessment.get("risk_level") in ["HIGH", "CRITICAL"]:
            task.status = "rejected"
            task.results = {"rejection_reason": "Risk level too high", "risk_assessment": risk_assessment}
            return {
                "success": False,
                "message": "Trade rejected due to high risk",
                "risk_assessment": risk_assessment
            }
        
        # Execute trading decision
        trader = self.agents["primary_trader"]
        trading_result = await trader.execute_specialized_task({
            "type": "evaluate_signals",
            "signals": [market_signal],
            "risk_assessment": risk_assessment
        })
        
        # Update task
        task.status = "completed"
        task.results = {
            "risk_assessment": risk_assessment,
            "trading_result": trading_result,
            "final_decision": trading_result.get("evaluations", [{}])[0].get("recommended_action", "hold")
        }
        
        return {
            "success": True,
            "task_id": task_id,
            "risk_assessment": risk_assessment,
            "trading_result": trading_result,
            "final_decision": task.results["final_decision"]
        }
    
    async def coordinate_emergency_response(self, emergency_type: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Coordinate emergency response across all agents"""
        self.logger.warning(f"🚨 Emergency response triggered: {emergency_type}")
        
        # Switch to emergency mode
        original_mode = self.coordination_mode
        self.coordination_mode = CoordinationMode.EMERGENCY_OVERRIDE
        self.system_state = SystemState.EMERGENCY
        
        try:
            # Create emergency task
            task_id = str(uuid.uuid4())
            task = CoordinationTask(
                task_id=task_id,
                task_type="emergency_response",
                priority=0,  # Highest priority
                required_agents=list(self.agents.keys()),
                assigned_agents=list(self.agents.keys()),
                status="executing",
                created_at=datetime.utcnow(),
                deadline=datetime.utcnow() + timedelta(minutes=1),
                context={"emergency_type": emergency_type, "context": context},
                results={}
            )
            
            self.active_tasks[task_id] = task
            
            # Execute emergency protocols
            emergency_results = {}
            
            if emergency_type == "market_crash":
                emergency_results = await self._handle_market_crash_emergency(context)
            elif emergency_type == "system_failure":
                emergency_results = await self._handle_system_failure_emergency(context)
            elif emergency_type == "risk_breach":
                emergency_results = await self._handle_risk_breach_emergency(context)
            else:
                emergency_results = await self._handle_general_emergency(emergency_type, context)
            
            # Update task
            task.status = "completed"
            task.results = emergency_results
            
            return {
                "success": True,
                "task_id": task_id,
                "emergency_type": emergency_type,
                "response_results": emergency_results,
                "response_time": (datetime.utcnow() - task.created_at).total_seconds()
            }
            
        finally:
            # Restore original mode
            self.coordination_mode = original_mode
            self.system_state = SystemState.ACTIVE
    
    async def _assign_agents_to_task(self, task: CoordinationTask) -> List[str]:
        """Assign optimal agents to a coordination task"""
        available_agents = []
        
        for agent_name in task.required_agents:
            if agent_name in self.agents:
                agent = self.agents[agent_name]
                
                # Check agent availability
                if (agent.state == "active" and 
                    self.agent_workloads.get(agent_name, 0) < 5):  # Max 5 concurrent tasks
                    available_agents.append(agent_name)
                    self.agent_workloads[agent_name] += 1
        
        task.assigned_agents = available_agents
        return available_agents
    
    async def _synthesize_analysis_results(self, analysis_results: Dict[str, Any], symbols: List[str]) -> Dict[str, Any]:
        """Synthesize multiple agent analysis results using AI"""
        ai_prompt = f"""
        Synthesize the following market analysis results from multiple AI agents for {symbols}:
        
        {json.dumps(analysis_results, indent=2)}
        
        Provide a unified analysis including:
        1. Consensus view on market direction
        2. Confidence level (0-1)
        3. Key opportunities and risks
        4. Recommended actions
        5. Conflicting opinions and their resolution
        """
        
        try:
            synthesis = await self.ai_service.generate_response(
                "integration_manager", ai_prompt, max_tokens=1500
            )
            
            return {
                "success": True,
                "synthesis": synthesis,
                "agents_analyzed": len(analysis_results),
                "symbols": symbols
            }
            
        except Exception as e:
            self.logger.error(f"Failed to synthesize analysis results: {e}")
            return {
                "success": False,
                "error": str(e),
                "raw_results": analysis_results
            }
    
    async def _system_monitor_loop(self):
        """Main system monitoring loop"""
        while self.system_state != SystemState.SHUTDOWN:
            try:
                await self._update_system_metrics()
                await self._check_system_health()
                await asyncio.sleep(30)  # Monitor every 30 seconds
                
            except Exception as e:
                self.logger.error(f"System monitor error: {e}")
                await asyncio.sleep(60)  # Wait longer on error
    
    async def _agent_health_monitor(self):
        """Monitor individual agent health"""
        while self.system_state != SystemState.SHUTDOWN:
            try:
                for agent_name, agent in self.agents.items():
                    # Check agent responsiveness
                    start_time = datetime.utcnow()
                    
                    try:
                        status = await asyncio.wait_for(
                            agent.get_agent_status(), timeout=10.0
                        )
                        
                        response_time = (datetime.utcnow() - start_time).total_seconds()
                        
                        # Update performance metrics
                        self.agent_performance[agent_name]["avg_response_time"] = (
                            self.agent_performance[agent_name]["avg_response_time"] * 0.9 + 
                            response_time * 0.1
                        )
                        
                        # Check if agent is healthy
                        if response_time > self.emergency_thresholds["max_response_time"]:
                            self.logger.warning(f"Agent {agent_name} slow response: {response_time:.2f}s")
                        
                    except asyncio.TimeoutError:
                        self.logger.error(f"Agent {agent_name} unresponsive")
                        # Could trigger agent restart here
                    
                    except Exception as e:
                        self.logger.error(f"Health check failed for {agent_name}: {e}")
                
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Agent health monitor error: {e}")
                await asyncio.sleep(120)
    
    async def _coordination_task_processor(self):
        """Process coordination tasks"""
        while self.system_state != SystemState.SHUTDOWN:
            try:
                # Check for expired tasks
                current_time = datetime.utcnow()
                expired_tasks = []
                
                for task_id, task in self.active_tasks.items():
                    if task.deadline and current_time > task.deadline and task.status == "pending":
                        expired_tasks.append(task_id)
                
                # Handle expired tasks
                for task_id in expired_tasks:
                    task = self.active_tasks[task_id]
                    task.status = "expired"
                    self.logger.warning(f"Task {task_id} expired: {task.task_type}")
                
                # Clean up completed tasks older than 1 hour
                cleanup_time = current_time - timedelta(hours=1)
                completed_tasks = [
                    task_id for task_id, task in self.active_tasks.items()
                    if task.status in ["completed", "expired", "rejected"] and task.created_at < cleanup_time
                ]
                
                for task_id in completed_tasks:
                    del self.active_tasks[task_id]
                
                await asyncio.sleep(30)  # Process every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Task processor error: {e}")
                await asyncio.sleep(60)
    
    async def _emergency_monitor(self):
        """Monitor for emergency conditions"""
        while self.system_state != SystemState.SHUTDOWN:
            try:
                # Check emergency thresholds
                emergency_conditions = await self._check_emergency_conditions()
                
                for condition in emergency_conditions:
                    await self.coordinate_emergency_response(
                        condition["type"], condition["context"]
                    )
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Emergency monitor error: {e}")
                await asyncio.sleep(30)
    
    async def _update_system_metrics(self):
        """Update system-wide metrics"""
        active_agents = sum(1 for agent in self.agents.values() if agent.state == "active")
        
        total_decisions = sum(
            self.agent_performance[name]["decisions_made"] 
            for name in self.agent_performance
        )
        
        avg_response_time = sum(
            self.agent_performance[name]["avg_response_time"] 
            for name in self.agent_performance
        ) / len(self.agent_performance) if self.agent_performance else 0
        
        uptime = (datetime.utcnow() - self.start_time).total_seconds()
        
        self.system_metrics = SystemMetrics(
            total_agents=len(self.agents),
            active_agents=active_agents,
            decisions_made_today=total_decisions,
            successful_trades=0,  # Would be calculated from actual trades
            total_pnl=0.0,  # Would be calculated from actual P&L
            risk_level="medium",  # Would be determined by risk managers
            system_uptime=uptime,
            avg_response_time=avg_response_time,
            collaboration_events=self.system_metrics.collaboration_events,
            learning_records=sum(len(agent.learning_records) for agent in self.agents.values()),
            timestamp=datetime.utcnow()
        )
    
    async def _check_system_health(self):
        """Check overall system health"""
        # Check agent availability
        availability_ratio = self.system_metrics.active_agents / self.system_metrics.total_agents
        
        if availability_ratio < self.emergency_thresholds["min_agent_availability"]:
            self.logger.warning(f"Low agent availability: {availability_ratio:.2%}")
        
        # Check response times
        if self.system_metrics.avg_response_time > self.emergency_thresholds["max_response_time"]:
            self.logger.warning(f"High average response time: {self.system_metrics.avg_response_time:.2f}s")
    
    async def _check_emergency_conditions(self) -> List[Dict[str, Any]]:
        """Check for emergency conditions"""
        conditions = []
        
        # Check system availability
        availability_ratio = self.system_metrics.active_agents / self.system_metrics.total_agents
        if availability_ratio < self.emergency_thresholds["min_agent_availability"]:
            conditions.append({
                "type": "system_failure",
                "context": {"availability_ratio": availability_ratio}
            })
        
        # Check response times
        if self.system_metrics.avg_response_time > self.emergency_thresholds["max_response_time"]:
            conditions.append({
                "type": "performance_degradation",
                "context": {"avg_response_time": self.system_metrics.avg_response_time}
            })
        
        return conditions
    
    async def _handle_market_crash_emergency(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle market crash emergency"""
        self.logger.warning("🚨 Handling market crash emergency")
        
        # Stop all trading
        for agent_name, agent in self.agents.items():
            if "trader" in agent_name:
                # Would implement emergency stop trading
                pass
        
        # Increase risk monitoring
        for agent_name, agent in self.agents.items():
            if "risk_manager" in agent_name:
                # Would implement enhanced risk monitoring
                pass
        
        return {
            "actions_taken": ["stopped_trading", "enhanced_risk_monitoring"],
            "agents_affected": len(self.agents),
            "emergency_mode": True
        }
    
    async def _handle_system_failure_emergency(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle system failure emergency"""
        self.logger.warning("🚨 Handling system failure emergency")
        
        # Attempt to restart failed agents
        restart_count = 0
        for agent_name, agent in self.agents.items():
            if agent.state != "active":
                try:
                    await agent.initialize()
                    restart_count += 1
                except Exception as e:
                    self.logger.error(f"Failed to restart agent {agent_name}: {e}")
        
        return {
            "actions_taken": ["agent_restart_attempted"],
            "agents_restarted": restart_count,
            "total_agents": len(self.agents)
        }
    
    async def _handle_risk_breach_emergency(self, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle risk breach emergency"""
        self.logger.warning("🚨 Handling risk breach emergency")
        
        # Implement emergency risk controls
        return {
            "actions_taken": ["emergency_risk_controls"],
            "risk_level": context.get("risk_level", "unknown")
        }
    
    async def _handle_general_emergency(self, emergency_type: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Handle general emergency"""
        self.logger.warning(f"🚨 Handling general emergency: {emergency_type}")
        
        return {
            "actions_taken": ["general_emergency_protocol"],
            "emergency_type": emergency_type,
            "context": context
        }
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        await self._update_system_metrics()
        
        agent_statuses = {}
        for agent_name, agent in self.agents.items():
            try:
                status = await agent.get_agent_status()
                agent_statuses[agent_name] = status
            except Exception as e:
                agent_statuses[agent_name] = {"error": str(e), "state": "error"}
        
        return {
            "system_state": self.system_state.value,
            "coordination_mode": self.coordination_mode.value,
            "system_metrics": asdict(self.system_metrics),
            "agent_statuses": agent_statuses,
            "active_tasks": len(self.active_tasks),
            "collaboration_network": {k: list(v) for k, v in self.collaboration_network.items()},
            "uptime_hours": (datetime.utcnow() - self.start_time).total_seconds() / 3600
        }
    
    async def shutdown(self):
        """Gracefully shutdown the integration manager"""
        self.logger.info("🔄 Shutting down Agent Integration Manager...")
        
        self.system_state = SystemState.SHUTDOWN
        
        # Shutdown all agents
        for agent_name, agent in self.agents.items():
            try:
                # Would implement agent shutdown if available
                self.logger.info(f"  Shutting down {agent_name}")
            except Exception as e:
                self.logger.error(f"Error shutting down {agent_name}: {e}")
        
        self.logger.info("✅ Agent Integration Manager shutdown complete")

# Factory function
def create_integration_manager(ai_service: AIService, config: Config, db_manager: DatabaseManager) -> AgentIntegrationManager:
    """Create and return an AgentIntegrationManager instance"""
    return AgentIntegrationManager(ai_service, config, db_manager)