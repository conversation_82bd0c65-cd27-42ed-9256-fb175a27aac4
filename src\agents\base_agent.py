"""
Noryon V2 - Base AI Agent
Foundation class for all AI trading agents with Ollama integration
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from abc import ABC, abstractmethod
import json
from dataclasses import dataclass
from enum import Enum
import aiohttp
import time

from src.core.config import Config
from src.core.logger import get_agent_logger, get_performance_logger
from src.db.database_manager import DatabaseManager

class AgentState(Enum):
    INITIALIZING = "initializing"
    ACTIVE = "active"
    IDLE = "idle"
    PROCESSING = "processing"
    ERROR = "error"
    STOPPED = "stopped"

@dataclass
class AgentMessage:
    """Message structure for agent communication"""
    source: str
    target: str
    message_type: str
    content: Dict[str, Any]
    timestamp: datetime
    priority: int = 1
    correlation_id: Optional[str] = None

@dataclass
class AnalysisResult:
    """Standard analysis result structure"""
    agent_name: str
    analysis_type: str
    symbol: str
    result: Dict[str, Any]
    confidence: float
    reasoning: str
    timestamp: datetime
    metadata: Dict[str, Any] = None

@dataclass
class TradingSignal:
    """Trading signal structure"""
    agent_name: str
    symbol: str
    action: str  # buy, sell, hold
    strength: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    price_target: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    strategy: str
    reasoning: str
    risk_score: float
    timestamp: datetime
    metadata: Dict[str, Any] = None

class BaseAgent(ABC):
    """Base class for all AI trading agents"""
    
    def __init__(self, agent_name: str, model_name: str, config: Config, db_manager: DatabaseManager):
        self.agent_name = agent_name
        self.model_name = model_name
        self.config = config
        self.db_manager = db_manager
        
        # Logging
        self.logger = get_agent_logger(agent_name)
        self.performance_logger = get_performance_logger(f"agent.{agent_name}")
        
        # Agent state
        self.state = AgentState.INITIALIZING
        self.last_heartbeat = datetime.utcnow()
        self.last_activity = datetime.utcnow()
        
        # Model configuration
        self.model_config = config.get_model_config(agent_name)
        self.max_tokens = self.model_config.get('max_tokens', 4096)
        self.temperature = self.model_config.get('temperature', 0.3)
        self.update_interval = self.model_config.get('update_interval', 60)
        
        # Ollama client
        self.ollama_url = config.ollama_url
        self.session: Optional[aiohttp.ClientSession] = None
        
        # Message queue
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.outbound_messages: asyncio.Queue = asyncio.Queue()
        
        # Analysis cache
        self.analysis_cache: Dict[str, Any] = {}
        self.cache_ttl = 300  # 5 minutes
        
        # Performance metrics
        self.metrics = {
            'total_analyses': 0,
            'successful_analyses': 0,
            'failed_analyses': 0,
            'avg_processing_time': 0,
            'last_analysis_time': None,
            'uptime_start': datetime.utcnow()
        }
        
        # Background tasks
        self.background_tasks: List[asyncio.Task] = []
        self.running = False
    
    async def initialize(self):
        """Initialize the agent"""
        try:
            self.logger.info(f"🤖 Initializing {self.agent_name} with model {self.model_name}")
            
            # Create HTTP session
            self.session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(total=self.config.OLLAMA_TIMEOUT)
            )
            
            # Test Ollama connection
            await self._test_ollama_connection()
            
            # Initialize agent-specific components
            await self._initialize_agent()
            
            # Register agent in database
            await self._register_agent()
            
            self.state = AgentState.IDLE
            self.logger.info(f"✅ {self.agent_name} initialized successfully")
            
        except Exception as e:
            self.state = AgentState.ERROR
            self.logger.error(f"❌ Failed to initialize {self.agent_name}: {e}")
            raise
    
    async def start(self):
        """Start the agent and background tasks"""
        try:
            self.running = True
            self.state = AgentState.ACTIVE
            
            # Start background tasks
            self.background_tasks = [
                asyncio.create_task(self._message_processor()),
                asyncio.create_task(self._heartbeat_sender()),
                asyncio.create_task(self._periodic_analysis()),
                asyncio.create_task(self._cache_cleaner())
            ]
            
            # Start agent-specific tasks
            agent_tasks = await self._start_agent_tasks()
            if agent_tasks:
                self.background_tasks.extend(agent_tasks)
            
            self.logger.info(f"🚀 {self.agent_name} started with {len(self.background_tasks)} background tasks")
            
        except Exception as e:
            self.state = AgentState.ERROR
            self.logger.error(f"❌ Failed to start {self.agent_name}: {e}")
            raise
    
    async def stop(self):
        """Stop the agent and cleanup"""
        try:
            self.running = False
            self.state = AgentState.STOPPED
            
            # Cancel background tasks
            for task in self.background_tasks:
                task.cancel()
            
            if self.background_tasks:
                await asyncio.gather(*self.background_tasks, return_exceptions=True)
            
            # Cleanup agent-specific resources
            await self._cleanup_agent()
            
            # Close HTTP session
            if self.session:
                await self.session.close()
            
            self.logger.info(f"🛑 {self.agent_name} stopped")
            
        except Exception as e:
            self.logger.error(f"❌ Error stopping {self.agent_name}: {e}")
    
    async def receive_message(self, message: AgentMessage):
        """Receive a message from another agent or system"""
        await self.message_queue.put(message)
        self.logger.debug(f"Received message from {message.source}: {message.message_type}")
    
    async def send_message(self, target: str, message_type: str, content: Dict[str, Any], 
                          priority: int = 1, correlation_id: Optional[str] = None):
        """Send a message to another agent or system"""
        message = AgentMessage(
            source=self.agent_name,
            target=target,
            message_type=message_type,
            content=content,
            timestamp=datetime.utcnow(),
            priority=priority,
            correlation_id=correlation_id
        )
        
        await self.outbound_messages.put(message)
        self.logger.debug(f"Sent message to {target}: {message_type}")
    
    async def analyze(self, data: Dict[str, Any], analysis_type: str) -> AnalysisResult:
        """Perform analysis using the AI model"""
        try:
            self.state = AgentState.PROCESSING
            start_time = time.time()
            
            # Check cache first
            cache_key = self._generate_cache_key(data, analysis_type)
            cached_result = self._get_cached_analysis(cache_key)
            if cached_result:
                return cached_result
            
            # Prepare prompt
            prompt = await self._prepare_analysis_prompt(data, analysis_type)
            
            # Call Ollama
            with self.performance_logger.timer("ollama_analysis", analysis_type=analysis_type):
                response = await self._call_ollama(prompt)
            
            # Parse response
            result = await self._parse_analysis_response(response, analysis_type)
            
            # Create analysis result
            analysis_result = AnalysisResult(
                agent_name=self.agent_name,
                analysis_type=analysis_type,
                symbol=data.get('symbol', 'UNKNOWN'),
                result=result,
                confidence=result.get('confidence', 0.5),
                reasoning=result.get('reasoning', ''),
                timestamp=datetime.utcnow(),
                metadata={'processing_time': time.time() - start_time}
            )
            
            # Cache result
            self._cache_analysis(cache_key, analysis_result)
            
            # Update metrics
            self.metrics['total_analyses'] += 1
            self.metrics['successful_analyses'] += 1
            self.metrics['last_analysis_time'] = datetime.utcnow()
            
            # Log analysis
            await self._log_analysis(analysis_result)
            
            self.state = AgentState.ACTIVE
            self.last_activity = datetime.utcnow()
            
            return analysis_result
            
        except Exception as e:
            self.metrics['failed_analyses'] += 1
            self.state = AgentState.ERROR
            self.logger.error(f"Analysis failed: {e}")
            raise
    
    async def generate_signal(self, analysis_data: Dict[str, Any]) -> Optional[TradingSignal]:
        """Generate a trading signal based on analysis"""
        try:
            # Prepare signal generation prompt
            prompt = await self._prepare_signal_prompt(analysis_data)
            
            # Call Ollama
            response = await self._call_ollama(prompt)
            
            # Parse signal response
            signal_data = await self._parse_signal_response(response)
            
            if not signal_data or signal_data.get('action') == 'hold':
                return None
            
            # Create trading signal
            signal = TradingSignal(
                agent_name=self.agent_name,
                symbol=analysis_data.get('symbol', 'UNKNOWN'),
                action=signal_data['action'],
                strength=signal_data.get('strength', 0.5),
                confidence=signal_data.get('confidence', 0.5),
                price_target=signal_data.get('price_target'),
                stop_loss=signal_data.get('stop_loss'),
                take_profit=signal_data.get('take_profit'),
                strategy=signal_data.get('strategy', 'default'),
                reasoning=signal_data.get('reasoning', ''),
                risk_score=signal_data.get('risk_score', 0.5),
                timestamp=datetime.utcnow(),
                metadata=analysis_data
            )
            
            # Log signal
            await self._log_signal(signal)
            
            return signal
            
        except Exception as e:
            self.logger.error(f"Signal generation failed: {e}")
            return None
    
    # Ollama Integration Methods
    
    async def _test_ollama_connection(self):
        """Test connection to Ollama"""
        try:
            async with self.session.get(f"{self.ollama_url}/api/tags") as response:
                if response.status == 200:
                    models = await response.json()
                    model_names = [model['name'] for model in models.get('models', [])]
                    
                    if self.model_name not in model_names:
                        raise ValueError(f"Model {self.model_name} not found in Ollama. Available models: {model_names}")
                    
                    self.logger.info(f"✅ Ollama connection verified, model {self.model_name} available")
                else:
                    raise ConnectionError(f"Ollama connection failed: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"❌ Ollama connection test failed: {e}")
            raise
    
    async def _call_ollama(self, prompt: str, system_prompt: str = None) -> str:
        """Call Ollama API for text generation"""
        try:
            payload = {
                "model": self.model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": self.temperature,
                    "num_predict": self.max_tokens
                }
            }
            
            if system_prompt:
                payload["system"] = system_prompt
            
            async with self.session.post(
                f"{self.ollama_url}/api/generate",
                json=payload
            ) as response:
                if response.status == 200:
                    result = await response.json()
                    return result.get('response', '')
                else:
                    error_text = await response.text()
                    raise Exception(f"Ollama API error {response.status}: {error_text}")
                    
        except Exception as e:
            self.logger.error(f"Ollama API call failed: {e}")
            raise
    
    # Cache Management
    
    def _generate_cache_key(self, data: Dict[str, Any], analysis_type: str) -> str:
        """Generate cache key for analysis"""
        key_data = {
            'agent': self.agent_name,
            'type': analysis_type,
            'symbol': data.get('symbol', ''),
            'timestamp_bucket': int(datetime.utcnow().timestamp() // self.cache_ttl)
        }
        return json.dumps(key_data, sort_keys=True)
    
    def _get_cached_analysis(self, cache_key: str) -> Optional[AnalysisResult]:
        """Get cached analysis result"""
        cached = self.analysis_cache.get(cache_key)
        if cached and datetime.utcnow() - cached['timestamp'] < timedelta(seconds=self.cache_ttl):
            return cached['result']
        return None
    
    def _cache_analysis(self, cache_key: str, result: AnalysisResult):
        """Cache analysis result"""
        self.analysis_cache[cache_key] = {
            'result': result,
            'timestamp': datetime.utcnow()
        }
    
    # Background Tasks
    
    async def _message_processor(self):
        """Process incoming messages"""
        while self.running:
            try:
                message = await asyncio.wait_for(self.message_queue.get(), timeout=1.0)
                await self._handle_message(message)
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing message: {e}")
    
    async def _heartbeat_sender(self):
        """Send periodic heartbeat"""
        while self.running:
            try:
                self.last_heartbeat = datetime.utcnow()
                
                # Update agent status in database
                await self.db_manager.set_agent_status(
                    self.agent_name, 
                    self.state.value,
                    ttl=300
                )
                
                await asyncio.sleep(60)  # Send heartbeat every minute
                
            except Exception as e:
                self.logger.error(f"Error sending heartbeat: {e}")
                await asyncio.sleep(60)
    
    async def _periodic_analysis(self):
        """Perform periodic analysis"""
        while self.running:
            try:
                if self.state == AgentState.ACTIVE:
                    await self._perform_periodic_analysis()
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                self.logger.error(f"Error in periodic analysis: {e}")
                await asyncio.sleep(self.update_interval)
    
    async def _cache_cleaner(self):
        """Clean expired cache entries"""
        while self.running:
            try:
                current_time = datetime.utcnow()
                expired_keys = []
                
                for key, cached in self.analysis_cache.items():
                    if current_time - cached['timestamp'] > timedelta(seconds=self.cache_ttl):
                        expired_keys.append(key)
                
                for key in expired_keys:
                    del self.analysis_cache[key]
                
                await asyncio.sleep(300)  # Clean every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error cleaning cache: {e}")
                await asyncio.sleep(300)
    
    # Database Operations
    
    async def _register_agent(self):
        """Register agent in database"""
        try:
            query = """
            INSERT INTO ai_agents (name, model_name, role, status, configuration)
            VALUES (:name, :model_name, :role, :status, :configuration)
            ON CONFLICT (name) DO UPDATE SET
                model_name = EXCLUDED.model_name,
                role = EXCLUDED.role,
                status = EXCLUDED.status,
                configuration = EXCLUDED.configuration,
                updated_at = CURRENT_TIMESTAMP
            """
            
            await self.db_manager.execute_postgres_query(query, {
                'name': self.agent_name,
                'model_name': self.model_name,
                'role': self.model_config.get('role', ''),
                'status': self.state.value,
                'configuration': json.dumps(self.model_config)
            })
            
        except Exception as e:
            self.logger.error(f"Error registering agent: {e}")
    
    async def _log_analysis(self, result: AnalysisResult):
        """Log analysis result to database"""
        try:
            await self.db_manager.insert_agent_log({
                'agent_name': self.agent_name,
                'log_level': 'INFO',
                'message': f"Analysis completed: {result.analysis_type}",
                'metadata': {
                    'analysis_type': result.analysis_type,
                    'symbol': result.symbol,
                    'confidence': result.confidence,
                    'processing_time': result.metadata.get('processing_time', 0)
                },
                'timestamp': datetime.utcnow()
            })
        except Exception as e:
            self.logger.error(f"Error logging analysis: {e}")
    
    async def _log_signal(self, signal: TradingSignal):
        """Log trading signal to database"""
        try:
            # Get agent ID
            agent_query = "SELECT id FROM ai_agents WHERE name = :name"
            agent_result = await self.db_manager.execute_postgres_query(agent_query, {'name': self.agent_name})
            
            if agent_result:
                agent_id = agent_result[0]['id']
                
                await self.db_manager.insert_trading_signal({
                    'agent_id': agent_id,
                    'symbol': signal.symbol,
                    'action': signal.action,
                    'strength': signal.strength,
                    'confidence': signal.confidence,
                    'price_target': signal.price_target,
                    'stop_loss': signal.stop_loss,
                    'take_profit': signal.take_profit,
                    'strategy': signal.strategy,
                    'reasoning': signal.reasoning,
                    'risk_score': signal.risk_score
                })
                
        except Exception as e:
            self.logger.error(f"Error logging signal: {e}")
    
    # Abstract Methods (to be implemented by subclasses)
    
    @abstractmethod
    async def _initialize_agent(self):
        """Initialize agent-specific components"""
        raise NotImplementedError("Subclasses must implement _initialize_agent")
    
    @abstractmethod
    async def _start_agent_tasks(self) -> List[asyncio.Task]:
        """Start agent-specific background tasks"""
        raise NotImplementedError("Subclasses must implement _start_agent_tasks")
    
    @abstractmethod
    async def _cleanup_agent(self):
        """Cleanup agent-specific resources"""
        raise NotImplementedError("Subclasses must implement _cleanup_agent")
    
    @abstractmethod
    async def _handle_message(self, message: AgentMessage):
        """Handle incoming messages"""
        raise NotImplementedError("Subclasses must implement _handle_message")
    
    @abstractmethod
    async def _perform_periodic_analysis(self):
        """Perform periodic analysis"""
        raise NotImplementedError("Subclasses must implement _perform_periodic_analysis")
    
    @abstractmethod
    async def _prepare_analysis_prompt(self, data: Dict[str, Any], analysis_type: str) -> str:
        """Prepare prompt for analysis"""
        raise NotImplementedError("Subclasses must implement _prepare_analysis_prompt")
    
    @abstractmethod
    async def _parse_analysis_response(self, response: str, analysis_type: str) -> Dict[str, Any]:
        """Parse analysis response from AI model"""
        raise NotImplementedError("Subclasses must implement _parse_analysis_response")
    
    @abstractmethod
    async def _prepare_signal_prompt(self, analysis_data: Dict[str, Any]) -> str:
        """Prepare prompt for signal generation"""
        raise NotImplementedError("Subclasses must implement _prepare_signal_prompt")
    
    @abstractmethod
    async def _parse_signal_response(self, response: str) -> Dict[str, Any]:
        """Parse signal response from AI model"""
        raise NotImplementedError("Subclasses must implement _parse_signal_response")
    
    # Utility Methods
    
    def get_status(self) -> Dict[str, Any]:
        """Get agent status"""
        uptime = datetime.utcnow() - self.metrics['uptime_start']
        
        return {
            'name': self.agent_name,
            'model': self.model_name,
            'state': self.state.value,
            'last_heartbeat': self.last_heartbeat,
            'last_activity': self.last_activity,
            'uptime_seconds': uptime.total_seconds(),
            'metrics': self.metrics,
            'cache_size': len(self.analysis_cache),
            'message_queue_size': self.message_queue.qsize(),
            'outbound_queue_size': self.outbound_messages.qsize()
        }
    
    def get_performance_metrics(self) -> Dict[str, Any]:
        """Get performance metrics"""
        total_analyses = self.metrics['total_analyses']
        success_rate = (self.metrics['successful_analyses'] / total_analyses) if total_analyses > 0 else 0
        
        return {
            'total_analyses': total_analyses,
            'success_rate': success_rate,
            'avg_processing_time': self.metrics['avg_processing_time'],
            'last_analysis_time': self.metrics['last_analysis_time'],
            'uptime_hours': (datetime.utcnow() - self.metrics['uptime_start']).total_seconds() / 3600
        }