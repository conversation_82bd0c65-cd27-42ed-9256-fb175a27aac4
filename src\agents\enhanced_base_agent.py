"""Enhanced Base Agent with Autonomous AI Capabilities
Provides advanced autonomous capabilities for AI agents with integrated toolkit access,
learning mechanisms, and sophisticated decision-making frameworks.
"""

import asyncio
import logging
import json
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Callable
from dataclasses import dataclass, asdict
from enum import Enum
from abc import ABC, abstractmethod
import numpy as np

from src.agents.agent_tools import (
    AgentToolkit, get_toolkit, ToolResult, MarketSignal, RiskAssessment,
    ToolCategory, ToolPriority
)
from src.agents.base_agent import BaseAgent, AgentState, AgentMessage, AnalysisResult, TradingSignal
from src.core.config import Config
from src.core.logger import get_agent_logger
from src.services.ai_service import AIService
from src.db.database_manager import DatabaseManager

class DecisionType(Enum):
    TRADING = "trading"
    ANALYSIS = "analysis"
    RISK_MANAGEMENT = "risk_management"
    COMMUNICATION = "communication"
    LEARNING = "learning"
    SYSTEM_CONTROL = "system_control"

class LearningMode(Enum):
    PASSIVE = "passive"  # Learn from observations
    ACTIVE = "active"    # Actively seek learning opportunities
    ADAPTIVE = "adaptive" # Adapt behavior based on performance

@dataclass
class Decision:
    """Represents an autonomous decision made by an agent"""
    decision_id: str
    agent_name: str
    decision_type: DecisionType
    action: str
    parameters: Dict[str, Any]
    reasoning: str
    confidence: float
    expected_outcome: str
    risk_assessment: float
    timestamp: datetime
    execution_status: str = "pending"
    actual_outcome: Optional[str] = None
    success_score: Optional[float] = None

@dataclass
class LearningRecord:
    """Records learning experiences for agent improvement"""
    agent_name: str
    experience_type: str
    situation: Dict[str, Any]
    action_taken: str
    outcome: Dict[str, Any]
    success_score: float
    lessons_learned: List[str]
    timestamp: datetime
    metadata: Dict[str, Any] = None

@dataclass
class AgentCapability:
    """Represents a capability that an agent can perform"""
    name: str
    description: str
    category: ToolCategory
    proficiency_level: float  # 0.0 to 1.0
    usage_count: int
    success_rate: float
    last_used: Optional[datetime]
    prerequisites: List[str] = None

class EnhancedBaseAgent(BaseAgent):
    """Enhanced base agent with autonomous AI capabilities and toolkit integration"""
    
    def __init__(self, agent_name: str, model_name: str, config: Config, 
                 db_manager: DatabaseManager, ai_service: AIService):
        super().__init__(agent_name, model_name, config, db_manager)
        self.ai_service = ai_service
        
        # Enhanced agent properties
        self.toolkit: Optional[AgentToolkit] = None
        self.capabilities: Dict[str, AgentCapability] = {}
        self.decision_history: List[Decision] = []
        self.learning_records: List[LearningRecord] = []
        self.performance_metrics: Dict[str, float] = {}
        
        # Autonomous behavior settings
        self.autonomy_level = 0.7  # 0.0 to 1.0
        self.learning_mode = LearningMode.ADAPTIVE
        self.decision_threshold = 0.6  # Minimum confidence for autonomous decisions
        self.max_decisions_per_hour = 10
        
        # Communication and coordination
        self.peer_agents: List[str] = []
        self.collaboration_history: Dict[str, List[Dict]] = {}
        self.trust_scores: Dict[str, float] = {}  # Trust scores for other agents
        
        # Learning and adaptation
        self.experience_buffer_size = 1000
        self.learning_rate = 0.1
        self.exploration_rate = 0.2
        
        # Performance tracking
        self.decisions_made_today = 0
        self.last_decision_time = datetime.utcnow()
        self.success_streak = 0
        self.failure_count = 0
        
        self.logger.info(f"Enhanced agent {agent_name} initialized with autonomy level {self.autonomy_level}")
    
    async def initialize(self):
        """Initialize the enhanced agent with toolkit and capabilities"""
        try:
            # Get toolkit instance
            self.toolkit = get_toolkit()
            
            # Initialize capabilities
            await self._initialize_capabilities()
            
            # Load historical performance data
            await self._load_performance_history()
            
            # Set up peer agent connections
            await self._discover_peer_agents()
            
            self.state = AgentState.ACTIVE
            self.logger.info(f"Enhanced agent {self.agent_name} fully initialized")
            
        except Exception as e:
            self.logger.error(f"Agent initialization failed: {e}")
            self.state = AgentState.ERROR
    
    async def _initialize_capabilities(self):
        """Initialize agent capabilities based on available tools"""
        if not self.toolkit:
            return
        
        available_tools = self.toolkit.get_available_tools()
        
        for tool_name in available_tools:
            tool_info = self.toolkit.get_tool_info(tool_name)
            
            capability = AgentCapability(
                name=tool_name,
                description=f"Capability to use {tool_name}",
                category=ToolCategory(tool_info.get("category", "data_processing")),
                proficiency_level=0.5,  # Start with medium proficiency
                usage_count=0,
                success_rate=1.0,
                last_used=None
            )
            
            self.capabilities[tool_name] = capability
        
        self.logger.info(f"Initialized {len(self.capabilities)} capabilities")
    
    async def _load_performance_history(self):
        """Load historical performance data for learning"""
        # In a real implementation, this would load from database
        # For now, initialize with default metrics
        self.performance_metrics = {
            "overall_success_rate": 0.85,
            "decision_accuracy": 0.78,
            "risk_management_score": 0.82,
            "collaboration_effectiveness": 0.75,
            "learning_progress": 0.6
        }
    
    async def _discover_peer_agents(self):
        """Discover and connect with peer agents"""
        # In a real implementation, this would query the agent registry
        self.peer_agents = [
            "market_watcher", "strategy_researcher", "risk_officer",
            "technical_analyst", "news_analyst", "trade_executor",
            "compliance_auditor", "chief_analyst", "portfolio_manager"
        ]
        
        # Initialize trust scores
        for peer in self.peer_agents:
            if peer != self.agent_name:
                self.trust_scores[peer] = 0.7  # Start with moderate trust
    
    async def make_autonomous_decision(self, situation: Dict[str, Any], 
                                     decision_type: DecisionType) -> Optional[Decision]:
        """Make an autonomous decision based on current situation"""
        if not self._can_make_decision():
            return None
        
        try:
            # Analyze situation with AI
            analysis_prompt = f"""
            As an autonomous AI trading agent ({self.agent_name}), analyze this situation:
            
            Situation: {json.dumps(situation, default=str)}
            Decision Type: {decision_type.value}
            
            Consider:
            1. Current market conditions
            2. Risk factors
            3. Potential actions and outcomes
            4. Confidence level for each option
            5. Resource requirements
            
            Recommend the best action with reasoning and confidence score (0-1).
            Format response as JSON with: action, parameters, reasoning, confidence, expected_outcome, risk_score
            """
            
            ai_response = await self.ai_service.generate_response(
                self.agent_name, analysis_prompt, situation
            )
            
            # Parse AI response
            try:
                decision_data = json.loads(ai_response)
            except json.JSONDecodeError:
                # Fallback parsing if JSON is malformed
                decision_data = self._parse_ai_response_fallback(ai_response)
            
            # Create decision object
            decision = Decision(
                decision_id=f"{self.agent_name}_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}",
                agent_name=self.agent_name,
                decision_type=decision_type,
                action=decision_data.get("action", "hold"),
                parameters=decision_data.get("parameters", {}),
                reasoning=decision_data.get("reasoning", "AI analysis"),
                confidence=float(decision_data.get("confidence", 0.5)),
                expected_outcome=decision_data.get("expected_outcome", "Unknown"),
                risk_assessment=float(decision_data.get("risk_score", 0.5)),
                timestamp=datetime.utcnow()
            )
            
            # Validate decision
            if await self._validate_decision(decision):
                self.decision_history.append(decision)
                self.decisions_made_today += 1
                self.last_decision_time = datetime.utcnow()
                
                self.logger.info(f"Autonomous decision made: {decision.action} (confidence: {decision.confidence:.2f})")
                return decision
            else:
                self.logger.warning(f"Decision validation failed for {decision.action}")
                return None
                
        except Exception as e:
            self.logger.error(f"Autonomous decision making failed: {e}")
            return None
    
    def _can_make_decision(self) -> bool:
        """Check if agent can make autonomous decisions"""
        # Check autonomy level
        if self.autonomy_level < 0.5:
            return False
        
        # Check decision rate limits
        if self.decisions_made_today >= self.max_decisions_per_hour:
            return False
        
        # Check agent state
        if self.state not in [AgentState.ACTIVE, AgentState.PROCESSING]:
            return False
        
        # Check recent failure rate
        if self.failure_count > 3 and self.success_streak == 0:
            return False
        
        return True
    
    async def _validate_decision(self, decision: Decision) -> bool:
        """Validate a decision before execution"""
        # Check confidence threshold
        if decision.confidence < self.decision_threshold:
            return False
        
        # Check risk assessment
        if decision.risk_assessment > 0.8:  # High risk
            return False
        
        # Check resource availability
        if not await self._check_resource_availability(decision):
            return False
        
        # Peer validation for critical decisions
        if decision.decision_type == DecisionType.TRADING and decision.risk_assessment > 0.6:
            return await self._get_peer_validation(decision)
        
        return True
    
    async def _check_resource_availability(self, decision: Decision) -> bool:
        """Check if required resources are available for decision execution"""
        # Check if required tools are available
        required_tools = decision.parameters.get("required_tools", [])
        
        for tool_name in required_tools:
            if tool_name not in self.capabilities:
                return False
            
            capability = self.capabilities[tool_name]
            if capability.proficiency_level < 0.3:  # Too low proficiency
                return False
        
        return True
    
    async def _get_peer_validation(self, decision: Decision) -> bool:
        """Get validation from peer agents for critical decisions"""
        if not self.toolkit:
            return True  # Skip validation if no toolkit
        
        try:
            # Request peer opinions
            validation_request = {
                "decision_summary": {
                    "action": decision.action,
                    "confidence": decision.confidence,
                    "risk_assessment": decision.risk_assessment,
                    "reasoning": decision.reasoning[:200]  # Truncated
                },
                "requesting_agent": self.agent_name
            }
            
            # Broadcast to peers
            comm_result = await self.toolkit.execute_tool(
                self.agent_name, "agent_communicator",
                action="broadcast",
                message=json.dumps(validation_request),
                topic="decision_validation"
            )
            
            # For now, assume validation passes if communication succeeds
            return comm_result.success
            
        except Exception as e:
            self.logger.error(f"Peer validation failed: {e}")
            return False
    
    async def execute_decision(self, decision: Decision) -> bool:
        """Execute an autonomous decision"""
        if not self.toolkit:
            self.logger.error("Cannot execute decision: toolkit not available")
            return False
        
        try:
            decision.execution_status = "executing"
            
            # Determine which tool to use based on decision type
            tool_name = self._get_tool_for_decision(decision)
            
            if not tool_name:
                self.logger.error(f"No suitable tool found for decision type: {decision.decision_type}")
                decision.execution_status = "failed"
                return False
            
            # Execute using appropriate tool
            result = await self.toolkit.execute_tool(
                self.agent_name, tool_name, **decision.parameters
            )
            
            # Update decision with results
            decision.execution_status = "completed" if result.success else "failed"
            decision.actual_outcome = result.message
            decision.success_score = 1.0 if result.success else 0.0
            
            # Update performance metrics
            await self._update_performance_metrics(decision, result)
            
            # Learn from the experience
            await self._record_learning_experience(decision, result)
            
            self.logger.info(f"Decision executed: {decision.action} - {'Success' if result.success else 'Failed'}")
            return result.success
            
        except Exception as e:
            self.logger.error(f"Decision execution failed: {e}")
            decision.execution_status = "error"
            decision.actual_outcome = f"Execution error: {str(e)}"
            decision.success_score = 0.0
            return False
    
    def _get_tool_for_decision(self, decision: Decision) -> Optional[str]:
        """Determine which tool to use for a given decision"""
        tool_mapping = {
            DecisionType.TRADING: "trading_executor",
            DecisionType.ANALYSIS: "market_data_analyzer",
            DecisionType.RISK_MANAGEMENT: "risk_manager",
            DecisionType.COMMUNICATION: "agent_communicator",
            DecisionType.SYSTEM_CONTROL: "performance_monitor"
        }
        
        return tool_mapping.get(decision.decision_type)
    
    async def _update_performance_metrics(self, decision: Decision, result: ToolResult):
        """Update agent performance metrics based on decision outcome"""
        if result.success:
            self.success_streak += 1
            self.failure_count = 0
        else:
            self.success_streak = 0
            self.failure_count += 1
        
        # Update capability proficiency
        tool_name = self._get_tool_for_decision(decision)
        if tool_name and tool_name in self.capabilities:
            capability = self.capabilities[tool_name]
            capability.usage_count += 1
            capability.last_used = datetime.utcnow()
            
            # Update success rate
            old_rate = capability.success_rate
            new_success = 1.0 if result.success else 0.0
            capability.success_rate = (old_rate * (capability.usage_count - 1) + new_success) / capability.usage_count
            
            # Update proficiency based on success rate
            if capability.success_rate > 0.8:
                capability.proficiency_level = min(1.0, capability.proficiency_level + 0.05)
            elif capability.success_rate < 0.6:
                capability.proficiency_level = max(0.1, capability.proficiency_level - 0.02)
    
    async def _record_learning_experience(self, decision: Decision, result: ToolResult):
        """Record learning experience for future improvement"""
        if self.learning_mode == LearningMode.PASSIVE:
            return
        
        # Extract lessons learned
        lessons = []
        if result.success:
            lessons.append(f"Successful {decision.action} with confidence {decision.confidence:.2f}")
            if decision.confidence > 0.8:
                lessons.append("High confidence decisions tend to succeed")
        else:
            lessons.append(f"Failed {decision.action} - review decision criteria")
            if decision.risk_assessment > 0.7:
                lessons.append("High risk decisions require more careful evaluation")
        
        learning_record = LearningRecord(
            agent_name=self.agent_name,
            experience_type=decision.decision_type.value,
            situation=decision.parameters,
            action_taken=decision.action,
            outcome={
                "success": result.success,
                "message": result.message,
                "execution_time": result.execution_time
            },
            success_score=decision.success_score or 0.0,
            lessons_learned=lessons,
            timestamp=datetime.utcnow()
        )
        
        self.learning_records.append(learning_record)
        
        # Keep only recent learning records
        if len(self.learning_records) > self.experience_buffer_size:
            self.learning_records = self.learning_records[-self.experience_buffer_size:]
    
    def _parse_ai_response_fallback(self, response: str) -> Dict[str, Any]:
        """Fallback parser for AI responses that aren't valid JSON"""
        # Simple keyword extraction
        lines = response.lower().split('\n')
        
        result = {
            "action": "hold",
            "parameters": {},
            "reasoning": response[:200],
            "confidence": 0.5,
            "expected_outcome": "Unknown",
            "risk_score": 0.5
        }
        
        # Extract action
        for line in lines:
            if "buy" in line:
                result["action"] = "buy"
                break
            elif "sell" in line:
                result["action"] = "sell"
                break
            elif "hold" in line:
                result["action"] = "hold"
                break
        
        # Extract confidence if mentioned
        for line in lines:
            if "confidence" in line:
                words = line.split()
                for i, word in enumerate(words):
                    if "confidence" in word and i + 1 < len(words):
                        try:
                            conf_val = float(words[i + 1].replace("%", "").replace(":", ""))
                            if conf_val > 1:
                                conf_val /= 100  # Convert percentage
                            result["confidence"] = min(1.0, max(0.0, conf_val))
                        except ValueError:
                            pass
                        break
        
        return result
    
    async def collaborate_with_peers(self, task: str, required_expertise: List[str]) -> Dict[str, Any]:
        """Collaborate with peer agents on complex tasks"""
        if not self.toolkit:
            return {"success": False, "message": "Toolkit not available"}
        
        try:
            # Find suitable peer agents
            suitable_peers = self._find_suitable_peers(required_expertise)
            
            if not suitable_peers:
                return {"success": False, "message": "No suitable peers found"}
            
            # Create collaboration request
            collaboration_request = {
                "task": task,
                "required_expertise": required_expertise,
                "initiator": self.agent_name,
                "timestamp": datetime.utcnow().isoformat(),
                "collaboration_id": f"collab_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}"
            }
            
            # Send collaboration requests
            responses = []
            for peer in suitable_peers:
                result = await self.toolkit.execute_tool(
                    self.agent_name, "agent_communicator",
                    action="send_message",
                    recipient=peer,
                    message=json.dumps(collaboration_request),
                    message_type="collaboration_request"
                )
                
                if result.success:
                    responses.append({"peer": peer, "status": "requested"})
            
            # Record collaboration attempt
            if task not in self.collaboration_history:
                self.collaboration_history[task] = []
            
            self.collaboration_history[task].append({
                "timestamp": datetime.utcnow(),
                "peers_contacted": suitable_peers,
                "responses": responses
            })
            
            return {
                "success": True,
                "collaboration_id": collaboration_request["collaboration_id"],
                "peers_contacted": len(suitable_peers),
                "responses": responses
            }
            
        except Exception as e:
            self.logger.error(f"Collaboration failed: {e}")
            return {"success": False, "message": f"Collaboration failed: {str(e)}"}
    
    def _find_suitable_peers(self, required_expertise: List[str]) -> List[str]:
        """Find peer agents with required expertise"""
        # Map expertise to agent types
        expertise_mapping = {
            "market_analysis": ["market_watcher", "technical_analyst"],
            "risk_assessment": ["risk_officer"],
            "trading": ["trade_executor"],
            "strategy": ["strategy_researcher", "chief_analyst"],
            "news_analysis": ["news_analyst"],
            "portfolio_management": ["portfolio_manager"],
            "compliance": ["compliance_auditor"]
        }
        
        suitable_peers = set()
        for expertise in required_expertise:
            if expertise in expertise_mapping:
                suitable_peers.update(expertise_mapping[expertise])
        
        # Filter by trust scores
        trusted_peers = [peer for peer in suitable_peers 
                        if peer in self.trust_scores and self.trust_scores[peer] > 0.5]
        
        return trusted_peers[:3]  # Limit to 3 peers
    
    async def adapt_behavior(self):
        """Adapt agent behavior based on performance and learning"""
        if self.learning_mode != LearningMode.ADAPTIVE:
            return
        
        try:
            # Analyze recent performance
            recent_decisions = [d for d in self.decision_history 
                             if d.timestamp > datetime.utcnow() - timedelta(hours=24)]
            
            if len(recent_decisions) < 5:
                return  # Not enough data
            
            # Calculate success rate
            successful_decisions = [d for d in recent_decisions if d.success_score and d.success_score > 0.7]
            success_rate = len(successful_decisions) / len(recent_decisions)
            
            # Adapt autonomy level
            if success_rate > 0.8:
                self.autonomy_level = min(1.0, self.autonomy_level + 0.05)
                self.decision_threshold = max(0.4, self.decision_threshold - 0.02)
            elif success_rate < 0.6:
                self.autonomy_level = max(0.3, self.autonomy_level - 0.05)
                self.decision_threshold = min(0.8, self.decision_threshold + 0.02)
            
            # Adapt exploration rate
            if self.success_streak > 5:
                self.exploration_rate = min(0.4, self.exploration_rate + 0.05)
            elif self.failure_count > 2:
                self.exploration_rate = max(0.1, self.exploration_rate - 0.02)
            
            self.logger.info(f"Behavior adapted: autonomy={self.autonomy_level:.2f}, threshold={self.decision_threshold:.2f}")
            
        except Exception as e:
            self.logger.error(f"Behavior adaptation failed: {e}")
    
    async def get_agent_status(self) -> Dict[str, Any]:
        """Get comprehensive agent status"""
        return {
            "agent_name": self.agent_name,
            "state": self.state.value,
            "autonomy_level": self.autonomy_level,
            "learning_mode": self.learning_mode.value,
            "capabilities_count": len(self.capabilities),
            "decisions_made_today": self.decisions_made_today,
            "success_streak": self.success_streak,
            "failure_count": self.failure_count,
            "performance_metrics": self.performance_metrics,
            "peer_agents_count": len(self.peer_agents),
            "learning_records_count": len(self.learning_records),
            "last_decision_time": self.last_decision_time.isoformat() if self.last_decision_time else None,
            "uptime": (datetime.utcnow() - self.last_heartbeat).total_seconds() if self.last_heartbeat else 0
        }
    
    @abstractmethod
    async def execute_specialized_task(self, task_data: Dict[str, Any]) -> ToolResult:
        """Execute agent-specific specialized tasks"""
        pass
    
    async def run_autonomous_cycle(self):
        """Main autonomous operation cycle"""
        while self.state == AgentState.ACTIVE:
            try:
                # Update heartbeat
                self.last_heartbeat = datetime.utcnow()
                
                # Check for messages
                if self.toolkit:
                    messages_result = await self.toolkit.execute_tool(
                        self.agent_name, "agent_communicator",
                        action="get_messages", limit=5
                    )
                    
                    if messages_result.success and messages_result.data.get("messages"):
                        await self._process_messages(messages_result.data["messages"])
                
                # Perform specialized tasks
                await self.execute_specialized_task({})
                
                # Adapt behavior periodically
                if datetime.utcnow().minute % 15 == 0:  # Every 15 minutes
                    await self.adapt_behavior()
                
                # Monitor performance
                if self.toolkit and datetime.utcnow().minute % 30 == 0:  # Every 30 minutes
                    await self.toolkit.execute_tool(
                        self.agent_name, "performance_monitor",
                        metric_type="agent"
                    )
                
                await asyncio.sleep(self.update_interval)
                
            except Exception as e:
                self.logger.error(f"Autonomous cycle error: {e}")
                await asyncio.sleep(5)
    
    async def _process_messages(self, messages: List[Dict[str, Any]]):
        """Process incoming messages from other agents"""
        for message in messages:
            try:
                message_type = message.get("message_type", "info")
                sender = message.get("sender", "unknown")
                content = message.get("message", "")
                
                if message_type == "collaboration_request":
                    await self._handle_collaboration_request(sender, content)
                elif message_type == "decision_validation":
                    await self._handle_validation_request(sender, content)
                elif message_type == "performance_update":
                    await self._handle_performance_update(sender, content)
                
                # Update trust score based on message quality
                if sender in self.trust_scores:
                    self.trust_scores[sender] = min(1.0, self.trust_scores[sender] + 0.01)
                
            except Exception as e:
                self.logger.error(f"Message processing error: {e}")
    
    async def _handle_collaboration_request(self, sender: str, content: str):
        """Handle collaboration request from peer agent"""
        try:
            request_data = json.loads(content)
            task = request_data.get("task", "")
            required_expertise = request_data.get("required_expertise", [])
            
            # Check if we can contribute
            can_contribute = any(expertise in [cap.category.value for cap in self.capabilities.values()] 
                               for expertise in required_expertise)
            
            if can_contribute and self.autonomy_level > 0.6:
                # Accept collaboration
                response = {
                    "collaboration_response": "accepted",
                    "agent": self.agent_name,
                    "capabilities": [cap.name for cap in self.capabilities.values() 
                                   if cap.proficiency_level > 0.6]
                }
                
                if self.toolkit:
                    await self.toolkit.execute_tool(
                        self.agent_name, "agent_communicator",
                        action="send_message",
                        recipient=sender,
                        message=json.dumps(response),
                        message_type="collaboration_response"
                    )
                
                self.logger.info(f"Accepted collaboration request from {sender} for task: {task}")
            
        except Exception as e:
            self.logger.error(f"Collaboration request handling failed: {e}")
    
    async def _handle_validation_request(self, sender: str, content: str):
        """Handle decision validation request from peer agent"""
        try:
            validation_data = json.loads(content)
            decision_summary = validation_data.get("decision_summary", {})
            
            # Provide validation based on our expertise
            validation_response = {
                "validator": self.agent_name,
                "validation": "approved" if decision_summary.get("confidence", 0) > 0.7 else "review_needed",
                "comments": f"Validation from {self.agent_name}",
                "trust_score": self.trust_scores.get(sender, 0.5)
            }
            
            if self.toolkit:
                await self.toolkit.execute_tool(
                    self.agent_name, "agent_communicator",
                    action="send_message",
                    recipient=sender,
                    message=json.dumps(validation_response),
                    message_type="validation_response"
                )
            
        except Exception as e:
            self.logger.error(f"Validation request handling failed: {e}")
    
    async def _handle_performance_update(self, sender: str, content: str):
        """Handle performance update from peer agent"""
        try:
            # Update trust score based on peer performance
            performance_data = json.loads(content)
            peer_success_rate = performance_data.get("success_rate", 0.5)
            
            if sender in self.trust_scores:
                # Adjust trust based on peer performance
                if peer_success_rate > 0.8:
                    self.trust_scores[sender] = min(1.0, self.trust_scores[sender] + 0.05)
                elif peer_success_rate < 0.6:
                    self.trust_scores[sender] = max(0.1, self.trust_scores[sender] - 0.02)
            
        except Exception as e:
            self.logger.error(f"Performance update handling failed: {e}")