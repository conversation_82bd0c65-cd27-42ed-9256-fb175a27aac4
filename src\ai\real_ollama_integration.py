"""
REAL OLLAMA AI INTEGRATION
Actual LLM-powered trading analysis using loaded Ollama models
"""

import asyncio
import aiohttp
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
import numpy as np

logger = logging.getLogger(__name__)


@dataclass
class OllamaAnalysis:
    """Real Ollama AI analysis result"""
    model_name: str
    symbol: str
    analysis_text: str
    recommendation: str  # BUY, SELL, HOLD
    confidence: float
    reasoning: str
    timestamp: datetime
    processing_time: float


class RealOllamaAI:
    """Real Ollama AI integration for trading analysis"""
    
    def __init__(self, base_url: str = "http://localhost:11434"):
        self.base_url = base_url
        self.session = None
        self.available_models = []
        self.logger = logging.getLogger(f"{__name__}.RealOllamaAI")
        
    async def initialize(self):
        """Initialize Ollama connection and check available models"""
        try:
            self.session = aiohttp.ClientSession()
            
            # Check Ollama status
            async with self.session.get(f"{self.base_url}/api/tags") as response:
                if response.status == 200:
                    data = await response.json()
                    self.available_models = [model["name"] for model in data.get("models", [])]
                    self.logger.info(f"✅ Ollama connected - {len(self.available_models)} models available")
                    return True
                else:
                    self.logger.error(f"❌ Ollama connection failed: {response.status}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Ollama: {e}")
            return False
    
    async def analyze_market_with_ai(self, model_name: str, symbol: str, market_data: Dict[str, Any]) -> Optional[OllamaAnalysis]:
        """Perform real AI analysis using Ollama model"""
        try:
            start_time = time.time()
            
            # Create detailed market analysis prompt
            prompt = self._create_market_analysis_prompt(symbol, market_data)
            
            # Make real API call to Ollama
            analysis_text = await self._call_ollama_model(model_name, prompt)
            
            if analysis_text:
                # Parse AI response
                recommendation, confidence, reasoning = self._parse_ai_response(analysis_text)
                
                processing_time = time.time() - start_time
                
                analysis = OllamaAnalysis(
                    model_name=model_name,
                    symbol=symbol,
                    analysis_text=analysis_text,
                    recommendation=recommendation,
                    confidence=confidence,
                    reasoning=reasoning,
                    timestamp=datetime.now(),
                    processing_time=processing_time
                )
                
                self.logger.info(f"🤖 {model_name} analyzed {symbol}: {recommendation} "
                               f"(confidence: {confidence:.2f}, time: {processing_time:.2f}s)")
                
                return analysis
            else:
                self.logger.error(f"❌ No response from {model_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error in AI analysis: {e}")
            return None
    
    def _create_market_analysis_prompt(self, symbol: str, market_data: Dict[str, Any]) -> str:
        """Create detailed market analysis prompt for AI"""
        price = market_data.get("price", 0)
        volume = market_data.get("volume", 0)
        bid = market_data.get("bid", price * 0.999)
        ask = market_data.get("ask", price * 1.001)
        source = market_data.get("source", "UNKNOWN")
        
        prompt = f"""
You are an expert cryptocurrency trading analyst. Analyze the following market data for {symbol}:

MARKET DATA:
- Current Price: ${price:.4f}
- Volume: {volume:,.0f}
- Bid: ${bid:.4f}
- Ask: ${ask:.4f}
- Spread: {((ask - bid) / price * 100):.3f}%
- Data Source: {source}

ANALYSIS REQUIRED:
1. Technical Analysis: Price action, support/resistance levels
2. Market Sentiment: Based on volume and spread
3. Risk Assessment: Volatility and liquidity analysis
4. Trading Recommendation: BUY, SELL, or HOLD

Provide your analysis in this format:
RECOMMENDATION: [BUY/SELL/HOLD]
CONFIDENCE: [0.0-1.0]
REASONING: [Your detailed analysis]

Be specific, analytical, and provide clear reasoning for your recommendation.
"""
        return prompt
    
    async def _call_ollama_model(self, model_name: str, prompt: str) -> Optional[str]:
        """Make actual API call to Ollama model"""
        try:
            payload = {
                "model": model_name,
                "prompt": prompt,
                "stream": False,
                "options": {
                    "temperature": 0.7,
                    "top_p": 0.9,
                    "max_tokens": 500
                }
            }
            
            async with self.session.post(
                f"{self.base_url}/api/generate",
                json=payload,
                timeout=aiohttp.ClientTimeout(total=30)
            ) as response:
                if response.status == 200:
                    data = await response.json()
                    return data.get("response", "").strip()
                else:
                    self.logger.error(f"❌ Ollama API error: {response.status}")
                    return None
                    
        except asyncio.TimeoutError:
            self.logger.error(f"❌ Timeout calling {model_name}")
            return None
        except Exception as e:
            self.logger.error(f"❌ Error calling {model_name}: {e}")
            return None
    
    def _parse_ai_response(self, response_text: str) -> tuple[str, float, str]:
        """Parse AI response to extract recommendation, confidence, and reasoning"""
        try:
            lines = response_text.split('\n')
            recommendation = "HOLD"
            confidence = 0.5
            reasoning = response_text
            
            for line in lines:
                line = line.strip()
                if line.startswith("RECOMMENDATION:"):
                    rec = line.split(":", 1)[1].strip().upper()
                    if rec in ["BUY", "SELL", "HOLD"]:
                        recommendation = rec
                elif line.startswith("CONFIDENCE:"):
                    try:
                        conf_str = line.split(":", 1)[1].strip()
                        confidence = float(conf_str)
                        confidence = max(0.0, min(1.0, confidence))  # Clamp to [0,1]
                    except:
                        pass
                elif line.startswith("REASONING:"):
                    reasoning = line.split(":", 1)[1].strip()
            
            return recommendation, confidence, reasoning
            
        except Exception as e:
            self.logger.error(f"❌ Error parsing AI response: {e}")
            return "HOLD", 0.5, response_text
    
    async def close(self):
        """Close Ollama connection"""
        if self.session:
            await self.session.close()


class RealOllamaAIManager:
    """Manager for multiple Ollama AI models"""
    
    def __init__(self):
        self.ollama_ai = RealOllamaAI()
        self.active_models = []
        self.analysis_history = []
        self.logger = logging.getLogger(f"{__name__}.RealOllamaAIManager")
        
    async def initialize(self):
        """Initialize Ollama AI manager"""
        try:
            success = await self.ollama_ai.initialize()
            if success:
                # Check which models are currently loaded
                await self._check_loaded_models()
                return True
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize AI manager: {e}")
            return False
    
    async def _check_loaded_models(self):
        """Check which models are currently loaded in Ollama"""
        try:
            async with self.ollama_ai.session.get(f"{self.ollama_ai.base_url}/api/ps") as response:
                if response.status == 200:
                    data = await response.json()
                    self.active_models = [model["name"] for model in data.get("models", [])]
                    self.logger.info(f"🤖 Active models: {self.active_models}")
                else:
                    self.logger.warning("⚠️ Could not check loaded models")
                    
        except Exception as e:
            self.logger.error(f"❌ Error checking loaded models: {e}")
    
    async def analyze_symbol_with_all_models(self, symbol: str, market_data: Dict[str, Any]) -> List[OllamaAnalysis]:
        """Analyze symbol with all available AI models"""
        try:
            analyses = []
            
            # Use priority models if available
            priority_models = ["phi4-reasoning:plus", "nemotron-mini:4b", "deepseek-r1:32b"]
            models_to_use = []
            
            # Check which priority models are available
            for model in priority_models:
                if model in self.ollama_ai.available_models:
                    models_to_use.append(model)
            
            # If no priority models, use any available
            if not models_to_use and self.ollama_ai.available_models:
                models_to_use = self.ollama_ai.available_models[:3]  # Use first 3
            
            if not models_to_use:
                self.logger.warning("⚠️ No Ollama models available")
                return []
            
            # Analyze with each model
            for model_name in models_to_use:
                analysis = await self.ollama_ai.analyze_market_with_ai(model_name, symbol, market_data)
                if analysis:
                    analyses.append(analysis)
                    self.analysis_history.append(analysis)
                    
                    # Keep history manageable
                    if len(self.analysis_history) > 1000:
                        self.analysis_history = self.analysis_history[-1000:]
            
            return analyses
            
        except Exception as e:
            self.logger.error(f"❌ Error in multi-model analysis: {e}")
            return []
    
    async def get_consensus_analysis(self, symbol: str, market_data: Dict[str, Any]) -> Optional[OllamaAnalysis]:
        """Get consensus analysis from multiple AI models"""
        try:
            analyses = await self.analyze_symbol_with_all_models(symbol, market_data)
            
            if not analyses:
                return None
            
            # Calculate consensus
            buy_votes = sum(1 for a in analyses if a.recommendation == "BUY")
            sell_votes = sum(1 for a in analyses if a.recommendation == "SELL")
            hold_votes = sum(1 for a in analyses if a.recommendation == "HOLD")
            
            # Determine consensus recommendation
            if buy_votes > sell_votes and buy_votes > hold_votes:
                consensus_rec = "BUY"
            elif sell_votes > buy_votes and sell_votes > hold_votes:
                consensus_rec = "SELL"
            else:
                consensus_rec = "HOLD"
            
            # Calculate weighted confidence
            total_confidence = sum(a.confidence for a in analyses)
            avg_confidence = total_confidence / len(analyses)
            
            # Create consensus reasoning
            reasoning_parts = []
            for analysis in analyses:
                reasoning_parts.append(f"{analysis.model_name}: {analysis.recommendation} ({analysis.confidence:.2f})")
            
            consensus_reasoning = f"Consensus from {len(analyses)} AI models: " + "; ".join(reasoning_parts)
            
            consensus = OllamaAnalysis(
                model_name="CONSENSUS",
                symbol=symbol,
                analysis_text=f"Multi-model consensus analysis",
                recommendation=consensus_rec,
                confidence=avg_confidence,
                reasoning=consensus_reasoning,
                timestamp=datetime.now(),
                processing_time=sum(a.processing_time for a in analyses)
            )
            
            self.logger.info(f"🎯 CONSENSUS for {symbol}: {consensus_rec} "
                           f"(confidence: {avg_confidence:.2f}, models: {len(analyses)})")
            
            return consensus
            
        except Exception as e:
            self.logger.error(f"❌ Error in consensus analysis: {e}")
            return None
    
    async def close(self):
        """Close AI manager"""
        await self.ollama_ai.close()


# Global instance
real_ollama_manager = RealOllamaAIManager()
