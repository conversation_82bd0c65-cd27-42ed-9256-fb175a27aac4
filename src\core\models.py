#!/usr/bin/env python3
"""
Core data models for the Noryon V2 trading system
"""

from dataclasses import dataclass
from datetime import datetime
from enum import Enum
from typing import Dict, List, Optional, Any, Union
from decimal import Decimal


class MessageType(Enum):
    """Types of messages that can be sent between agents"""
    ANALYSIS = "analysis"
    SIGNAL = "signal"
    ALERT = "alert"
    STATUS = "status"
    REQUEST = "request"
    RESPONSE = "response"


class AlertLevel(Enum):
    """Alert severity levels"""
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"
    EMERGENCY = "emergency"


class MarketEventType(Enum):
    """Types of market events"""
    NEWS = "news"
    EARNINGS = "earnings"
    ECONOMIC_DATA = "economic_data"
    REGULATORY = "regulatory"
    TECHNICAL = "technical"
    SOCIAL_SENTIMENT = "social_sentiment"


@dataclass
class AgentMessage:
    """Base message structure for inter-agent communication"""
    sender: str
    recipient: str
    message_type: MessageType
    content: Dict[str, Any]
    timestamp: datetime
    priority: int = 1
    correlation_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'sender': self.sender,
            'recipient': self.recipient,
            'message_type': self.message_type.value,
            'content': self.content,
            'timestamp': self.timestamp.isoformat(),
            'priority': self.priority,
            'correlation_id': self.correlation_id
        }


@dataclass
class NewsAlert:
    """News alert data structure"""
    title: str
    content: str
    source: str
    url: str
    timestamp: datetime
    sentiment_score: float
    impact_score: float
    symbols: List[str]
    categories: List[str]
    alert_level: AlertLevel
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'title': self.title,
            'content': self.content,
            'source': self.source,
            'url': self.url,
            'timestamp': self.timestamp.isoformat(),
            'sentiment_score': self.sentiment_score,
            'impact_score': self.impact_score,
            'symbols': self.symbols,
            'categories': self.categories,
            'alert_level': self.alert_level.value
        }


@dataclass
class MarketEvent:
    """Market event data structure"""
    event_id: str
    event_type: MarketEventType
    title: str
    description: str
    timestamp: datetime
    symbols: List[str]
    impact_score: float
    confidence: float
    metadata: Dict[str, Any]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'event_id': self.event_id,
            'event_type': self.event_type.value,
            'title': self.title,
            'description': self.description,
            'timestamp': self.timestamp.isoformat(),
            'symbols': self.symbols,
            'impact_score': self.impact_score,
            'confidence': self.confidence,
            'metadata': self.metadata
        }


@dataclass
class TradingSignal:
    """Trading signal data structure"""
    signal_id: str
    symbol: str
    action: str  # 'BUY', 'SELL', 'HOLD'
    confidence: float
    price_target: Optional[Decimal]
    stop_loss: Optional[Decimal]
    take_profit: Optional[Decimal]
    quantity: Optional[Decimal]
    timeframe: str
    reasoning: str
    timestamp: datetime
    agent_source: str
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'signal_id': self.signal_id,
            'symbol': self.symbol,
            'action': self.action,
            'confidence': self.confidence,
            'price_target': float(self.price_target) if self.price_target else None,
            'stop_loss': float(self.stop_loss) if self.stop_loss else None,
            'take_profit': float(self.take_profit) if self.take_profit else None,
            'quantity': float(self.quantity) if self.quantity else None,
            'timeframe': self.timeframe,
            'reasoning': self.reasoning,
            'timestamp': self.timestamp.isoformat(),
            'agent_source': self.agent_source
        }


@dataclass
class AnalysisResult:
    """Analysis result data structure"""
    analysis_id: str
    analyst: str
    symbol: str
    analysis_type: str
    result: Dict[str, Any]
    confidence: float
    timestamp: datetime
    recommendations: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'analysis_id': self.analysis_id,
            'analyst': self.analyst,
            'symbol': self.symbol,
            'analysis_type': self.analysis_type,
            'result': self.result,
            'confidence': self.confidence,
            'timestamp': self.timestamp.isoformat(),
            'recommendations': self.recommendations
        }


@dataclass
class TechnicalSignal:
    """Technical analysis signal data structure"""
    signal_id: str
    symbol: str
    timeframe: str
    signal_type: str
    direction: str  # 'bullish', 'bearish', 'neutral'
    strength: int  # 1-5 scale
    confidence: float
    entry_price: float
    stop_loss: Optional[float]
    take_profit: Optional[float]
    indicators: List[str]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'signal_id': self.signal_id,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'signal_type': self.signal_type,
            'direction': self.direction,
            'strength': self.strength,
            'confidence': self.confidence,
            'entry_price': self.entry_price,
            'stop_loss': self.stop_loss,
            'take_profit': self.take_profit,
            'indicators': self.indicators,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class TradeOrder:
    """Trade order data structure"""
    order_id: str
    symbol: str
    order_type: str  # 'market', 'limit', 'stop', 'stop_limit'
    side: str  # 'buy', 'sell'
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = 'GTC'  # 'GTC', 'IOC', 'FOK', 'DAY'
    status: str = 'pending'  # 'pending', 'submitted', 'filled', 'cancelled', 'rejected'
    filled_quantity: float = 0.0
    average_fill_price: Optional[float] = None
    commission: float = 0.0
    timestamp: datetime = None
    execution_timestamp: Optional[datetime] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'order_id': self.order_id,
            'symbol': self.symbol,
            'order_type': self.order_type,
            'side': self.side,
            'quantity': self.quantity,
            'price': self.price,
            'stop_price': self.stop_price,
            'time_in_force': self.time_in_force,
            'status': self.status,
            'filled_quantity': self.filled_quantity,
            'average_fill_price': self.average_fill_price,
            'commission': self.commission,
            'timestamp': self.timestamp.isoformat() if self.timestamp else None,
            'execution_timestamp': self.execution_timestamp.isoformat() if self.execution_timestamp else None,
            'metadata': self.metadata or {}
        }


@dataclass
class ExecutionReport:
    """Trade execution report data structure"""
    report_id: str
    order_id: str
    symbol: str
    side: str
    executed_quantity: float
    execution_price: float
    execution_time: datetime
    commission: float
    fees: float
    execution_venue: str
    liquidity_flag: str  # 'maker', 'taker'
    slippage: float
    market_impact: float
    execution_quality: float  # 0-1 score
    notes: Optional[str] = None
    metadata: Optional[Dict[str, Any]] = None
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'report_id': self.report_id,
            'order_id': self.order_id,
            'symbol': self.symbol,
            'side': self.side,
            'executed_quantity': self.executed_quantity,
            'execution_price': self.execution_price,
            'execution_time': self.execution_time.isoformat(),
            'commission': self.commission,
            'fees': self.fees,
            'execution_venue': self.execution_venue,
            'liquidity_flag': self.liquidity_flag,
            'slippage': self.slippage,
            'market_impact': self.market_impact,
            'execution_quality': self.execution_quality,
            'notes': self.notes,
            'metadata': self.metadata or {}
        }


@dataclass
class ChartPattern:
    """Chart pattern data structure"""
    pattern_id: str
    symbol: str
    pattern_type: str
    timeframe: str
    start_time: datetime
    end_time: Optional[datetime]
    confidence: float
    target_price: Optional[float]
    breakout_price: Optional[float]
    support_levels: List[float]
    resistance_levels: List[float]
    volume_confirmation: bool
    status: str  # 'forming', 'completed', 'broken'
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'pattern_id': self.pattern_id,
            'symbol': self.symbol,
            'pattern_type': self.pattern_type,
            'timeframe': self.timeframe,
            'start_time': self.start_time.isoformat(),
            'end_time': self.end_time.isoformat() if self.end_time else None,
            'confidence': self.confidence,
            'target_price': self.target_price,
            'breakout_price': self.breakout_price,
            'support_levels': self.support_levels,
            'resistance_levels': self.resistance_levels,
            'volume_confirmation': self.volume_confirmation,
            'status': self.status
        }


@dataclass
class MarketAnalysis:
    """Market analysis data structure"""
    analysis_id: str
    symbol: str
    timeframe: str
    analysis_type: str
    timestamp: datetime
    confidence: float
    trend_direction: str
    strength: float
    key_levels: List[float]
    indicators: Dict[str, Any]
    summary: str
    recommendations: List[str]
    risk_factors: List[str]
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'analysis_id': self.analysis_id,
            'symbol': self.symbol,
            'timeframe': self.timeframe,
            'analysis_type': self.analysis_type,
            'timestamp': self.timestamp.isoformat(),
            'confidence': self.confidence,
            'trend_direction': self.trend_direction,
            'strength': self.strength,
            'key_levels': self.key_levels,
            'indicators': self.indicators,
            'summary': self.summary,
            'recommendations': self.recommendations,
            'risk_factors': self.risk_factors
        }


@dataclass
class StrategicRecommendation:
    """Strategic recommendation data structure"""
    recommendation_id: str
    symbol: str
    action: str  # 'buy', 'sell', 'hold', 'watch'
    priority: str  # 'high', 'medium', 'low'
    confidence: float
    timeframe: str
    entry_price: Optional[float]
    target_price: Optional[float]
    stop_loss: Optional[float]
    position_size: Optional[float]
    reasoning: str
    supporting_analysis: List[str]
    risk_assessment: Dict[str, Any]
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'recommendation_id': self.recommendation_id,
            'symbol': self.symbol,
            'action': self.action,
            'priority': self.priority,
            'confidence': self.confidence,
            'timeframe': self.timeframe,
            'entry_price': self.entry_price,
            'target_price': self.target_price,
            'stop_loss': self.stop_loss,
            'position_size': self.position_size,
            'reasoning': self.reasoning,
            'supporting_analysis': self.supporting_analysis,
            'risk_assessment': self.risk_assessment,
            'timestamp': self.timestamp.isoformat()
        }


@dataclass
class RiskAlert:
    """Risk alert data structure"""
    alert_id: str
    alert_type: str  # 'position_risk', 'portfolio_risk', 'market_risk', 'liquidity_risk'
    severity: AlertLevel
    symbol: Optional[str]
    message: str
    current_value: float
    threshold_value: float
    risk_score: float
    recommendations: List[str]
    timestamp: datetime
    acknowledged: bool = False
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'alert_id': self.alert_id,
            'alert_type': self.alert_type,
            'severity': self.severity.value,
            'symbol': self.symbol,
            'message': self.message,
            'current_value': self.current_value,
            'threshold_value': self.threshold_value,
            'risk_score': self.risk_score,
            'recommendations': self.recommendations,
            'timestamp': self.timestamp.isoformat(),
            'acknowledged': self.acknowledged
        }


@dataclass
class RiskMetrics:
    """Risk metrics data structure"""
    metrics_id: str
    symbol: Optional[str]
    portfolio_var: float  # Value at Risk
    portfolio_cvar: float  # Conditional Value at Risk
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    beta: float
    alpha: float
    volatility: float
    correlation_matrix: Dict[str, Dict[str, float]]
    position_sizes: Dict[str, float]
    concentration_risk: float
    liquidity_risk: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'metrics_id': self.metrics_id,
            'symbol': self.symbol,
            'portfolio_var': self.portfolio_var,
            'portfolio_cvar': self.portfolio_cvar,
            'sharpe_ratio': self.sharpe_ratio,
            'sortino_ratio': self.sortino_ratio,
            'max_drawdown': self.max_drawdown,
            'beta': self.beta,
            'alpha': self.alpha,
            'volatility': self.volatility,
            'correlation_matrix': self.correlation_matrix,
            'position_sizes': self.position_sizes,
            'concentration_risk': self.concentration_risk,
            'liquidity_risk': self.liquidity_risk,
            'timestamp': self.timestamp.isoformat()
        }