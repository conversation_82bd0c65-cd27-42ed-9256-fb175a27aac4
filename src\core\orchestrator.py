"""
Noryon V2 - System Orchestrator
Central coordination system for all AI agents and trading operations
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from decimal import Decimal

from src.core.config import Config
from src.core.logger import get_logger, get_performance_logger, get_trading_logger
from src.db.database_manager import DatabaseManager
from src.exchanges.exchange_manager import ExchangeManager

class AgentStatus(Enum):
    INITIALIZING = "initializing"
    ACTIVE = "active"
    IDLE = "idle"
    ERROR = "error"
    STOPPED = "stopped"

class TradingMode(Enum):
    PAPER = "paper"
    LIVE = "live"
    SIMULATION = "simulation"

@dataclass
class AgentMessage:
    """Message structure for inter-agent communication"""
    source_agent: str
    target_agent: str
    message_type: str
    content: Dict[str, Any]
    timestamp: datetime
    priority: int = 1  # 1=low, 2=medium, 3=high, 4=critical
    requires_response: bool = False
    correlation_id: Optional[str] = None

@dataclass
class TradingSignal:
    """Trading signal structure"""
    symbol: str
    action: str  # buy, sell, hold
    strength: float  # 0.0 to 1.0
    price_target: Optional[float]
    stop_loss: Optional[float]
    take_profit: Optional[float]
    strategy: str
    agent_source: str
    timestamp: datetime
    confidence: float
    reasoning: str
    risk_score: float

@dataclass
class MarketData:
    """Market data structure"""
    symbol: str
    price: float
    volume: float
    timestamp: datetime
    bid: Optional[float] = None
    ask: Optional[float] = None
    high_24h: Optional[float] = None
    low_24h: Optional[float] = None
    change_24h: Optional[float] = None

class SystemOrchestrator:
    """Central orchestrator for the entire trading system"""
    
    def __init__(self, agents: Dict[str, Any], db_manager: DatabaseManager, 
                 exchange_manager: ExchangeManager, config: Config):
        self.agents = agents
        self.db_manager = db_manager
        self.exchange_manager = exchange_manager
        self.config = config
        
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger("orchestrator")
        self.trading_logger = get_trading_logger("orchestrator")
        
        self.running = False
        self.trading_mode = TradingMode.PAPER if config.PAPER_TRADING else TradingMode.LIVE
        
        # Communication system
        self.message_queue: asyncio.Queue = asyncio.Queue()
        self.agent_status: Dict[str, AgentStatus] = {}
        self.agent_last_heartbeat: Dict[str, datetime] = {}
        
        # Trading state
        self.active_signals: Dict[str, List[TradingSignal]] = {}
        self.pending_orders: Dict[str, Any] = {}
        self.portfolio_state: Dict[str, Any] = {}
        self.risk_metrics: Dict[str, Any] = {}
        
        # Market data cache
        self.market_data_cache: Dict[str, MarketData] = {}
        self.last_market_update: Dict[str, datetime] = {}
        
        # Performance tracking
        self.system_metrics: Dict[str, Any] = {
            "trades_executed": 0,
            "signals_generated": 0,
            "decisions_made": 0,
            "errors_encountered": 0,
            "uptime_start": datetime.utcnow()
        }
        
        # Task management
        self.background_tasks: List[asyncio.Task] = []
        
    async def initialize(self):
        """Initialize the orchestrator"""
        try:
            self.logger.info("🎯 Initializing System Orchestrator...")
            
            # Initialize agent status tracking
            for agent_name in self.agents.keys():
                self.agent_status[agent_name] = AgentStatus.INITIALIZING
                self.agent_last_heartbeat[agent_name] = datetime.utcnow()
                self.active_signals[agent_name] = []
            
            # Initialize portfolio state
            await self._initialize_portfolio_state()
            
            # Initialize risk metrics
            await self._initialize_risk_metrics()
            
            self.logger.info("✅ System Orchestrator initialized successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize orchestrator: {e}")
            raise
    
    async def start(self):
        """Start the orchestrator and all background tasks"""
        try:
            self.running = True
            self.logger.info("🚀 Starting System Orchestrator...")
            
            # Start background tasks
            self.background_tasks = [
                asyncio.create_task(self._message_processor()),
                asyncio.create_task(self._agent_health_monitor()),
                asyncio.create_task(self._market_data_processor()),
                asyncio.create_task(self._trading_decision_engine()),
                asyncio.create_task(self._risk_monitor()),
                asyncio.create_task(self._performance_tracker()),
                asyncio.create_task(self._portfolio_manager()),
                asyncio.create_task(self._system_health_monitor())
            ]
            
            # Mark all agents as active
            for agent_name in self.agents.keys():
                self.agent_status[agent_name] = AgentStatus.ACTIVE
            
            self.logger.info("✅ System Orchestrator started successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start orchestrator: {e}")
            raise
    
    async def stop(self):
        """Stop the orchestrator and all background tasks"""
        self.logger.info("🛑 Stopping System Orchestrator...")
        self.running = False
        
        # Cancel all background tasks
        for task in self.background_tasks:
            task.cancel()
        
        # Wait for tasks to complete
        if self.background_tasks:
            await asyncio.gather(*self.background_tasks, return_exceptions=True)
        
        # Mark all agents as stopped
        for agent_name in self.agents.keys():
            self.agent_status[agent_name] = AgentStatus.STOPPED
        
        self.logger.info("✅ System Orchestrator stopped")
    
    async def send_message(self, message: AgentMessage):
        """Send a message between agents"""
        await self.message_queue.put(message)
        
        self.logger.debug(
            f"Message queued: {message.source_agent} -> {message.target_agent} ({message.message_type})",
            extra={
                "source": message.source_agent,
                "target": message.target_agent,
                "type": message.message_type,
                "priority": message.priority
            }
        )
    
    async def submit_trading_signal(self, signal: TradingSignal):
        """Submit a trading signal for processing"""
        self.active_signals[signal.agent_source].append(signal)
        self.system_metrics["signals_generated"] += 1
        
        self.trading_logger.log_signal(
            signal_type=signal.action,
            symbol=signal.symbol,
            strength=signal.strength,
            source=signal.agent_source,
            confidence=signal.confidence,
            strategy=signal.strategy,
            reasoning=signal.reasoning
        )
        
        # Notify relevant agents
        await self.send_message(AgentMessage(
            source_agent="orchestrator",
            target_agent="risk_manager",
            message_type="signal_evaluation",
            content=signal.__dict__,
            timestamp=datetime.utcnow(),
            priority=3
        ))
    
    async def get_market_data(self, symbol: str) -> Optional[MarketData]:
        """Get cached market data for a symbol"""
        return self.market_data_cache.get(symbol)
    
    async def get_portfolio_state(self) -> Dict[str, Any]:
        """Get current portfolio state"""
        return self.portfolio_state.copy()
    
    async def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        uptime = datetime.utcnow() - self.system_metrics["uptime_start"]
        
        return {
            "status": "running" if self.running else "stopped",
            "trading_mode": self.trading_mode.value,
            "uptime_seconds": uptime.total_seconds(),
            "agents": {
                name: {
                    "status": status.value,
                    "last_heartbeat": self.agent_last_heartbeat.get(name),
                    "active_signals": len(self.active_signals.get(name, []))
                }
                for name, status in self.agent_status.items()
            },
            "metrics": self.system_metrics,
            "portfolio": self.portfolio_state,
            "risk_metrics": self.risk_metrics,
            "market_data_symbols": len(self.market_data_cache),
            "pending_orders": len(self.pending_orders)
        }
    
    # Background Task Methods
    
    async def _message_processor(self):
        """Process inter-agent messages"""
        while self.running:
            try:
                # Get message with timeout
                message = await asyncio.wait_for(
                    self.message_queue.get(), 
                    timeout=1.0
                )
                
                await self._handle_message(message)
                
            except asyncio.TimeoutError:
                continue
            except Exception as e:
                self.logger.error(f"Error processing message: {e}")
                self.system_metrics["errors_encountered"] += 1
    
    async def _handle_message(self, message: AgentMessage):
        """Handle a specific message"""
        try:
            target_agent = self.agents.get(message.target_agent)
            if not target_agent:
                self.logger.warning(f"Target agent not found: {message.target_agent}")
                return
            
            # Route message based on type
            if message.message_type == "market_update":
                await self._handle_market_update(message)
            elif message.message_type == "trading_signal":
                await self._handle_trading_signal(message)
            elif message.message_type == "risk_alert":
                await self._handle_risk_alert(message)
            elif message.message_type == "portfolio_update":
                await self._handle_portfolio_update(message)
            else:
                # Forward to target agent
                if hasattr(target_agent, 'receive_message'):
                    await target_agent.receive_message(message)
            
        except Exception as e:
            self.logger.error(f"Error handling message: {e}")
    
    async def _agent_health_monitor(self):
        """Monitor agent health and heartbeats"""
        while self.running:
            try:
                current_time = datetime.utcnow()
                
                for agent_name, last_heartbeat in self.agent_last_heartbeat.items():
                    time_since_heartbeat = current_time - last_heartbeat
                    
                    if time_since_heartbeat > timedelta(minutes=5):
                        if self.agent_status[agent_name] != AgentStatus.ERROR:
                            self.logger.warning(f"Agent {agent_name} appears unresponsive")
                            self.agent_status[agent_name] = AgentStatus.ERROR
                    elif self.agent_status[agent_name] == AgentStatus.ERROR:
                        self.logger.info(f"Agent {agent_name} recovered")
                        self.agent_status[agent_name] = AgentStatus.ACTIVE
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error in agent health monitor: {e}")
                await asyncio.sleep(30)
    
    async def _market_data_processor(self):
        """Process and cache market data"""
        while self.running:
            try:
                # Get market data from exchanges
                for symbol in self.config.TRADING_PAIRS:
                    try:
                        market_data = await self.exchange_manager.get_ticker(symbol)
                        if market_data:
                            self.market_data_cache[symbol] = MarketData(
                                symbol=symbol,
                                price=market_data.get('last', 0),
                                volume=market_data.get('volume', 0),
                                timestamp=datetime.utcnow(),
                                bid=market_data.get('bid'),
                                ask=market_data.get('ask'),
                                high_24h=market_data.get('high'),
                                low_24h=market_data.get('low'),
                                change_24h=market_data.get('change')
                            )
                            self.last_market_update[symbol] = datetime.utcnow()
                    except Exception as e:
                        self.logger.error(f"Error getting market data for {symbol}: {e}")
                
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Error in market data processor: {e}")
                await asyncio.sleep(5)
    
    async def _trading_decision_engine(self):
        """Main trading decision engine"""
        while self.running:
            try:
                # Collect signals from all agents
                all_signals = []
                for agent_signals in self.active_signals.values():
                    all_signals.extend(agent_signals)
                
                if not all_signals:
                    await asyncio.sleep(10)
                    continue
                
                # Group signals by symbol
                signals_by_symbol = {}
                for signal in all_signals:
                    if signal.symbol not in signals_by_symbol:
                        signals_by_symbol[signal.symbol] = []
                    signals_by_symbol[signal.symbol].append(signal)
                
                # Process each symbol
                for symbol, signals in signals_by_symbol.items():
                    await self._process_symbol_signals(symbol, signals)
                
                # Clear processed signals
                for agent_name in self.active_signals:
                    self.active_signals[agent_name] = []
                
                await asyncio.sleep(10)  # Process every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error in trading decision engine: {e}")
                await asyncio.sleep(10)
    
    async def _process_symbol_signals(self, symbol: str, signals: List[TradingSignal]):
        """Process trading signals for a specific symbol"""
        try:
            if not signals:
                return
            
            # Calculate consensus
            buy_signals = [s for s in signals if s.action == 'buy']
            sell_signals = [s for s in signals if s.action == 'sell']
            hold_signals = [s for s in signals if s.action == 'hold']
            
            # Weight signals by confidence and agent reliability
            buy_weight = sum(s.strength * s.confidence for s in buy_signals)
            sell_weight = sum(s.strength * s.confidence for s in sell_signals)
            
            # Make trading decision
            if buy_weight > sell_weight and buy_weight > 0.6:
                await self._execute_buy_decision(symbol, buy_signals)
            elif sell_weight > buy_weight and sell_weight > 0.6:
                await self._execute_sell_decision(symbol, sell_signals)
            
            self.system_metrics["decisions_made"] += 1
            
        except Exception as e:
            self.logger.error(f"Error processing signals for {symbol}: {e}")
    
    async def _execute_buy_decision(self, symbol: str, signals: List[TradingSignal]):
        """Execute a buy decision"""
        try:
            # Calculate position size based on risk management
            position_size = await self._calculate_position_size(symbol, 'buy', signals)
            
            if position_size <= 0:
                return
            
            # Get current market price
            market_data = self.market_data_cache.get(symbol)
            if not market_data:
                return
            
            # Calculate stop loss and take profit
            avg_stop_loss = sum(s.stop_loss for s in signals if s.stop_loss) / len([s for s in signals if s.stop_loss])
            avg_take_profit = sum(s.take_profit for s in signals if s.take_profit) / len([s for s in signals if s.take_profit])
            
            # Execute trade
            if self.trading_mode == TradingMode.PAPER:
                await self._execute_paper_trade(symbol, 'buy', position_size, market_data.price)
            else:
                await self._execute_live_trade(symbol, 'buy', position_size, market_data.price)
            
            self.trading_logger.log_trade(
                action='buy',
                symbol=symbol,
                quantity=position_size,
                price=market_data.price,
                exchange='aggregated',
                strategy='consensus',
                signals_count=len(signals),
                stop_loss=avg_stop_loss,
                take_profit=avg_take_profit
            )
            
        except Exception as e:
            self.logger.error(f"Error executing buy decision for {symbol}: {e}")
    
    async def _execute_sell_decision(self, symbol: str, signals: List[TradingSignal]):
        """Execute a sell decision"""
        try:
            # Check current position
            current_position = self.portfolio_state.get('positions', {}).get(symbol, 0)
            if current_position <= 0:
                return
            
            # Calculate sell quantity
            sell_quantity = min(current_position, await self._calculate_sell_quantity(symbol, signals))
            
            if sell_quantity <= 0:
                return
            
            # Get current market price
            market_data = self.market_data_cache.get(symbol)
            if not market_data:
                return
            
            # Execute trade
            if self.trading_mode == TradingMode.PAPER:
                await self._execute_paper_trade(symbol, 'sell', sell_quantity, market_data.price)
            else:
                await self._execute_live_trade(symbol, 'sell', sell_quantity, market_data.price)
            
            self.trading_logger.log_trade(
                action='sell',
                symbol=symbol,
                quantity=sell_quantity,
                price=market_data.price,
                exchange='aggregated',
                strategy='consensus',
                signals_count=len(signals)
            )
            
        except Exception as e:
            self.logger.error(f"Error executing sell decision for {symbol}: {e}")
    
    async def _risk_monitor(self):
        """Monitor risk metrics and portfolio health"""
        while self.running:
            try:
                await self._update_risk_metrics()
                await self._check_risk_limits()
                await asyncio.sleep(60)  # Check every minute
                
            except Exception as e:
                self.logger.error(f"Error in risk monitor: {e}")
                await asyncio.sleep(60)
    
    async def _performance_tracker(self):
        """Track system performance metrics"""
        while self.running:
            try:
                await self._update_performance_metrics()
                await asyncio.sleep(300)  # Update every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in performance tracker: {e}")
                await asyncio.sleep(300)
    
    async def _portfolio_manager(self):
        """Manage portfolio state and rebalancing"""
        while self.running:
            try:
                await self._update_portfolio_state()
                await self._check_rebalancing_needs()
                await asyncio.sleep(300)  # Check every 5 minutes
                
            except Exception as e:
                self.logger.error(f"Error in portfolio manager: {e}")
                await asyncio.sleep(300)
    
    async def _system_health_monitor(self):
        """Monitor overall system health"""
        while self.running:
            try:
                await self._check_system_health()
                await asyncio.sleep(120)  # Check every 2 minutes
                
            except Exception as e:
                self.logger.error(f"Error in system health monitor: {e}")
                await asyncio.sleep(120)
    
    # Helper Methods
    
    async def _initialize_portfolio_state(self):
        """Initialize portfolio state"""
        self.portfolio_state = {
            "total_value": float(self.config.INITIAL_BALANCE),
            "cash_balance": float(self.config.INITIAL_BALANCE),
            "positions": {},
            "pnl": 0.0,
            "daily_pnl": 0.0,
            "total_trades": 0,
            "winning_trades": 0,
            "losing_trades": 0
        }
    
    async def _initialize_risk_metrics(self):
        """Initialize risk metrics"""
        self.risk_metrics = {
            "portfolio_risk": 0.0,
            "var_95": 0.0,
            "max_drawdown": 0.0,
            "sharpe_ratio": 0.0,
            "beta": 0.0,
            "correlation_matrix": {},
            "risk_alerts": []
        }
    
    async def _calculate_position_size(self, symbol: str, action: str, signals: List[TradingSignal]) -> float:
        """Calculate appropriate position size"""
        # Implement position sizing logic based on risk management
        max_position_value = self.portfolio_state["total_value"] * float(self.config.MAX_POSITION_SIZE)
        
        # Get current price
        market_data = self.market_data_cache.get(symbol)
        if not market_data:
            return 0.0
        
        # Calculate position size
        position_size = max_position_value / market_data.price
        
        # Apply risk adjustments
        avg_risk_score = sum(s.risk_score for s in signals) / len(signals)
        position_size *= (1.0 - avg_risk_score)
        
        return max(0.0, position_size)
    
    async def _calculate_sell_quantity(self, symbol: str, signals: List[TradingSignal]) -> float:
        """Calculate sell quantity based on signals"""
        current_position = self.portfolio_state.get('positions', {}).get(symbol, 0)
        
        # Calculate sell percentage based on signal strength
        avg_strength = sum(s.strength for s in signals) / len(signals)
        sell_percentage = min(1.0, avg_strength)
        
        return current_position * sell_percentage
    
    async def _execute_paper_trade(self, symbol: str, action: str, quantity: float, price: float):
        """Execute a paper trade"""
        trade_value = quantity * price
        
        if action == 'buy':
            if self.portfolio_state["cash_balance"] >= trade_value:
                self.portfolio_state["cash_balance"] -= trade_value
                current_position = self.portfolio_state["positions"].get(symbol, 0)
                self.portfolio_state["positions"][symbol] = current_position + quantity
        elif action == 'sell':
            current_position = self.portfolio_state["positions"].get(symbol, 0)
            if current_position >= quantity:
                self.portfolio_state["positions"][symbol] = current_position - quantity
                self.portfolio_state["cash_balance"] += trade_value
        
        self.portfolio_state["total_trades"] += 1
        self.system_metrics["trades_executed"] += 1
    
    async def _execute_live_trade(self, symbol: str, action: str, quantity: float, price: float):
        """Execute a live trade"""
        try:
            if self.trading_mode == TradingMode.PAPER:
                # Paper trading simulation
                trade_id = f"paper_{datetime.utcnow().strftime('%Y%m%d_%H%M%S')}_{symbol}"
                trade_value = quantity * price
                
                if action.lower() == 'buy':
                    if self.portfolio_state["cash_balance"] >= trade_value:
                        self.portfolio_state["cash_balance"] -= trade_value
                        current_position = self.portfolio_state["positions"].get(symbol, 0)
                        self.portfolio_state["positions"][symbol] = current_position + quantity
                        
                        self.trading_logger.info(f"📈 PAPER BUY: {quantity} {symbol} @ ${price:.2f} (Total: ${trade_value:.2f})")
                        return {"success": True, "trade_id": trade_id, "type": "paper"}
                    else:
                        self.logger.warning(f"❌ Insufficient funds for {symbol} buy order")
                        return {"success": False, "error": "Insufficient funds"}
                        
                elif action.lower() == 'sell':
                    current_position = self.portfolio_state["positions"].get(symbol, 0)
                    if current_position >= quantity:
                        self.portfolio_state["positions"][symbol] = current_position - quantity
                        self.portfolio_state["cash_balance"] += trade_value
                        
                        self.trading_logger.info(f"📉 PAPER SELL: {quantity} {symbol} @ ${price:.2f} (Total: ${trade_value:.2f})")
                        return {"success": True, "trade_id": trade_id, "type": "paper"}
                    else:
                        self.logger.warning(f"❌ Insufficient {symbol} position for sell order")
                        return {"success": False, "error": "Insufficient position"}
            else:
                # Live trading through exchange manager
                order_result = await self.exchange_manager.create_order(
                    symbol=symbol,
                    order_type="market",
                    side=action,
                    amount=quantity,
                    price=price
                )
                
                if order_result and order_result.get("success"):
                    self.trading_logger.info(f"🔥 LIVE {action.upper()}: {quantity} {symbol} @ ${price:.2f}")
                    return order_result
                else:
                    self.logger.error(f"❌ Live trade failed for {symbol}")
                    return {"success": False, "error": "Exchange order failed"}
                    
        except Exception as e:
            self.logger.error(f"❌ Trade execution error: {e}")
            return {"success": False, "error": str(e)}
    
    async def _update_risk_metrics(self):
        """Update risk metrics"""
        try:
            total_value = self.portfolio_state.get("total_value", float(self.config.INITIAL_BALANCE))
            initial_balance = float(self.config.INITIAL_BALANCE)
            
            # Calculate portfolio risk metrics
            portfolio_risk = 0.0
            position_risks = {}
            
            for symbol, quantity in self.portfolio_state.get("positions", {}).items():
                if quantity > 0:
                    market_data = self.market_data_cache.get(symbol)
                    if market_data:
                        position_value = quantity * market_data.price
                        position_weight = position_value / total_value if total_value > 0 else 0
                        
                        # Simple risk calculation based on position size
                        position_risk = position_weight * 0.1  # Assume 10% volatility
                        position_risks[symbol] = position_risk
                        portfolio_risk += position_risk
            
            # Calculate drawdown
            peak_value = self.portfolio_state.get("peak_value", initial_balance)
            if total_value > peak_value:
                self.portfolio_state["peak_value"] = total_value
                peak_value = total_value
            
            drawdown = (peak_value - total_value) / peak_value if peak_value > 0 else 0
            
            self.risk_metrics.update({
                "portfolio_risk": portfolio_risk,
                "position_risks": position_risks,
                "drawdown": drawdown,
                "risk_score": min(portfolio_risk * 10, 10),  # Scale to 0-10
                "last_updated": datetime.utcnow()
            })
            
        except Exception as e:
            self.logger.error(f"❌ Risk metrics update error: {e}")
    
    async def _check_risk_limits(self):
        """Check if risk limits are exceeded"""
        try:
            max_drawdown = float(self.config.MAX_DRAWDOWN)
            max_portfolio_risk = float(self.config.MAX_PORTFOLIO_RISK)
            
            current_drawdown = self.risk_metrics.get("drawdown", 0)
            current_risk = self.risk_metrics.get("portfolio_risk", 0)
            
            risk_alerts = []
            
            if current_drawdown > max_drawdown:
                risk_alerts.append(f"⚠️ Drawdown limit exceeded: {current_drawdown:.2%} > {max_drawdown:.2%}")
                
            if current_risk > max_portfolio_risk:
                risk_alerts.append(f"⚠️ Portfolio risk limit exceeded: {current_risk:.2%} > {max_portfolio_risk:.2%}")
            
            # Check individual position sizes
            max_position_size = float(self.config.MAX_POSITION_SIZE)
            total_value = self.portfolio_state.get("total_value", float(self.config.INITIAL_BALANCE))
            
            for symbol, quantity in self.portfolio_state.get("positions", {}).items():
                if quantity > 0:
                    market_data = self.market_data_cache.get(symbol)
                    if market_data:
                        position_value = quantity * market_data.price
                        position_weight = position_value / total_value if total_value > 0 else 0
                        
                        if position_weight > max_position_size:
                            risk_alerts.append(f"⚠️ Position size limit exceeded for {symbol}: {position_weight:.2%} > {max_position_size:.2%}")
            
            if risk_alerts:
                for alert in risk_alerts:
                    self.logger.warning(alert)
                    
                # Send risk alert message
                alert_message = AgentMessage(
                    source_agent="orchestrator",
                    target_agent="risk_manager",
                    message_type="risk_alert",
                    content={"alerts": risk_alerts, "risk_metrics": self.risk_metrics},
                    timestamp=datetime.utcnow(),
                    priority=4  # Critical
                )
                await self.message_queue.put(alert_message)
                
            return len(risk_alerts) == 0
            
        except Exception as e:
            self.logger.error(f"❌ Risk limit check error: {e}")
            return True  # Assume safe if check fails
    
    async def _update_performance_metrics(self):
        """Update performance metrics"""
        try:
            total_value = self.portfolio_state.get("total_value", float(self.config.INITIAL_BALANCE))
            initial_balance = float(self.config.INITIAL_BALANCE)
            
            # Calculate basic performance metrics
            total_return = (total_value - initial_balance) / initial_balance
            
            # Store performance history
            if "performance_history" not in self.portfolio_state:
                self.portfolio_state["performance_history"] = []
            
            performance_entry = {
                "timestamp": datetime.utcnow(),
                "total_value": total_value,
                "total_return": total_return,
                "cash_balance": self.portfolio_state.get("cash_balance", initial_balance)
            }
            
            self.portfolio_state["performance_history"].append(performance_entry)
            
            # Keep only last 1000 entries
            if len(self.portfolio_state["performance_history"]) > 1000:
                self.portfolio_state["performance_history"] = self.portfolio_state["performance_history"][-1000:]
            
            # Calculate additional metrics
            self.portfolio_state.update({
                "total_return": total_return,
                "total_return_pct": total_return * 100,
                "last_performance_update": datetime.utcnow()
            })
            
        except Exception as e:
            self.logger.error(f"❌ Performance metrics update error: {e}")
    
    async def _update_portfolio_state(self):
        """Update portfolio state"""
        # Calculate current portfolio value
        total_value = self.portfolio_state["cash_balance"]
        
        for symbol, quantity in self.portfolio_state["positions"].items():
            market_data = self.market_data_cache.get(symbol)
            if market_data:
                total_value += quantity * market_data.price
        
        self.portfolio_state["total_value"] = total_value
        
        # Calculate PnL
        initial_balance = float(self.config.INITIAL_BALANCE)
        self.portfolio_state["pnl"] = total_value - initial_balance
    
    async def _check_rebalancing_needs(self):
        """Check if portfolio rebalancing is needed"""
        try:
            total_value = self.portfolio_state.get("total_value", float(self.config.INITIAL_BALANCE))
            positions = self.portfolio_state.get("positions", {})
            
            if total_value <= 0 or not positions:
                return False
            
            # Calculate current position weights
            position_weights = {}
            for symbol, quantity in positions.items():
                if quantity > 0:
                    market_data = self.market_data_cache.get(symbol)
                    if market_data:
                        position_value = quantity * market_data.price
                        position_weights[symbol] = position_value / total_value
            
            # Check if any position is significantly overweight
            max_position_size = float(self.config.MAX_POSITION_SIZE)
            rebalancing_needed = False
            
            for symbol, weight in position_weights.items():
                if weight > max_position_size * 1.2:  # 20% buffer
                    self.logger.info(f"🔄 Rebalancing needed for {symbol}: {weight:.2%} > {max_position_size:.2%}")
                    rebalancing_needed = True
            
            # Check if portfolio is too concentrated (top 3 positions > 80%)
            sorted_weights = sorted(position_weights.values(), reverse=True)
            if len(sorted_weights) >= 3 and sum(sorted_weights[:3]) > 0.8:
                self.logger.info("🔄 Portfolio concentration rebalancing needed")
                rebalancing_needed = True
            
            if rebalancing_needed:
                # Send rebalancing signal
                rebalance_message = AgentMessage(
                    source_agent="orchestrator",
                    target_agent="portfolio_manager",
                    message_type="rebalance_request",
                    content={
                        "position_weights": position_weights,
                        "max_position_size": max_position_size,
                        "reason": "position_size_exceeded"
                    },
                    timestamp=datetime.utcnow(),
                    priority=3
                )
                await self.message_queue.put(rebalance_message)
            
            return rebalancing_needed
            
        except Exception as e:
            self.logger.error(f"❌ Rebalancing check error: {e}")
            return False
    
    async def _check_system_health(self):
        """Check overall system health"""
        try:
            health_status = {
                "timestamp": datetime.utcnow(),
                "overall_status": "healthy",
                "components": {},
                "issues": []
            }
            
            # Check agent statuses
            inactive_agents = []
            for agent_id, status in self.agent_status.items():
                if status != AgentStatus.ACTIVE:
                    inactive_agents.append(agent_id)
                    health_status["components"][agent_id] = "inactive"
                else:
                    health_status["components"][agent_id] = "active"
            
            if inactive_agents:
                health_status["issues"].append(f"Inactive agents: {', '.join(inactive_agents)}")
                health_status["overall_status"] = "degraded"
            
            # Check message queue size
            queue_size = self.message_queue.qsize()
            if queue_size > 100:
                health_status["issues"].append(f"High message queue size: {queue_size}")
                health_status["overall_status"] = "degraded"
            
            health_status["components"]["message_queue"] = f"size_{queue_size}"
            
            # Check active signals count
            active_signals_count = len(self.active_signals)
            if active_signals_count > 50:
                health_status["issues"].append(f"Too many active signals: {active_signals_count}")
                health_status["overall_status"] = "degraded"
            
            health_status["components"]["active_signals"] = f"count_{active_signals_count}"
            
            # Check pending orders
            pending_orders_count = len(self.pending_orders)
            if pending_orders_count > 20:
                health_status["issues"].append(f"Too many pending orders: {pending_orders_count}")
                health_status["overall_status"] = "degraded"
            
            health_status["components"]["pending_orders"] = f"count_{pending_orders_count}"
            
            # Check risk metrics
            risk_score = self.risk_metrics.get("risk_score", 0)
            if risk_score > 8:
                health_status["issues"].append(f"High risk score: {risk_score}")
                health_status["overall_status"] = "warning"
            
            health_status["components"]["risk_score"] = risk_score
            
            # Log health status
            if health_status["overall_status"] == "healthy":
                self.logger.info("✅ System health check: All systems operational")
            elif health_status["overall_status"] == "degraded":
                self.logger.warning(f"⚠️ System health degraded: {'; '.join(health_status['issues'])}")
            else:
                self.logger.error(f"❌ System health warning: {'; '.join(health_status['issues'])}")
            
            return health_status
            
        except Exception as e:
            self.logger.error(f"❌ System health check error: {e}")
            return {
                "timestamp": datetime.utcnow(),
                "overall_status": "error",
                "components": {},
                "issues": [f"Health check failed: {str(e)}"]
            }
    
    async def _handle_market_update(self, message: AgentMessage):
        """Handle market data updates"""
        try:
            content = message.content
            symbol = content.get("symbol")
            
            if symbol:
                # Update market data cache
                market_data = MarketData(
                    symbol=symbol,
                    price=content.get("price", 0.0),
                    volume=content.get("volume", 0.0),
                    timestamp=message.timestamp,
                    bid=content.get("bid", 0.0),
                    ask=content.get("ask", 0.0),
                    change_24h=content.get("change_24h", 0.0)
                )
                
                self.market_data_cache[symbol] = market_data
                
                # Update portfolio state with new prices
                await self._update_portfolio_state()
                
                self.logger.debug(f"📊 Market update processed for {symbol}: ${market_data.price}")
                
        except Exception as e:
            self.logger.error(f"❌ Market update handling error: {e}")
    
    async def _handle_trading_signal(self, message: AgentMessage):
        """Handle trading signals from agents"""
        try:
            content = message.content
            
            # Create trading signal from message content
            signal = TradingSignal(
                symbol=content.get("symbol"),
                action=content.get("action"),
                strength=content.get("strength", 0.5),
                price_target=content.get("price_target"),
                stop_loss=content.get("stop_loss"),
                take_profit=content.get("take_profit"),
                strategy=content.get("strategy", "unknown"),
                agent_source=message.source_agent,
                timestamp=message.timestamp,
                confidence=content.get("confidence", 0.5),
                reasoning=content.get("reasoning", ""),
                risk_score=content.get("risk_score", 0.5)
            )
            
            # Validate signal
            if not signal.symbol or not signal.action:
                self.logger.warning(f"⚠️ Invalid trading signal from {message.source_agent}: missing symbol or action")
                return
            
            if signal.confidence < 0.3:  # Minimum confidence threshold
                self.logger.info(f"📉 Low confidence signal ignored from {message.source_agent}: {signal.confidence}")
                return
            
            # Submit signal for processing
            await self.submit_trading_signal(signal)
            
        except Exception as e:
            self.logger.error(f"❌ Trading signal handling error: {e}")
    
    async def _handle_risk_alert(self, message: AgentMessage):
        """Handle risk alerts"""
        try:
            content = message.content
            alerts = content.get("alerts", [])
            risk_metrics = content.get("risk_metrics", {})
            
            self.logger.warning(f"⚠️ Risk Alert from {message.source_agent}:")
            for alert in alerts:
                self.logger.warning(f"  - {alert}")
            
            # Take action based on risk level
            risk_score = risk_metrics.get("risk_score", 0)
            
            if risk_score > 8:  # Critical risk level
                self.logger.error("🚨 CRITICAL RISK LEVEL - Halting all trading")
                
                # Cancel all pending orders
                for order_id in list(self.pending_orders.keys()):
                    try:
                        if hasattr(self, 'exchange_manager'):
                            await self.exchange_manager.cancel_order(order_id)
                        del self.pending_orders[order_id]
                        self.logger.info(f"❌ Cancelled order {order_id} due to critical risk")
                    except Exception as e:
                        self.logger.error(f"❌ Failed to cancel order {order_id}: {e}")
                
                # Clear active signals
                for agent_name in self.active_signals:
                    self.active_signals[agent_name] = []
                self.logger.info("🧹 Cleared all active signals due to critical risk")
                
            elif risk_score > 6:  # High risk level
                self.logger.warning("⚠️ HIGH RISK LEVEL - Reducing position sizes")
            
            # Update risk metrics
            self.risk_metrics.update(risk_metrics)
            
        except Exception as e:
            self.logger.error(f"❌ Risk alert handling error: {e}")
    
    async def _handle_portfolio_update(self, message: AgentMessage):
        """Handle portfolio updates"""
        try:
            content = message.content
            update_type = content.get("update_type")
            
            if update_type == "position_change":
                symbol = content.get("symbol")
                new_quantity = content.get("quantity", 0.0)
                
                if symbol:
                    old_quantity = self.portfolio_state.get("positions", {}).get(symbol, 0.0)
                    self.portfolio_state.setdefault("positions", {})[symbol] = new_quantity
                    
                    self.logger.info(f"📊 Position updated: {symbol} {old_quantity} → {new_quantity}")
                    
            elif update_type == "balance_change":
                new_balance = content.get("cash_balance")
                if new_balance is not None:
                    old_balance = self.portfolio_state.get("cash_balance", 0.0)
                    self.portfolio_state["cash_balance"] = new_balance
                    
                    self.logger.info(f"💰 Cash balance updated: ${old_balance:.2f} → ${new_balance:.2f}")
                    
            elif update_type == "trade_executed":
                trade_info = content.get("trade_info", {})
                symbol = trade_info.get("symbol")
                side = trade_info.get("side")
                quantity = trade_info.get("quantity", 0.0)
                price = trade_info.get("price", 0.0)
                
                self.logger.info(f"✅ Trade executed: {side.upper()} {quantity} {symbol} @ ${price}")
                
                # Update trade history
                if "trade_history" not in self.portfolio_state:
                    self.portfolio_state["trade_history"] = []
                
                self.portfolio_state["trade_history"].append({
                    "timestamp": message.timestamp,
                    "symbol": symbol,
                    "side": side,
                    "quantity": quantity,
                    "price": price,
                    "value": quantity * price
                })
                
                # Keep only last 1000 trades
                if len(self.portfolio_state["trade_history"]) > 1000:
                    self.portfolio_state["trade_history"] = self.portfolio_state["trade_history"][-1000:]
            
            # Update portfolio state after any change
            await self._update_portfolio_state()
            await self._update_performance_metrics()
            
        except Exception as e:
            self.logger.error(f"❌ Portfolio update handling error: {e}")