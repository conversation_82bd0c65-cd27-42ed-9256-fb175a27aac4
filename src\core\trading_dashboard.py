"""
Real-Time AI Trading Dashboard
Live monitoring of AI agents and trading activity
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

from src.db.redis_manager import SimplifiedRedisManager
from src.db.clickhouse import ClickHouseManager

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TradingDashboard:
    """Real-time trading dashboard for monitoring AI agents"""
    
    def __init__(self):
        self.redis_manager = SimplifiedRedisManager()
        self.clickhouse_manager = ClickHouseManager()
        self.running = False
        self.logger = logging.getLogger(f"{__name__}.TradingDashboard")
        
    async def start_dashboard(self):
        """Start the real-time dashboard"""
        self.running = True
        self.logger.info("📊 STARTING REAL-TIME AI TRADING DASHBOARD")
        self.logger.info("=" * 100)
        
        while self.running:
            try:
                await self._display_dashboard()
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except KeyboardInterrupt:
                self.running = False
                break
            except Exception as e:
                self.logger.error(f"❌ Dashboard error: {e}")
                await asyncio.sleep(1)
    
    async def _display_dashboard(self):
        """Display the real-time dashboard"""
        try:
            # Clear screen (works on most terminals)
            print("\033[2J\033[H")
            
            # Header
            print("🚀 AI TRADING SYSTEM - LIVE DASHBOARD")
            print("=" * 100)
            print(f"📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | 🔄 Auto-refresh every 5 seconds")
            print("=" * 100)
            
            # Market data section
            await self._display_market_data()
            
            # AI agents section
            await self._display_ai_agents()
            
            # Trading activity section
            await self._display_trading_activity()
            
            # Performance metrics section
            await self._display_performance_metrics()
            
            print("=" * 100)
            print("💡 Press Ctrl+C to stop dashboard")
            print("=" * 100)
            
        except Exception as e:
            self.logger.error(f"❌ Error displaying dashboard: {e}")
    
    async def _display_market_data(self):
        """Display current market data"""
        print("\n📊 LIVE MARKET DATA")
        print("-" * 50)
        
        try:
            # Sample symbols to display
            symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "BNBUSDT", "SOLUSDT"]
            
            print(f"{'Symbol':<10} {'Price':<12} {'Volume':<12} {'Trend':<10} {'Volatility':<12}")
            print("-" * 60)
            
            for symbol in symbols:
                market_data = await self.redis_manager.get_market_data(symbol)
                if market_data:
                    price = market_data.get('price', 0)
                    volume = market_data.get('volume', 0)
                    trend = market_data.get('trend', 'N/A')
                    volatility = market_data.get('volatility', 0)
                    
                    # Format display
                    price_str = f"${price:,.4f}" if price > 1 else f"${price:.6f}"
                    volume_str = f"{volume:,}"
                    vol_str = f"{volatility:.3f}"
                    
                    print(f"{symbol:<10} {price_str:<12} {volume_str:<12} {trend:<10} {vol_str:<12}")
                else:
                    print(f"{symbol:<10} {'No data':<12} {'No data':<12} {'No data':<10} {'No data':<12}")
        
        except Exception as e:
            print(f"❌ Error displaying market data: {e}")
    
    async def _display_ai_agents(self):
        """Display AI agent status"""
        print("\n🤖 AI AGENTS STATUS")
        print("-" * 50)
        
        try:
            # Agent configurations
            agents = [
                ("TECH_ANALYST_1", "nemotron-mini:4b", "technical_analysis"),
                ("FUND_ANALYST_1", "deepseek-r1:32b", "fundamental_analysis"),
                ("SENTIMENT_1", "cogito:32b", "sentiment_analysis"),
                ("RISK_MANAGER_1", "magistral:24b", "risk_assessment"),
                ("STRATEGY_1", "command-r:35b", "strategy_development")
            ]
            
            print(f"{'Agent ID':<15} {'Model':<20} {'Specialization':<20} {'Status':<10}")
            print("-" * 70)
            
            for agent_id, model, specialization in agents:
                # Check if agent has recent analysis
                recent_analysis = await self.redis_manager.get_market_data(f"ai_analysis_{agent_id}_BTCUSDT")
                status = "🟢 ACTIVE" if recent_analysis else "🟡 IDLE"
                
                print(f"{agent_id:<15} {model:<20} {specialization:<20} {status:<10}")
        
        except Exception as e:
            print(f"❌ Error displaying AI agents: {e}")
    
    async def _display_trading_activity(self):
        """Display recent trading activity"""
        print("\n💰 RECENT TRADING ACTIVITY")
        print("-" * 50)
        
        try:
            # Get recent consensus data
            symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
            
            print(f"{'Symbol':<10} {'AI Signal':<10} {'Confidence':<12} {'Action':<10}")
            print("-" * 45)
            
            for symbol in symbols:
                consensus = await self.redis_manager.get_market_data(f"ai_consensus_{symbol}")
                if consensus:
                    action = consensus.get('action', 'HOLD')
                    confidence = consensus.get('confidence', 0.0)
                    
                    # Color code actions
                    if action == "BUY":
                        action_display = "🟢 BUY"
                    elif action == "SELL":
                        action_display = "🔴 SELL"
                    else:
                        action_display = "🟡 HOLD"
                    
                    confidence_str = f"{confidence:.2f}"
                    
                    print(f"{symbol:<10} {action_display:<10} {confidence_str:<12} {action:<10}")
                else:
                    print(f"{symbol:<10} {'No signal':<10} {'N/A':<12} {'N/A':<10}")
        
        except Exception as e:
            print(f"❌ Error displaying trading activity: {e}")
    
    async def _display_performance_metrics(self):
        """Display performance metrics"""
        print("\n📈 PERFORMANCE METRICS")
        print("-" * 50)
        
        try:
            # Simulate performance data (in real system, this would come from database)
            current_time = datetime.now()
            
            # Mock performance data
            metrics = {
                "Portfolio Value": "$102,450.00",
                "Total P&L": "+$2,450.00",
                "P&L %": "+2.45%",
                "Total Trades": "47",
                "Win Rate": "68.1%",
                "Sharpe Ratio": "1.34",
                "Max Drawdown": "3.2%",
                "Active Positions": "8"
            }
            
            # Display in two columns
            items = list(metrics.items())
            for i in range(0, len(items), 2):
                left_item = items[i]
                right_item = items[i + 1] if i + 1 < len(items) else ("", "")
                
                print(f"{left_item[0]:<20} {left_item[1]:<15} {right_item[0]:<20} {right_item[1]:<15}")
        
        except Exception as e:
            print(f"❌ Error displaying performance metrics: {e}")


async def main():
    """Main dashboard execution"""
    dashboard = TradingDashboard()
    
    try:
        await dashboard.start_dashboard()
    except KeyboardInterrupt:
        logger.info("📊 Dashboard stopped by user")


if __name__ == "__main__":
    asyncio.run(main())
