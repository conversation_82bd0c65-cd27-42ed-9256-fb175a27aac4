"""
Real Market Data Feeds Integration
Production-ready connections to actual exchanges and data providers
"""

import asyncio
import websockets
import json
import logging
import time
import aiohttp
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import ssl
import certifi

logger = logging.getLogger(__name__)


class DataProvider(Enum):
    """Supported data providers"""
    BINANCE = "BINANCE"
    COINBASE = "COINBASE"
    KRAKEN = "KRAKEN"
    ALPACA = "ALPACA"
    POLYGON = "POLYGON"
    ALPHA_VANTAGE = "ALPHA_VANTAGE"


@dataclass
class RealMarketData:
    """Real market data structure"""
    symbol: str
    exchange: str
    timestamp: datetime
    price: float
    volume: float
    bid: float
    ask: float
    bid_size: float
    ask_size: float
    high_24h: float
    low_24h: float
    volume_24h: float
    price_change_24h: float
    price_change_percent_24h: float
    trades_count: int
    raw_data: Dict[str, Any]


@dataclass
class OrderBookLevel:
    """Order book level data"""
    price: float
    quantity: float
    orders_count: int = 1


@dataclass
class OrderBookSnapshot:
    """Complete order book snapshot"""
    symbol: str
    exchange: str
    timestamp: datetime
    bids: List[OrderBookLevel]
    asks: List[OrderBookLevel]
    sequence: int = 0


class BinanceDataFeed:
    """Real Binance market data feed"""
    
    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None):
        self.api_key = api_key
        self.api_secret = api_secret
        self.base_url = "https://api.binance.com"
        self.ws_url = "wss://stream.binance.com:9443/ws"
        self.session = None
        self.ws_connection = None
        self.subscriptions = set()
        self.callbacks = {}
        self.logger = logging.getLogger(f"{__name__}.BinanceDataFeed")
        
    async def initialize(self):
        """Initialize connection and session"""
        try:
            self.session = aiohttp.ClientSession()
            
            # Test API connectivity
            async with self.session.get(f"{self.base_url}/api/v3/ping") as response:
                if response.status == 200:
                    self.logger.info("✅ Binance API connection established")
                else:
                    self.logger.error(f"❌ Binance API connection failed: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Binance connection: {e}")
            raise
    
    async def get_exchange_info(self) -> Dict[str, Any]:
        """Get exchange information and trading rules"""
        try:
            async with self.session.get(f"{self.base_url}/api/v3/exchangeInfo") as response:
                if response.status == 200:
                    data = await response.json()
                    self.logger.info(f"📊 Retrieved exchange info for {len(data['symbols'])} symbols")
                    return data
                else:
                    self.logger.error(f"❌ Failed to get exchange info: {response.status}")
                    return {}
                    
        except Exception as e:
            self.logger.error(f"❌ Error getting exchange info: {e}")
            return {}
    
    async def get_24hr_ticker(self, symbol: str) -> Optional[RealMarketData]:
        """Get 24hr ticker statistics"""
        try:
            params = {"symbol": symbol.upper()}
            async with self.session.get(f"{self.base_url}/api/v3/ticker/24hr", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    market_data = RealMarketData(
                        symbol=data["symbol"],
                        exchange="BINANCE",
                        timestamp=datetime.now(),
                        price=float(data["lastPrice"]),
                        volume=float(data["volume"]),
                        bid=float(data["bidPrice"]),
                        ask=float(data["askPrice"]),
                        bid_size=float(data["bidQty"]),
                        ask_size=float(data["askQty"]),
                        high_24h=float(data["highPrice"]),
                        low_24h=float(data["lowPrice"]),
                        volume_24h=float(data["volume"]),
                        price_change_24h=float(data["priceChange"]),
                        price_change_percent_24h=float(data["priceChangePercent"]),
                        trades_count=int(data["count"]),
                        raw_data=data
                    )
                    
                    self.logger.info(f"📈 {symbol}: ${market_data.price:.4f} "
                                   f"({market_data.price_change_percent_24h:+.2f}%)")
                    return market_data
                else:
                    self.logger.error(f"❌ Failed to get ticker for {symbol}: {response.status}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"❌ Error getting ticker for {symbol}: {e}")
            return None
    
    async def get_order_book(self, symbol: str, limit: int = 100) -> Optional[OrderBookSnapshot]:
        """Get current order book snapshot"""
        try:
            params = {"symbol": symbol.upper(), "limit": limit}
            async with self.session.get(f"{self.base_url}/api/v3/depth", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    bids = [OrderBookLevel(float(level[0]), float(level[1])) 
                           for level in data["bids"]]
                    asks = [OrderBookLevel(float(level[0]), float(level[1])) 
                           for level in data["asks"]]
                    
                    order_book = OrderBookSnapshot(
                        symbol=symbol.upper(),
                        exchange="BINANCE",
                        timestamp=datetime.now(),
                        bids=bids,
                        asks=asks,
                        sequence=data.get("lastUpdateId", 0)
                    )
                    
                    self.logger.info(f"📚 {symbol} order book: "
                                   f"Best bid: ${bids[0].price:.4f} ({bids[0].quantity:.2f}), "
                                   f"Best ask: ${asks[0].price:.4f} ({asks[0].quantity:.2f})")
                    return order_book
                else:
                    self.logger.error(f"❌ Failed to get order book for {symbol}: {response.status}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"❌ Error getting order book for {symbol}: {e}")
            return None
    
    async def get_klines(self, symbol: str, interval: str = "1m", limit: int = 500) -> pd.DataFrame:
        """Get historical kline/candlestick data"""
        try:
            params = {
                "symbol": symbol.upper(),
                "interval": interval,
                "limit": limit
            }
            
            async with self.session.get(f"{self.base_url}/api/v3/klines", params=params) as response:
                if response.status == 200:
                    data = await response.json()
                    
                    df = pd.DataFrame(data, columns=[
                        'timestamp', 'open', 'high', 'low', 'close', 'volume',
                        'close_time', 'quote_volume', 'trades_count',
                        'taker_buy_volume', 'taker_buy_quote_volume', 'ignore'
                    ])
                    
                    # Convert to proper types
                    df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
                    df['close_time'] = pd.to_datetime(df['close_time'], unit='ms')
                    
                    numeric_columns = ['open', 'high', 'low', 'close', 'volume', 
                                     'quote_volume', 'taker_buy_volume', 'taker_buy_quote_volume']
                    df[numeric_columns] = df[numeric_columns].astype(float)
                    df['trades_count'] = df['trades_count'].astype(int)
                    
                    self.logger.info(f"📊 Retrieved {len(df)} {interval} candles for {symbol}")
                    return df
                else:
                    self.logger.error(f"❌ Failed to get klines for {symbol}: {response.status}")
                    return pd.DataFrame()
                    
        except Exception as e:
            self.logger.error(f"❌ Error getting klines for {symbol}: {e}")
            return pd.DataFrame()
    
    async def subscribe_ticker(self, symbol: str, callback: Callable):
        """Subscribe to real-time ticker updates"""
        try:
            stream_name = f"{symbol.lower()}@ticker"
            self.subscriptions.add(stream_name)
            self.callbacks[stream_name] = callback
            
            if not self.ws_connection:
                await self._start_websocket()
            
            # Subscribe to stream
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": [stream_name],
                "id": int(time.time())
            }
            
            await self.ws_connection.send(json.dumps(subscribe_msg))
            self.logger.info(f"📡 Subscribed to {symbol} ticker stream")
            
        except Exception as e:
            self.logger.error(f"❌ Error subscribing to ticker for {symbol}: {e}")
    
    async def subscribe_order_book(self, symbol: str, callback: Callable):
        """Subscribe to real-time order book updates"""
        try:
            stream_name = f"{symbol.lower()}@depth20@100ms"
            self.subscriptions.add(stream_name)
            self.callbacks[stream_name] = callback
            
            if not self.ws_connection:
                await self._start_websocket()
            
            # Subscribe to stream
            subscribe_msg = {
                "method": "SUBSCRIBE",
                "params": [stream_name],
                "id": int(time.time())
            }
            
            await self.ws_connection.send(json.dumps(subscribe_msg))
            self.logger.info(f"📚 Subscribed to {symbol} order book stream")
            
        except Exception as e:
            self.logger.error(f"❌ Error subscribing to order book for {symbol}: {e}")
    
    async def _start_websocket(self):
        """Start WebSocket connection"""
        try:
            ssl_context = ssl.create_default_context(cafile=certifi.where())
            self.ws_connection = await websockets.connect(self.ws_url, ssl=ssl_context)
            
            # Start message handler
            asyncio.create_task(self._handle_websocket_messages())
            self.logger.info("🔌 WebSocket connection established")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start WebSocket: {e}")
            raise
    
    async def _handle_websocket_messages(self):
        """Handle incoming WebSocket messages"""
        try:
            async for message in self.ws_connection:
                try:
                    data = json.loads(message)
                    
                    # Handle ticker updates
                    if "stream" in data and data["stream"].endswith("@ticker"):
                        await self._handle_ticker_update(data)
                    
                    # Handle order book updates
                    elif "stream" in data and "@depth" in data["stream"]:
                        await self._handle_order_book_update(data)
                    
                except json.JSONDecodeError:
                    self.logger.warning(f"⚠️ Invalid JSON received: {message}")
                except Exception as e:
                    self.logger.error(f"❌ Error processing message: {e}")
                    
        except websockets.exceptions.ConnectionClosed:
            self.logger.warning("⚠️ WebSocket connection closed")
        except Exception as e:
            self.logger.error(f"❌ WebSocket error: {e}")
    
    async def _handle_ticker_update(self, data: Dict[str, Any]):
        """Handle ticker update message"""
        try:
            ticker_data = data["data"]
            stream_name = data["stream"]
            
            if stream_name in self.callbacks:
                market_data = RealMarketData(
                    symbol=ticker_data["s"],
                    exchange="BINANCE",
                    timestamp=datetime.fromtimestamp(ticker_data["E"] / 1000),
                    price=float(ticker_data["c"]),
                    volume=float(ticker_data["v"]),
                    bid=float(ticker_data["b"]),
                    ask=float(ticker_data["a"]),
                    bid_size=float(ticker_data["B"]),
                    ask_size=float(ticker_data["A"]),
                    high_24h=float(ticker_data["h"]),
                    low_24h=float(ticker_data["l"]),
                    volume_24h=float(ticker_data["v"]),
                    price_change_24h=float(ticker_data["P"]),
                    price_change_percent_24h=float(ticker_data["P"]),
                    trades_count=int(ticker_data["n"]),
                    raw_data=ticker_data
                )
                
                await self.callbacks[stream_name](market_data)
                
        except Exception as e:
            self.logger.error(f"❌ Error handling ticker update: {e}")
    
    async def _handle_order_book_update(self, data: Dict[str, Any]):
        """Handle order book update message"""
        try:
            book_data = data["data"]
            stream_name = data["stream"]
            
            if stream_name in self.callbacks:
                bids = [OrderBookLevel(float(level[0]), float(level[1])) 
                       for level in book_data["bids"]]
                asks = [OrderBookLevel(float(level[0]), float(level[1])) 
                       for level in book_data["asks"]]
                
                order_book = OrderBookSnapshot(
                    symbol=book_data.get("s", ""),
                    exchange="BINANCE",
                    timestamp=datetime.now(),
                    bids=bids,
                    asks=asks,
                    sequence=book_data.get("lastUpdateId", 0)
                )
                
                await self.callbacks[stream_name](order_book)
                
        except Exception as e:
            self.logger.error(f"❌ Error handling order book update: {e}")
    
    async def close(self):
        """Close connections"""
        try:
            if self.ws_connection:
                await self.ws_connection.close()
            if self.session:
                await self.session.close()
            self.logger.info("🔌 Connections closed")
        except Exception as e:
            self.logger.error(f"❌ Error closing connections: {e}")


class CoinbaseDataFeed:
    """Real Coinbase Pro market data feed"""
    
    def __init__(self, api_key: Optional[str] = None, api_secret: Optional[str] = None, passphrase: Optional[str] = None):
        self.api_key = api_key
        self.api_secret = api_secret
        self.passphrase = passphrase
        self.base_url = "https://api.exchange.coinbase.com"
        self.ws_url = "wss://ws-feed.exchange.coinbase.com"
        self.session = None
        self.ws_connection = None
        self.subscriptions = set()
        self.callbacks = {}
        self.logger = logging.getLogger(f"{__name__}.CoinbaseDataFeed")
    
    async def initialize(self):
        """Initialize connection and session"""
        try:
            self.session = aiohttp.ClientSession()
            
            # Test API connectivity
            async with self.session.get(f"{self.base_url}/time") as response:
                if response.status == 200:
                    self.logger.info("✅ Coinbase API connection established")
                else:
                    self.logger.error(f"❌ Coinbase API connection failed: {response.status}")
                    
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Coinbase connection: {e}")
            raise
    
    async def get_products(self) -> List[Dict[str, Any]]:
        """Get available trading products"""
        try:
            async with self.session.get(f"{self.base_url}/products") as response:
                if response.status == 200:
                    products = await response.json()
                    self.logger.info(f"📊 Retrieved {len(products)} Coinbase products")
                    return products
                else:
                    self.logger.error(f"❌ Failed to get products: {response.status}")
                    return []
                    
        except Exception as e:
            self.logger.error(f"❌ Error getting products: {e}")
            return []
    
    async def get_ticker(self, product_id: str) -> Optional[RealMarketData]:
        """Get ticker for specific product"""
        try:
            async with self.session.get(f"{self.base_url}/products/{product_id}/ticker") as response:
                if response.status == 200:
                    data = await response.json()
                    
                    # Get 24hr stats
                    async with self.session.get(f"{self.base_url}/products/{product_id}/stats") as stats_response:
                        stats_data = await stats_response.json() if stats_response.status == 200 else {}
                    
                    market_data = RealMarketData(
                        symbol=product_id,
                        exchange="COINBASE",
                        timestamp=datetime.now(),
                        price=float(data["price"]),
                        volume=float(data["volume"]),
                        bid=float(data["bid"]),
                        ask=float(data["ask"]),
                        bid_size=float(data["size"]),
                        ask_size=float(data["size"]),
                        high_24h=float(stats_data.get("high", data["price"])),
                        low_24h=float(stats_data.get("low", data["price"])),
                        volume_24h=float(stats_data.get("volume", data["volume"])),
                        price_change_24h=0.0,  # Calculate from stats if available
                        price_change_percent_24h=0.0,
                        trades_count=0,
                        raw_data=data
                    )
                    
                    self.logger.info(f"📈 {product_id}: ${market_data.price:.4f}")
                    return market_data
                else:
                    self.logger.error(f"❌ Failed to get ticker for {product_id}: {response.status}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"❌ Error getting ticker for {product_id}: {e}")
            return None
    
    async def close(self):
        """Close connections"""
        try:
            if self.ws_connection:
                await self.ws_connection.close()
            if self.session:
                await self.session.close()
            self.logger.info("🔌 Coinbase connections closed")
        except Exception as e:
            self.logger.error(f"❌ Error closing Coinbase connections: {e}")


class RealMarketDataManager:
    """Manager for real market data from multiple sources"""
    
    def __init__(self):
        self.data_feeds = {}
        self.active_subscriptions = {}
        self.market_data_cache = {}
        self.logger = logging.getLogger(f"{__name__}.RealMarketDataManager")
    
    async def initialize_feed(self, provider: DataProvider, **credentials):
        """Initialize data feed for specific provider"""
        try:
            if provider == DataProvider.BINANCE:
                feed = BinanceDataFeed(
                    api_key=credentials.get("api_key"),
                    api_secret=credentials.get("api_secret")
                )
            elif provider == DataProvider.COINBASE:
                feed = CoinbaseDataFeed(
                    api_key=credentials.get("api_key"),
                    api_secret=credentials.get("api_secret"),
                    passphrase=credentials.get("passphrase")
                )
            else:
                self.logger.error(f"❌ Unsupported provider: {provider}")
                return False
            
            await feed.initialize()
            self.data_feeds[provider] = feed
            self.logger.info(f"✅ Initialized {provider.value} data feed")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize {provider.value} feed: {e}")
            return False
    
    async def get_real_market_data(self, provider: DataProvider, symbol: str) -> Optional[RealMarketData]:
        """Get real market data from specific provider"""
        try:
            if provider not in self.data_feeds:
                self.logger.error(f"❌ Provider {provider.value} not initialized")
                return None
            
            feed = self.data_feeds[provider]
            
            if provider == DataProvider.BINANCE:
                return await feed.get_24hr_ticker(symbol)
            elif provider == DataProvider.COINBASE:
                return await feed.get_ticker(symbol)
            else:
                self.logger.error(f"❌ Get data not implemented for {provider.value}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Error getting market data: {e}")
            return None
    
    async def close_all_feeds(self):
        """Close all data feed connections"""
        for provider, feed in self.data_feeds.items():
            try:
                await feed.close()
                self.logger.info(f"🔌 Closed {provider.value} feed")
            except Exception as e:
                self.logger.error(f"❌ Error closing {provider.value} feed: {e}")


# Global instance
real_market_data_manager = RealMarketDataManager()
