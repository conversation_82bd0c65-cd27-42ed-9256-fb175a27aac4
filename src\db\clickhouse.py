"""Enhanced ClickHouse Manager for AI Trading System Time-Series Analytics

Advanced ClickHouse client with:
- OHLCV data schema and management
- Trade history storage and analytics
- Performance metrics tracking
- Batch insertion optimization
- Query optimization for historical analysis
- Real-time analytics support
"""

from __future__ import annotations

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional, Tuple
import pandas as pd

try:
    from clickhouse_driver import Client
    CLICKHOUSE_AVAILABLE = True
except ImportError:
    CLICKHOUSE_AVAILABLE = False
    Client = None

try:
    from src.core.config import get_settings
    settings = get_settings()
except ImportError:
    # Fallback configuration
    class Settings:
        CLICKHOUSE_HOST = "localhost"
        CLICKHOUSE_PORT = 9000
        CLICKHOUSE_USER = "default"
        CLICKHOUSE_PASSWORD = ""
        CLICKHOUSE_DB = "trading"
    settings = Settings()

logger = logging.getLogger(__name__)

# Client is created lazily to avoid connection at import time
_client: Client | None = None


class MockClickHouseClient:
    """Enhanced mock ClickHouse client for testing and fallback"""

    def __init__(self):
        self.data_store = {}
        self.logger = logging.getLogger(f"{__name__}.MockClickHouseClient")

    def execute(self, query: str, params=None):
        """Enhanced mock execute method"""
        query_upper = query.upper()

        if "SELECT 1" in query_upper:
            return [(1,)]
        elif "CREATE TABLE" in query_upper or "CREATE MATERIALIZED VIEW" in query_upper:
            self.logger.info(f"Mock: Created table/view from query: {query[:100]}...")
            return []
        elif "INSERT INTO" in query_upper:
            if params:
                self.logger.info(f"Mock: Inserted {len(params)} rows")
            return []
        elif "SELECT" in query_upper:
            # Return mock data for analytics queries
            if "COUNT(*)" in query_upper:
                return [(1000,)]
            elif "AVG(" in query_upper or "SUM(" in query_upper:
                return [(123.45,)]
            else:
                return []
        return []


def get_client():
    """Return enhanced ClickHouse Client instance with fallback"""
    global _client
    if _client is None:
        logger.info("Creating enhanced ClickHouse client...")

        if not CLICKHOUSE_AVAILABLE:
            logger.warning("ClickHouse driver not available, using mock client")
            _client = MockClickHouseClient()
            return _client

        try:
            _client = Client(
                host=settings.CLICKHOUSE_HOST,
                port=settings.CLICKHOUSE_PORT,
                user=settings.CLICKHOUSE_USER,
                password=settings.CLICKHOUSE_PASSWORD or "",
                database=settings.CLICKHOUSE_DB,
                settings={
                    "use_numpy": False,
                    "max_execution_time": 60,
                    "max_memory_usage": 10000000000,  # 10GB
                    "max_threads": 8
                },
            )
            # Test the connection
            _client.execute("SELECT 1")
            logger.info("✅ ClickHouse connection successful")

        except Exception as e:
            logger.warning(f"⚠️ ClickHouse not available, using mock client: {e}")
            _client = MockClickHouseClient()

    return _client


def ping() -> bool:
    """Enhanced ping with better error handling"""
    client = get_client()
    try:
        result = client.execute("SELECT 1")
        return result == [(1,)] or isinstance(client, MockClickHouseClient)
    except Exception as exc:
        logger.error("ClickHouse ping failed: %s", exc)
        return False


class ClickHouseManager:
    """Advanced ClickHouse Manager for AI Trading System Analytics"""

    def __init__(self):
        self.client = get_client()
        self.logger = logging.getLogger(f"{__name__}.ClickHouseManager")
        self.batch_size = 1000

    async def initialize_schema(self) -> bool:
        """Initialize ClickHouse database schema for trading analytics"""
        try:
            self.logger.info("🔧 Initializing ClickHouse schema...")

            # Create OHLCV data table
            ohlcv_schema = """
            CREATE TABLE IF NOT EXISTS ohlcv_data (
                symbol String,
                timestamp DateTime64(3),
                open Float64,
                high Float64,
                low Float64,
                close Float64,
                volume Float64,
                interval String,
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            PARTITION BY toYYYYMM(timestamp)
            ORDER BY (symbol, interval, timestamp)
            """

            # Create trade history table
            trade_history_schema = """
            CREATE TABLE IF NOT EXISTS trade_history (
                trade_id String,
                symbol String,
                side Enum('BUY' = 1, 'SELL' = 2),
                quantity Float64,
                price Float64,
                timestamp DateTime64(3),
                order_type Enum('MARKET' = 1, 'LIMIT' = 2, 'STOP' = 3),
                execution_algorithm String,
                slippage Float64,
                commission Float64,
                portfolio_id String,
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            PARTITION BY toYYYYMM(timestamp)
            ORDER BY (symbol, timestamp, trade_id)
            """

            # Create performance metrics table
            performance_metrics_schema = """
            CREATE TABLE IF NOT EXISTS performance_metrics (
                metric_id String,
                portfolio_id String,
                timestamp DateTime64(3),
                total_value Float64,
                pnl Float64,
                pnl_percent Float64,
                sharpe_ratio Float64,
                max_drawdown Float64,
                win_rate Float64,
                total_trades UInt32,
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            PARTITION BY toYYYYMM(timestamp)
            ORDER BY (portfolio_id, timestamp)
            """

            # Create AI analysis results table
            ai_analysis_schema = """
            CREATE TABLE IF NOT EXISTS ai_analysis (
                analysis_id String,
                symbol String,
                analysis_type String,
                model_name String,
                timestamp DateTime64(3),
                confidence Float64,
                prediction String,
                reasoning String,
                metadata String,
                created_at DateTime DEFAULT now()
            ) ENGINE = MergeTree()
            PARTITION BY toYYYYMM(timestamp)
            ORDER BY (symbol, analysis_type, timestamp)
            """

            # Execute schema creation
            schemas = [
                ("OHLCV Data", ohlcv_schema),
                ("Trade History", trade_history_schema),
                ("Performance Metrics", performance_metrics_schema),
                ("AI Analysis", ai_analysis_schema)
            ]

            for name, schema in schemas:
                try:
                    self.client.execute(schema)
                    self.logger.info(f"  ✅ {name} table created/verified")
                except Exception as e:
                    self.logger.error(f"  ❌ Failed to create {name} table: {e}")
                    return False

            # Create materialized views for real-time analytics
            await self._create_materialized_views()

            self.logger.info("✅ ClickHouse schema initialization complete")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to initialize ClickHouse schema: {e}")
            return False

    async def _create_materialized_views(self):
        """Create materialized views for real-time analytics"""
        try:
            # Daily OHLCV aggregation view
            daily_ohlcv_view = """
            CREATE MATERIALIZED VIEW IF NOT EXISTS daily_ohlcv_mv
            ENGINE = SummingMergeTree()
            PARTITION BY toYYYYMM(date)
            ORDER BY (symbol, date)
            AS SELECT
                symbol,
                toDate(timestamp) as date,
                argMin(open, timestamp) as open,
                max(high) as high,
                min(low) as low,
                argMax(close, timestamp) as close,
                sum(volume) as volume
            FROM ohlcv_data
            WHERE interval = '1m'
            GROUP BY symbol, date
            """

            # Portfolio performance summary view
            portfolio_summary_view = """
            CREATE MATERIALIZED VIEW IF NOT EXISTS portfolio_summary_mv
            ENGINE = ReplacingMergeTree()
            PARTITION BY toYYYYMM(date)
            ORDER BY (portfolio_id, date)
            AS SELECT
                portfolio_id,
                toDate(timestamp) as date,
                argMax(total_value, timestamp) as latest_value,
                sum(pnl) as daily_pnl,
                max(max_drawdown) as max_drawdown,
                avg(sharpe_ratio) as avg_sharpe_ratio
            FROM performance_metrics
            GROUP BY portfolio_id, date
            """

            views = [
                ("Daily OHLCV", daily_ohlcv_view),
                ("Portfolio Summary", portfolio_summary_view)
            ]

            for name, view in views:
                try:
                    self.client.execute(view)
                    self.logger.info(f"  ✅ {name} materialized view created")
                except Exception as e:
                    self.logger.warning(f"  ⚠️ {name} view creation failed: {e}")

        except Exception as e:
            self.logger.error(f"Failed to create materialized views: {e}")

    async def insert_ohlcv_batch(self, ohlcv_data: List[Dict[str, Any]]) -> bool:
        """Batch insert OHLCV data with optimization"""
        try:
            if not ohlcv_data:
                return True

            # Prepare data for batch insertion
            formatted_data = []
            for record in ohlcv_data:
                formatted_record = (
                    record['symbol'],
                    record['timestamp'],
                    record['open'],
                    record['high'],
                    record['low'],
                    record['close'],
                    record['volume'],
                    record.get('interval', '1m')
                )
                formatted_data.append(formatted_record)

            # Batch insert
            self.client.execute(
                "INSERT INTO ohlcv_data (symbol, timestamp, open, high, low, close, volume, interval) VALUES",
                formatted_data
            )

            self.logger.info(f"✅ Inserted {len(formatted_data)} OHLCV records")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to insert OHLCV batch: {e}")
            return False

    async def insert_trade_batch(self, trades: List[Dict[str, Any]]) -> bool:
        """Batch insert trade history data"""
        try:
            if not trades:
                return True

            formatted_trades = []
            for trade in trades:
                formatted_trade = (
                    trade['trade_id'],
                    trade['symbol'],
                    trade['side'],
                    trade['quantity'],
                    trade['price'],
                    trade['timestamp'],
                    trade.get('order_type', 'MARKET'),
                    trade.get('execution_algorithm', 'MANUAL'),
                    trade.get('slippage', 0.0),
                    trade.get('commission', 0.0),
                    trade.get('portfolio_id', 'default')
                )
                formatted_trades.append(formatted_trade)

            self.client.execute(
                """INSERT INTO trade_history
                   (trade_id, symbol, side, quantity, price, timestamp, order_type,
                    execution_algorithm, slippage, commission, portfolio_id) VALUES""",
                formatted_trades
            )

            self.logger.info(f"✅ Inserted {len(formatted_trades)} trade records")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to insert trade batch: {e}")
            return False

    async def insert_performance_metrics(self, metrics: List[Dict[str, Any]]) -> bool:
        """Insert performance metrics data"""
        try:
            if not metrics:
                return True

            formatted_metrics = []
            for metric in metrics:
                formatted_metric = (
                    metric['metric_id'],
                    metric['portfolio_id'],
                    metric['timestamp'],
                    metric['total_value'],
                    metric['pnl'],
                    metric['pnl_percent'],
                    metric.get('sharpe_ratio', 0.0),
                    metric.get('max_drawdown', 0.0),
                    metric.get('win_rate', 0.0),
                    metric.get('total_trades', 0)
                )
                formatted_metrics.append(formatted_metric)

            self.client.execute(
                """INSERT INTO performance_metrics
                   (metric_id, portfolio_id, timestamp, total_value, pnl, pnl_percent,
                    sharpe_ratio, max_drawdown, win_rate, total_trades) VALUES""",
                formatted_metrics
            )

            self.logger.info(f"✅ Inserted {len(formatted_metrics)} performance metrics")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to insert performance metrics: {e}")
            return False

    async def insert_ai_analysis(self, analyses: List[Dict[str, Any]]) -> bool:
        """Insert AI analysis results"""
        try:
            if not analyses:
                return True

            formatted_analyses = []
            for analysis in analyses:
                formatted_analysis = (
                    analysis['analysis_id'],
                    analysis['symbol'],
                    analysis['analysis_type'],
                    analysis['model_name'],
                    analysis['timestamp'],
                    analysis.get('confidence', 0.0),
                    analysis.get('prediction', ''),
                    analysis.get('reasoning', ''),
                    analysis.get('metadata', '{}')
                )
                formatted_analyses.append(formatted_analysis)

            self.client.execute(
                """INSERT INTO ai_analysis
                   (analysis_id, symbol, analysis_type, model_name, timestamp,
                    confidence, prediction, reasoning, metadata) VALUES""",
                formatted_analyses
            )

            self.logger.info(f"✅ Inserted {len(formatted_analyses)} AI analysis records")
            return True

        except Exception as e:
            self.logger.error(f"❌ Failed to insert AI analysis: {e}")
            return False

    async def get_ohlcv_data(self, symbol: str, interval: str = '1m',
                           start_time: Optional[datetime] = None,
                           end_time: Optional[datetime] = None,
                           limit: int = 1000) -> List[Dict[str, Any]]:
        """Get OHLCV data with time range filtering"""
        try:
            query = """
            SELECT symbol, timestamp, open, high, low, close, volume, interval
            FROM ohlcv_data
            WHERE symbol = %(symbol)s AND interval = %(interval)s
            """

            params = {'symbol': symbol, 'interval': interval}

            if start_time:
                query += " AND timestamp >= %(start_time)s"
                params['start_time'] = start_time

            if end_time:
                query += " AND timestamp <= %(end_time)s"
                params['end_time'] = end_time

            query += " ORDER BY timestamp DESC LIMIT %(limit)s"
            params['limit'] = limit

            result = self.client.execute(query, params)

            # Convert to list of dictionaries
            ohlcv_data = []
            for row in result:
                ohlcv_data.append({
                    'symbol': row[0],
                    'timestamp': row[1],
                    'open': row[2],
                    'high': row[3],
                    'low': row[4],
                    'close': row[5],
                    'volume': row[6],
                    'interval': row[7]
                })

            return ohlcv_data

        except Exception as e:
            self.logger.error(f"❌ Failed to get OHLCV data for {symbol}: {e}")
            return []

    async def get_trade_history(self, symbol: Optional[str] = None,
                              portfolio_id: Optional[str] = None,
                              start_time: Optional[datetime] = None,
                              end_time: Optional[datetime] = None,
                              limit: int = 1000) -> List[Dict[str, Any]]:
        """Get trade history with filtering"""
        try:
            query = """
            SELECT trade_id, symbol, side, quantity, price, timestamp,
                   order_type, execution_algorithm, slippage, commission, portfolio_id
            FROM trade_history
            WHERE 1=1
            """

            params = {}

            if symbol:
                query += " AND symbol = %(symbol)s"
                params['symbol'] = symbol

            if portfolio_id:
                query += " AND portfolio_id = %(portfolio_id)s"
                params['portfolio_id'] = portfolio_id

            if start_time:
                query += " AND timestamp >= %(start_time)s"
                params['start_time'] = start_time

            if end_time:
                query += " AND timestamp <= %(end_time)s"
                params['end_time'] = end_time

            query += " ORDER BY timestamp DESC LIMIT %(limit)s"
            params['limit'] = limit

            result = self.client.execute(query, params)

            trades = []
            for row in result:
                trades.append({
                    'trade_id': row[0],
                    'symbol': row[1],
                    'side': row[2],
                    'quantity': row[3],
                    'price': row[4],
                    'timestamp': row[5],
                    'order_type': row[6],
                    'execution_algorithm': row[7],
                    'slippage': row[8],
                    'commission': row[9],
                    'portfolio_id': row[10]
                })

            return trades

        except Exception as e:
            self.logger.error(f"❌ Failed to get trade history: {e}")
            return []

    async def get_performance_analytics(self, portfolio_id: str,
                                      start_time: Optional[datetime] = None,
                                      end_time: Optional[datetime] = None) -> Dict[str, Any]:
        """Get comprehensive performance analytics"""
        try:
            # Base query for performance metrics
            query = """
            SELECT
                COUNT(*) as total_records,
                AVG(pnl_percent) as avg_pnl_percent,
                SUM(pnl) as total_pnl,
                MAX(total_value) as max_portfolio_value,
                MIN(total_value) as min_portfolio_value,
                AVG(sharpe_ratio) as avg_sharpe_ratio,
                MAX(max_drawdown) as worst_drawdown,
                AVG(win_rate) as avg_win_rate,
                SUM(total_trades) as total_trades
            FROM performance_metrics
            WHERE portfolio_id = %(portfolio_id)s
            """

            params = {'portfolio_id': portfolio_id}

            if start_time:
                query += " AND timestamp >= %(start_time)s"
                params['start_time'] = start_time

            if end_time:
                query += " AND timestamp <= %(end_time)s"
                params['end_time'] = end_time

            result = self.client.execute(query, params)

            if result:
                row = result[0]
                analytics = {
                    'portfolio_id': portfolio_id,
                    'total_records': row[0],
                    'avg_pnl_percent': row[1] or 0.0,
                    'total_pnl': row[2] or 0.0,
                    'max_portfolio_value': row[3] or 0.0,
                    'min_portfolio_value': row[4] or 0.0,
                    'avg_sharpe_ratio': row[5] or 0.0,
                    'worst_drawdown': row[6] or 0.0,
                    'avg_win_rate': row[7] or 0.0,
                    'total_trades': row[8] or 0
                }

                # Calculate additional metrics
                if analytics['max_portfolio_value'] > 0 and analytics['min_portfolio_value'] > 0:
                    analytics['portfolio_growth'] = (
                        (analytics['max_portfolio_value'] - analytics['min_portfolio_value']) /
                        analytics['min_portfolio_value'] * 100
                    )
                else:
                    analytics['portfolio_growth'] = 0.0

                return analytics

            return {}

        except Exception as e:
            self.logger.error(f"❌ Failed to get performance analytics: {e}")
            return {}


# Global ClickHouse manager instance
clickhouse_manager = ClickHouseManager()


# Legacy functions for backward compatibility
def insert_tick(row: Dict[str, Any]) -> None:
    """Legacy function - insert single tick row"""
    client = get_client()
    try:
        client.execute("INSERT INTO ticks VALUES", [row])
    except Exception as e:
        logger.error(f"Failed to insert tick: {e}")