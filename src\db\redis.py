"""Enhanced Redis Manager for AI Trading System

Advanced Redis client with connection pooling, failover mechanisms,
real-time data storage, and trading object serialization.

Features:
- Connection pooling and failover
- Real-time price feed storage
- Trading object serialization/deserialization
- Data persistence and retrieval
- Performance monitoring
"""

from __future__ import annotations

import asyncio
import json
import logging
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Final
import redis.asyncio as aioredis
from dataclasses import asdict, is_dataclass

try:
    from src.core.config import get_settings
    settings = get_settings()
except ImportError:
    # Fallback configuration for testing
    class Settings:
        redis_url = "redis://localhost:6379"
        DEBUG = False
    settings = Settings()

logger = logging.getLogger(__name__)

# Internal cached client and connection pool
_client: aioredis.Redis | None = None
_connection_pool: aioredis.ConnectionPool | None = None

# Configuration constants
PING_TIMEOUT: Final[int] = 5
MAX_CONNECTIONS: Final[int] = 20
RETRY_ON_TIMEOUT: Final[bool] = True
SOCKET_KEEPALIVE: Final[bool] = True


async def get_connection_pool() -> aioredis.ConnectionPool:
    """Get or create Redis connection pool with advanced configuration"""
    global _connection_pool
    if _connection_pool is None:
        logger.info("Creating Redis connection pool...")
        _connection_pool = aioredis.ConnectionPool.from_url(
            settings.redis_url,
            max_connections=MAX_CONNECTIONS,
            retry_on_timeout=RETRY_ON_TIMEOUT,
            socket_keepalive=SOCKET_KEEPALIVE,
            socket_keepalive_options={},
            decode_responses=True,
            encoding='utf-8'
        )
    return _connection_pool


def get_client() -> aioredis.Redis:
    """Return enhanced Redis client with connection pooling and failover"""
    global _client
    if _client is None:
        logger.info("Creating enhanced Redis client with connection pooling...")
        try:
            # Create client with connection pool
            pool = asyncio.run(get_connection_pool())
            _client = aioredis.Redis(connection_pool=pool)
        except Exception as e:
            logger.warning(f"Failed to create pooled client, using simple client: {e}")
            _client = aioredis.from_url(settings.redis_url, decode_responses=True)
    return _client


async def ping() -> bool:
    """Enhanced ping with timeout and retry logic"""
    client = get_client()
    try:
        response = await asyncio.wait_for(client.ping(), timeout=PING_TIMEOUT)
        return response is True
    except asyncio.TimeoutError:
        logger.error("Redis ping timeout")
        return False
    except Exception as exc:
        logger.error("Redis ping failed: %s", exc, exc_info=settings.DEBUG)
        return False


async def close() -> None:
    """Gracefully close Redis connections and pool"""
    global _client, _connection_pool

    if _client is not None:
        try:
            await _client.close()
        except Exception as e:
            logger.error(f"Error closing Redis client: {e}")
        _client = None

    if _connection_pool is not None:
        try:
            await _connection_pool.disconnect()
        except Exception as e:
            logger.error(f"Error closing Redis connection pool: {e}")
        _connection_pool = None


class RedisManager:
    """Advanced Redis Manager for AI Trading System"""

    def __init__(self):
        self.client = get_client()
        self.logger = logging.getLogger(f"{__name__}.RedisManager")

    async def test_connection(self) -> bool:
        """Test Redis connection with detailed diagnostics"""
        try:
            # Basic ping test
            if not await ping():
                return False

            # Test basic operations
            test_key = "test:connection"
            await self.client.set(test_key, "test_value", ex=10)
            value = await self.client.get(test_key)
            await self.client.delete(test_key)

            if value != "test_value":
                self.logger.error("Redis connection test failed: value mismatch")
                return False

            self.logger.info("✅ Redis connection test successful")
            return True

        except Exception as e:
            self.logger.error(f"❌ Redis connection test failed: {e}")
            return False

    def serialize_trading_object(self, obj: Any) -> str:
        """Serialize trading objects for Redis storage"""
        try:
            if is_dataclass(obj):
                return json.dumps(asdict(obj), default=str)
            elif isinstance(obj, dict):
                return json.dumps(obj, default=str)
            elif hasattr(obj, '__dict__'):
                return json.dumps(obj.__dict__, default=str)
            else:
                return json.dumps(obj, default=str)
        except Exception as e:
            self.logger.warning(f"JSON serialization failed, using pickle: {e}")
            return pickle.dumps(obj).hex()

    def deserialize_trading_object(self, data: str, object_type: str = "dict") -> Any:
        """Deserialize trading objects from Redis"""
        try:
            # Try JSON first
            return json.loads(data)
        except json.JSONDecodeError:
            try:
                # Fallback to pickle
                return pickle.loads(bytes.fromhex(data))
            except Exception as e:
                self.logger.error(f"Failed to deserialize object: {e}")
                return None

    async def store_market_data(self, symbol: str, data: Dict[str, Any], ttl: int = 300) -> bool:
        """Store real-time market data with TTL"""
        try:
            key = f"market_data:{symbol}"
            serialized_data = self.serialize_trading_object(data)

            # Store with expiration
            await self.client.setex(key, ttl, serialized_data)

            # Also add to time-series for historical tracking
            timestamp = int(datetime.now().timestamp())
            ts_key = f"market_data_ts:{symbol}"
            await self.client.zadd(ts_key, {serialized_data: timestamp})

            # Keep only last 1000 entries in time-series
            await self.client.zremrangebyrank(ts_key, 0, -1001)

            return True

        except Exception as e:
            self.logger.error(f"Failed to store market data for {symbol}: {e}")
            return False

    async def get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Retrieve latest market data for symbol"""
        try:
            key = f"market_data:{symbol}"
            data = await self.client.get(key)

            if data:
                return self.deserialize_trading_object(data)
            return None

        except Exception as e:
            self.logger.error(f"Failed to get market data for {symbol}: {e}")
            return None

    async def get_market_data_history(self, symbol: str, limit: int = 100) -> List[Dict[str, Any]]:
        """Get historical market data from time-series"""
        try:
            ts_key = f"market_data_ts:{symbol}"
            # Get latest entries
            data_list = await self.client.zrevrange(ts_key, 0, limit-1)

            history = []
            for data_str in data_list:
                obj = self.deserialize_trading_object(data_str)
                if obj:
                    history.append(obj)

            return history

        except Exception as e:
            self.logger.error(f"Failed to get market data history for {symbol}: {e}")
            return []

    async def store_price_feed(self, symbol: str, price: float, volume: float = 0,
                              timestamp: Optional[datetime] = None) -> bool:
        """Store real-time price feed data"""
        try:
            if timestamp is None:
                timestamp = datetime.now()

            # Store latest price
            price_key = f"price:{symbol}"
            price_data = {
                "price": price,
                "volume": volume,
                "timestamp": timestamp.isoformat()
            }

            await self.client.setex(price_key, 300, self.serialize_trading_object(price_data))

            # Add to price time-series
            ts_key = f"price_ts:{symbol}"
            score = int(timestamp.timestamp())
            await self.client.zadd(ts_key, {f"{price}:{volume}": score})

            # Keep only last 24 hours of price data
            cutoff = int((datetime.now() - timedelta(hours=24)).timestamp())
            await self.client.zremrangebyscore(ts_key, 0, cutoff)

            return True

        except Exception as e:
            self.logger.error(f"Failed to store price feed for {symbol}: {e}")
            return False

    async def get_latest_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get latest price for symbol"""
        try:
            price_key = f"price:{symbol}"
            data = await self.client.get(price_key)

            if data:
                return self.deserialize_trading_object(data)
            return None

        except Exception as e:
            self.logger.error(f"Failed to get latest price for {symbol}: {e}")
            return None

    async def store_portfolio_state(self, portfolio_id: str, state: Dict[str, Any], ttl: int = 60) -> bool:
        """Store portfolio state with TTL"""
        try:
            key = f"portfolio:{portfolio_id}"
            serialized_state = self.serialize_trading_object(state)
            await self.client.setex(key, ttl, serialized_state)

            # Also store in hash for quick field access
            hash_key = f"portfolio_hash:{portfolio_id}"
            for field, value in state.items():
                await self.client.hset(hash_key, field, str(value))
            await self.client.expire(hash_key, ttl)

            return True

        except Exception as e:
            self.logger.error(f"Failed to store portfolio state {portfolio_id}: {e}")
            return False

    async def get_portfolio_state(self, portfolio_id: str) -> Optional[Dict[str, Any]]:
        """Get portfolio state"""
        try:
            key = f"portfolio:{portfolio_id}"
            data = await self.client.get(key)

            if data:
                return self.deserialize_trading_object(data)
            return None

        except Exception as e:
            self.logger.error(f"Failed to get portfolio state {portfolio_id}: {e}")
            return None

    async def store_trade_execution(self, trade_id: str, execution_data: Dict[str, Any]) -> bool:
        """Store trade execution data"""
        try:
            key = f"trade_execution:{trade_id}"
            serialized_data = self.serialize_trading_object(execution_data)

            # Store with 24-hour TTL
            await self.client.setex(key, 86400, serialized_data)

            # Add to executions list for the symbol
            symbol = execution_data.get('symbol', 'UNKNOWN')
            list_key = f"executions:{symbol}"
            await self.client.lpush(list_key, trade_id)
            await self.client.ltrim(list_key, 0, 999)  # Keep last 1000 executions
            await self.client.expire(list_key, 86400)

            return True

        except Exception as e:
            self.logger.error(f"Failed to store trade execution {trade_id}: {e}")
            return False

    async def get_trade_execution(self, trade_id: str) -> Optional[Dict[str, Any]]:
        """Get trade execution data"""
        try:
            key = f"trade_execution:{trade_id}"
            data = await self.client.get(key)

            if data:
                return self.deserialize_trading_object(data)
            return None

        except Exception as e:
            self.logger.error(f"Failed to get trade execution {trade_id}: {e}")
            return None

    async def cache_ai_analysis(self, analysis_id: str, analysis_data: Dict[str, Any], ttl: int = 1800) -> bool:
        """Cache AI analysis results (30 min TTL)"""
        try:
            key = f"ai_analysis:{analysis_id}"
            serialized_data = self.serialize_trading_object(analysis_data)
            await self.client.setex(key, ttl, serialized_data)

            return True

        except Exception as e:
            self.logger.error(f"Failed to cache AI analysis {analysis_id}: {e}")
            return False

    async def get_ai_analysis(self, analysis_id: str) -> Optional[Dict[str, Any]]:
        """Get cached AI analysis"""
        try:
            key = f"ai_analysis:{analysis_id}"
            data = await self.client.get(key)

            if data:
                return self.deserialize_trading_object(data)
            return None

        except Exception as e:
            self.logger.error(f"Failed to get AI analysis {analysis_id}: {e}")
            return None

    async def store_system_metrics(self, metrics: Dict[str, Any]) -> bool:
        """Store system performance metrics"""
        try:
            timestamp = int(datetime.now().timestamp())
            key = "system_metrics"

            # Store latest metrics
            serialized_metrics = self.serialize_trading_object(metrics)
            await self.client.setex(key, 300, serialized_metrics)

            # Add to time-series
            ts_key = "system_metrics_ts"
            await self.client.zadd(ts_key, {serialized_metrics: timestamp})

            # Keep only last 24 hours
            cutoff = timestamp - 86400
            await self.client.zremrangebyscore(ts_key, 0, cutoff)

            return True

        except Exception as e:
            self.logger.error(f"Failed to store system metrics: {e}")
            return False

    async def get_system_metrics(self) -> Optional[Dict[str, Any]]:
        """Get latest system metrics"""
        try:
            key = "system_metrics"
            data = await self.client.get(key)

            if data:
                return self.deserialize_trading_object(data)
            return None

        except Exception as e:
            self.logger.error(f"Failed to get system metrics: {e}")
            return None

    async def cleanup_expired_data(self) -> Dict[str, int]:
        """Cleanup expired data and return statistics"""
        try:
            cleanup_stats = {
                "market_data_cleaned": 0,
                "price_feeds_cleaned": 0,
                "portfolios_cleaned": 0,
                "executions_cleaned": 0
            }

            # Clean up old time-series data
            cutoff_24h = int((datetime.now() - timedelta(hours=24)).timestamp())
            cutoff_1h = int((datetime.now() - timedelta(hours=1)).timestamp())

            # Clean price time-series (keep 24h)
            price_keys = await self.client.keys("price_ts:*")
            for key in price_keys:
                removed = await self.client.zremrangebyscore(key, 0, cutoff_24h)
                cleanup_stats["price_feeds_cleaned"] += removed

            # Clean market data time-series (keep 1000 entries)
            market_keys = await self.client.keys("market_data_ts:*")
            for key in market_keys:
                await self.client.zremrangebyrank(key, 0, -1001)
                cleanup_stats["market_data_cleaned"] += 1

            self.logger.info(f"Redis cleanup completed: {cleanup_stats}")
            return cleanup_stats

        except Exception as e:
            self.logger.error(f"Failed to cleanup expired data: {e}")
            return {}


# Global Redis manager instance
redis_manager = RedisManager()