"""
Simplified Redis Manager for AI Trading System
Works without external Redis dependencies using in-memory storage for testing
"""

import asyncio
import json
import logging
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union
from dataclasses import asdict, is_dataclass
import threading
import time

logger = logging.getLogger(__name__)


class InMemoryRedisClient:
    """In-memory Redis client for testing and development"""
    
    def __init__(self):
        self.data = {}
        self.expiry = {}
        self.lock = threading.Lock()
        self.logger = logging.getLogger(f"{__name__}.InMemoryRedisClient")
        
        # Start cleanup task
        self._start_cleanup_task()
    
    def _start_cleanup_task(self):
        """Start background task to clean expired keys"""
        def cleanup():
            while True:
                try:
                    current_time = time.time()
                    with self.lock:
                        expired_keys = [
                            key for key, expiry_time in self.expiry.items()
                            if expiry_time <= current_time
                        ]
                        for key in expired_keys:
                            self.data.pop(key, None)
                            self.expiry.pop(key, None)
                    
                    time.sleep(10)  # Cleanup every 10 seconds
                except Exception as e:
                    self.logger.error(f"Cleanup task error: {e}")
                    time.sleep(30)
        
        cleanup_thread = threading.Thread(target=cleanup, daemon=True)
        cleanup_thread.start()
    
    async def ping(self):
        """Ping the in-memory client"""
        return True
    
    async def set(self, key: str, value: str):
        """Set a key-value pair"""
        with self.lock:
            self.data[key] = value
        return True
    
    async def setex(self, key: str, ttl: int, value: str):
        """Set a key-value pair with TTL"""
        with self.lock:
            self.data[key] = value
            self.expiry[key] = time.time() + ttl
        return True
    
    async def get(self, key: str):
        """Get a value by key"""
        with self.lock:
            # Check if key is expired
            if key in self.expiry and self.expiry[key] <= time.time():
                self.data.pop(key, None)
                self.expiry.pop(key, None)
                return None
            return self.data.get(key)
    
    async def delete(self, key: str):
        """Delete a key"""
        with self.lock:
            self.data.pop(key, None)
            self.expiry.pop(key, None)
        return True
    
    async def hset(self, key: str, field: str, value: str):
        """Set a hash field"""
        with self.lock:
            if key not in self.data:
                self.data[key] = {}
            if not isinstance(self.data[key], dict):
                self.data[key] = {}
            self.data[key][field] = value
        return True
    
    async def hget(self, key: str, field: str):
        """Get a hash field"""
        with self.lock:
            if key in self.data and isinstance(self.data[key], dict):
                return self.data[key].get(field)
        return None
    
    async def expire(self, key: str, ttl: int):
        """Set TTL for a key"""
        with self.lock:
            if key in self.data:
                self.expiry[key] = time.time() + ttl
        return True
    
    async def zadd(self, key: str, mapping: Dict[str, float]):
        """Add to sorted set"""
        with self.lock:
            if key not in self.data:
                self.data[key] = []
            if not isinstance(self.data[key], list):
                self.data[key] = []
            
            for member, score in mapping.items():
                # Remove existing member if present
                self.data[key] = [(m, s) for m, s in self.data[key] if m != member]
                # Add new member
                self.data[key].append((member, score))
                # Sort by score
                self.data[key].sort(key=lambda x: x[1])
        return True
    
    async def zrevrange(self, key: str, start: int, end: int):
        """Get sorted set members in reverse order"""
        with self.lock:
            if key in self.data and isinstance(self.data[key], list):
                sorted_data = sorted(self.data[key], key=lambda x: x[1], reverse=True)
                if end == -1:
                    return [member for member, score in sorted_data[start:]]
                else:
                    return [member for member, score in sorted_data[start:end+1]]
        return []
    
    async def zremrangebyrank(self, key: str, start: int, end: int):
        """Remove sorted set members by rank"""
        with self.lock:
            if key in self.data and isinstance(self.data[key], list):
                if end == -1:
                    self.data[key] = self.data[key][:start] if start > 0 else []
                else:
                    self.data[key] = self.data[key][:start] + self.data[key][end+1:]
        return True
    
    async def zremrangebyscore(self, key: str, min_score: float, max_score: float):
        """Remove sorted set members by score"""
        with self.lock:
            if key in self.data and isinstance(self.data[key], list):
                self.data[key] = [
                    (member, score) for member, score in self.data[key]
                    if not (min_score <= score <= max_score)
                ]
        return True
    
    async def lpush(self, key: str, value: str):
        """Push to list (left)"""
        with self.lock:
            if key not in self.data:
                self.data[key] = []
            if not isinstance(self.data[key], list):
                self.data[key] = []
            self.data[key].insert(0, value)
        return True
    
    async def ltrim(self, key: str, start: int, end: int):
        """Trim list"""
        with self.lock:
            if key in self.data and isinstance(self.data[key], list):
                self.data[key] = self.data[key][start:end+1]
        return True
    
    async def keys(self, pattern: str):
        """Get keys matching pattern (simplified)"""
        with self.lock:
            if pattern.endswith("*"):
                prefix = pattern[:-1]
                return [key for key in self.data.keys() if key.startswith(prefix)]
            else:
                return [key for key in self.data.keys() if key == pattern]
    
    async def close(self):
        """Close connection (no-op for in-memory)"""
        pass


class SimplifiedRedisManager:
    """Simplified Redis Manager that works without external dependencies"""
    
    def __init__(self):
        self.client = InMemoryRedisClient()
        self.logger = logging.getLogger(f"{__name__}.SimplifiedRedisManager")
        
    async def test_connection(self) -> bool:
        """Test Redis connection"""
        try:
            result = await self.client.ping()
            if result:
                # Test basic operations
                await self.client.set("test_key", "test_value")
                value = await self.client.get("test_key")
                await self.client.delete("test_key")
                
                if value == "test_value":
                    self.logger.info("✅ In-memory Redis connection test successful")
                    return True
            
            self.logger.error("❌ In-memory Redis connection test failed")
            return False
            
        except Exception as e:
            self.logger.error(f"❌ Redis connection test failed: {e}")
            return False
    
    def serialize_trading_object(self, obj: Any) -> str:
        """Serialize trading objects for storage"""
        try:
            if is_dataclass(obj):
                return json.dumps(asdict(obj), default=str)
            elif isinstance(obj, dict):
                return json.dumps(obj, default=str)
            elif hasattr(obj, '__dict__'):
                return json.dumps(obj.__dict__, default=str)
            else:
                return json.dumps(obj, default=str)
        except Exception as e:
            self.logger.warning(f"JSON serialization failed, using pickle: {e}")
            return pickle.dumps(obj).hex()
    
    def deserialize_trading_object(self, data: str) -> Any:
        """Deserialize trading objects from storage"""
        try:
            return json.loads(data)
        except json.JSONDecodeError:
            try:
                return pickle.loads(bytes.fromhex(data))
            except Exception as e:
                self.logger.error(f"Failed to deserialize object: {e}")
                return None
    
    async def store_market_data(self, symbol: str, data: Dict[str, Any], ttl: int = 300) -> bool:
        """Store real-time market data"""
        try:
            key = f"market_data:{symbol}"
            serialized_data = self.serialize_trading_object(data)
            await self.client.setex(key, ttl, serialized_data)
            
            # Add to time-series
            timestamp = int(datetime.now().timestamp())
            ts_key = f"market_data_ts:{symbol}"
            await self.client.zadd(ts_key, {serialized_data: timestamp})
            
            return True
        except Exception as e:
            self.logger.error(f"Failed to store market data for {symbol}: {e}")
            return False
    
    async def get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get latest market data"""
        try:
            key = f"market_data:{symbol}"
            data = await self.client.get(key)
            if data:
                return self.deserialize_trading_object(data)
            return None
        except Exception as e:
            self.logger.error(f"Failed to get market data for {symbol}: {e}")
            return None
    
    async def store_price_feed(self, symbol: str, price: float, volume: float = 0) -> bool:
        """Store real-time price feed"""
        try:
            price_key = f"price:{symbol}"
            price_data = {
                "price": price,
                "volume": volume,
                "timestamp": datetime.now().isoformat()
            }
            await self.client.setex(price_key, 300, self.serialize_trading_object(price_data))
            return True
        except Exception as e:
            self.logger.error(f"Failed to store price feed for {symbol}: {e}")
            return False
    
    async def get_latest_price(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get latest price"""
        try:
            price_key = f"price:{symbol}"
            data = await self.client.get(price_key)
            if data:
                return self.deserialize_trading_object(data)
            return None
        except Exception as e:
            self.logger.error(f"Failed to get latest price for {symbol}: {e}")
            return None
    
    async def store_portfolio_state(self, portfolio_id: str, state: Dict[str, Any], ttl: int = 60) -> bool:
        """Store portfolio state"""
        try:
            key = f"portfolio:{portfolio_id}"
            serialized_state = self.serialize_trading_object(state)
            await self.client.setex(key, ttl, serialized_state)
            return True
        except Exception as e:
            self.logger.error(f"Failed to store portfolio state {portfolio_id}: {e}")
            return False
    
    async def get_portfolio_state(self, portfolio_id: str) -> Optional[Dict[str, Any]]:
        """Get portfolio state"""
        try:
            key = f"portfolio:{portfolio_id}"
            data = await self.client.get(key)
            if data:
                return self.deserialize_trading_object(data)
            return None
        except Exception as e:
            self.logger.error(f"Failed to get portfolio state {portfolio_id}: {e}")
            return None


# Global instance
simplified_redis_manager = SimplifiedRedisManager()
