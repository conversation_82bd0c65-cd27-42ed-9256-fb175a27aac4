"""
Noryon V2 - Exchange Manager
Unified interface for multiple cryptocurrency exchanges
"""

import asyncio
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Union, Tuple
from decimal import Decimal
import json
from dataclasses import dataclass
from enum import Enum

import ccxt.async_support as ccxt
import websockets
import aiohttp

from src.core.config import Config
from src.core.logger import get_logger, get_performance_logger, get_trading_logger
from .mock_exchange import MockExchange

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"

class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    PENDING = "pending"
    OPEN = "open"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"

@dataclass
class OrderBook:
    """Order book data structure"""
    symbol: str
    bids: List[Tuple[float, float]]  # [(price, quantity), ...]
    asks: List[Tuple[float, float]]
    timestamp: datetime
    exchange: str

@dataclass
class Ticker:
    """Ticker data structure"""
    symbol: str
    last: float
    bid: float
    ask: float
    high: float
    low: float
    volume: float
    change: float
    percentage: float
    timestamp: datetime
    exchange: str

@dataclass
class Trade:
    """Trade data structure"""
    id: str
    symbol: str
    side: str
    amount: float
    price: float
    cost: float
    fee: float
    timestamp: datetime
    exchange: str

@dataclass
class Order:
    """Order data structure"""
    id: str
    symbol: str
    type: str
    side: str
    amount: float
    price: Optional[float]
    filled: float
    remaining: float
    status: str
    timestamp: datetime
    exchange: str
    fees: Dict[str, Any]

@dataclass
class Balance:
    """Balance data structure"""
    currency: str
    free: float
    used: float
    total: float
    exchange: str

class ExchangeConnector:
    """Base class for exchange connectors"""
    
    def __init__(self, exchange_name: str, config: Config):
        self.exchange_name = exchange_name
        self.config = config
        self.logger = get_logger(f"exchange.{exchange_name}")
        self.performance_logger = get_performance_logger(f"exchange.{exchange_name}")
        self.trading_logger = get_trading_logger(f"exchange.{exchange_name}")
        
        self.exchange = None
        self.websocket_connections = {}
        self.is_connected = False
        self.last_heartbeat = datetime.utcnow()
        
        # Rate limiting
        self.rate_limits = {
            'requests_per_second': 10,
            'requests_per_minute': 1200,
            'last_request_time': 0,
            'request_count': 0
        }
    
    async def initialize(self):
        """Initialize the exchange connector"""
        raise NotImplementedError
    
    async def connect(self):
        """Connect to the exchange"""
        raise NotImplementedError
    
    async def disconnect(self):
        """Disconnect from the exchange"""
        raise NotImplementedError
    
    async def get_ticker(self, symbol: str) -> Optional[Ticker]:
        """Get ticker data for a symbol"""
        raise NotImplementedError
    
    async def get_order_book(self, symbol: str, limit: int = 100) -> Optional[OrderBook]:
        """Get order book for a symbol"""
        raise NotImplementedError
    
    async def get_balance(self) -> Dict[str, Balance]:
        """Get account balance"""
        raise NotImplementedError
    
    async def create_order(self, symbol: str, order_type: OrderType, side: OrderSide, 
                          amount: float, price: Optional[float] = None, 
                          params: Dict = None) -> Optional[Order]:
        """Create an order"""
        raise NotImplementedError
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        raise NotImplementedError
    
    async def get_order(self, order_id: str, symbol: str) -> Optional[Order]:
        """Get order details"""
        raise NotImplementedError
    
    async def get_trades(self, symbol: str, limit: int = 100) -> List[Trade]:
        """Get recent trades"""
        raise NotImplementedError

class BinanceConnector(ExchangeConnector):
    """Binance exchange connector"""
    
    def __init__(self, config: Config):
        super().__init__("binance", config)
        self.sandbox = config.BINANCE_TESTNET
    
    async def initialize(self):
        """Initialize Binance connector"""
        try:
            self.exchange = ccxt.binance({
                'apiKey': self.config.BINANCE_API_KEY,
                'secret': self.config.BINANCE_SECRET_KEY,
                'sandbox': self.sandbox,
                'enableRateLimit': True,
                'rateLimit': 100,
                'options': {
                    'defaultType': 'spot',
                    'adjustForTimeDifference': True
                }
            })
            
            await self.exchange.load_markets()
            self.is_connected = True
            self.logger.info("✅ Binance connector initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Binance: {e}")
            raise
    
    async def connect(self):
        """Connect to Binance"""
        if not self.is_connected:
            await self.initialize()
    
    async def disconnect(self):
        """Disconnect from Binance"""
        if self.exchange:
            await self.exchange.close()
        self.is_connected = False
    
    async def get_ticker(self, symbol: str) -> Optional[Ticker]:
        """Get Binance ticker"""
        try:
            with self.performance_logger.timer("get_ticker", symbol=symbol):
                ticker_data = await self.exchange.fetch_ticker(symbol)
                
                return Ticker(
                    symbol=symbol,
                    last=ticker_data['last'],
                    bid=ticker_data['bid'],
                    ask=ticker_data['ask'],
                    high=ticker_data['high'],
                    low=ticker_data['low'],
                    volume=ticker_data['baseVolume'],
                    change=ticker_data['change'],
                    percentage=ticker_data['percentage'],
                    timestamp=datetime.fromtimestamp(ticker_data['timestamp'] / 1000),
                    exchange="binance"
                )
        except Exception as e:
            self.logger.error(f"Error getting ticker for {symbol}: {e}")
            return None
    
    async def get_order_book(self, symbol: str, limit: int = 100) -> Optional[OrderBook]:
        """Get Binance order book"""
        try:
            with self.performance_logger.timer("get_order_book", symbol=symbol):
                book_data = await self.exchange.fetch_order_book(symbol, limit)
                
                return OrderBook(
                    symbol=symbol,
                    bids=[(float(price), float(amount)) for price, amount in book_data['bids']],
                    asks=[(float(price), float(amount)) for price, amount in book_data['asks']],
                    timestamp=datetime.fromtimestamp(book_data['timestamp'] / 1000),
                    exchange="binance"
                )
        except Exception as e:
            self.logger.error(f"Error getting order book for {symbol}: {e}")
            return None
    
    async def get_balance(self) -> Dict[str, Balance]:
        """Get Binance balance"""
        try:
            with self.performance_logger.timer("get_balance"):
                balance_data = await self.exchange.fetch_balance()
                
                balances = {}
                for currency, data in balance_data.items():
                    if isinstance(data, dict) and 'free' in data:
                        balances[currency] = Balance(
                            currency=currency,
                            free=float(data['free']),
                            used=float(data['used']),
                            total=float(data['total']),
                            exchange="binance"
                        )
                
                return balances
        except Exception as e:
            self.logger.error(f"Error getting balance: {e}")
            return {}
    
    async def create_order(self, symbol: str, order_type: OrderType, side: OrderSide, 
                          amount: float, price: Optional[float] = None, 
                          params: Dict = None) -> Optional[Order]:
        """Create Binance order"""
        try:
            with self.performance_logger.timer("create_order", symbol=symbol, type=order_type.value, side=side.value):
                order_data = await self.exchange.create_order(
                    symbol=symbol,
                    type=order_type.value,
                    side=side.value,
                    amount=amount,
                    price=price,
                    params=params or {}
                )
                
                order = Order(
                    id=order_data['id'],
                    symbol=symbol,
                    type=order_type.value,
                    side=side.value,
                    amount=float(order_data['amount']),
                    price=float(order_data['price']) if order_data['price'] else None,
                    filled=float(order_data['filled']),
                    remaining=float(order_data['remaining']),
                    status=order_data['status'],
                    timestamp=datetime.fromtimestamp(order_data['timestamp'] / 1000),
                    exchange="binance",
                    fees=order_data.get('fees', {})
                )
                
                self.trading_logger.log_order(
                    order_type=order_type.value,
                    symbol=symbol,
                    quantity=amount,
                    price=price or 0,
                    order_id=order.id,
                    status=order.status
                )
                
                return order
                
        except Exception as e:
            self.logger.error(f"Error creating order: {e}")
            return None
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel Binance order"""
        try:
            with self.performance_logger.timer("cancel_order", order_id=order_id):
                await self.exchange.cancel_order(order_id, symbol)
                return True
        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    async def get_order(self, order_id: str, symbol: str) -> Optional[Order]:
        """Get Binance order details"""
        try:
            with self.performance_logger.timer("get_order", order_id=order_id):
                order_data = await self.exchange.fetch_order(order_id, symbol)
                
                return Order(
                    id=order_data['id'],
                    symbol=symbol,
                    type=order_data['type'],
                    side=order_data['side'],
                    amount=float(order_data['amount']),
                    price=float(order_data['price']) if order_data['price'] else None,
                    filled=float(order_data['filled']),
                    remaining=float(order_data['remaining']),
                    status=order_data['status'],
                    timestamp=datetime.fromtimestamp(order_data['timestamp'] / 1000),
                    exchange="binance",
                    fees=order_data.get('fees', {})
                )
        except Exception as e:
            self.logger.error(f"Error getting order {order_id}: {e}")
            return None
    
    async def get_trades(self, symbol: str, limit: int = 100) -> List[Trade]:
        """Get Binance recent trades"""
        try:
            with self.performance_logger.timer("get_trades", symbol=symbol):
                trades_data = await self.exchange.fetch_trades(symbol, limit=limit)
                
                trades = []
                for trade_data in trades_data:
                    trades.append(Trade(
                        id=trade_data['id'],
                        symbol=symbol,
                        side=trade_data['side'],
                        amount=float(trade_data['amount']),
                        price=float(trade_data['price']),
                        cost=float(trade_data['cost']),
                        fee=float(trade_data['fee']['cost']) if trade_data.get('fee') else 0,
                        timestamp=datetime.fromtimestamp(trade_data['timestamp'] / 1000),
                        exchange="binance"
                    ))
                
                return trades
        except Exception as e:
            self.logger.error(f"Error getting trades for {symbol}: {e}")
            return []

class CoinbaseConnector(ExchangeConnector):
    """Coinbase Pro exchange connector"""
    
    def __init__(self, config: Config):
        super().__init__("coinbase", config)
        self.sandbox = config.COINBASE_SANDBOX
    
    async def initialize(self):
        """Initialize Coinbase connector"""
        try:
            self.exchange = ccxt.coinbasepro({
                'apiKey': self.config.COINBASE_API_KEY,
                'secret': self.config.COINBASE_SECRET_KEY,
                'password': self.config.COINBASE_PASSPHRASE,
                'sandbox': self.sandbox,
                'enableRateLimit': True,
                'rateLimit': 100
            })
            
            await self.exchange.load_markets()
            self.is_connected = True
            self.logger.info("✅ Coinbase connector initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Coinbase: {e}")
            raise
    
    # Similar implementation as Binance but with Coinbase-specific adaptations
    async def connect(self):
        if not self.is_connected:
            await self.initialize()
    
    async def disconnect(self):
        if self.exchange:
            await self.exchange.close()
        self.is_connected = False
    
    # Implement other methods similar to Binance...

class KrakenConnector(ExchangeConnector):
    """Kraken exchange connector"""
    
    def __init__(self, config: Config):
        super().__init__("kraken", config)
    
    async def initialize(self):
        """Initialize Kraken connector"""
        try:
            self.exchange = ccxt.kraken({
                'apiKey': self.config.KRAKEN_API_KEY,
                'secret': self.config.KRAKEN_SECRET_KEY,
                'enableRateLimit': True,
                'rateLimit': 1000
            })
            
            await self.exchange.load_markets()
            self.is_connected = True
            self.logger.info("✅ Kraken connector initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Kraken: {e}")
            raise
    
    # Similar implementation as Binance...

class BybitConnector(ExchangeConnector):
    """Bybit exchange connector"""
    
    def __init__(self, config: Config):
        super().__init__("bybit", config)
        self.testnet = config.BYBIT_TESTNET
    
    async def initialize(self):
        """Initialize Bybit connector"""
        try:
            self.exchange = ccxt.bybit({
                'apiKey': self.config.BYBIT_API_KEY,
                'secret': self.config.BYBIT_SECRET_KEY,
                'testnet': self.testnet,
                'enableRateLimit': True,
                'rateLimit': 100
            })
            
            await self.exchange.load_markets()
            self.is_connected = True
            self.logger.info("✅ Bybit connector initialized")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Bybit: {e}")
            raise
    
    # Similar implementation as Binance...

class ExchangeManager:
    """Main exchange manager that coordinates all exchange connectors"""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        self.performance_logger = get_performance_logger("exchange_manager")
        
        self.connectors: Dict[str, ExchangeConnector] = {}
        self.active_exchanges: List[str] = []
        
        # Mock exchange for paper trading
        self.mock_exchange: Optional[MockExchange] = None
        
        # Market data cache
        self.ticker_cache: Dict[str, Dict[str, Ticker]] = {}
        self.orderbook_cache: Dict[str, Dict[str, OrderBook]] = {}
        
        # Arbitrage opportunities
        self.arbitrage_opportunities: List[Dict[str, Any]] = []
        
        # WebSocket connections
        self.websocket_tasks: List[asyncio.Task] = []
        
        # Performance metrics
        self.metrics = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'avg_response_time': 0,
            'last_update': datetime.utcnow()
        }
    
    async def initialize(self):
        """Initialize all configured exchange connectors"""
        try:
            self.logger.info("🔗 Initializing exchange connections...")
            
            if self.config.PAPER_TRADING and self.mock_exchange:
                try:
                    await self.mock_exchange.start_simulation()
                    self.active_exchanges.append('mock')
                    self.logger.info("✅ Mock exchange initialized for paper trading")
                except Exception as e:
                    self.logger.error(f"❌ Failed to initialize mock exchange: {e}")
            else:
                # Initialize connectors for configured exchanges
                if self.config.is_exchange_configured("binance"):
                    self.connectors["binance"] = BinanceConnector(self.config)
                    await self.connectors["binance"].initialize()
                    self.active_exchanges.append("binance")
                
                if self.config.is_exchange_configured("coinbase"):
                    self.connectors["coinbase"] = CoinbaseConnector(self.config)
                    await self.connectors["coinbase"].initialize()
                    self.active_exchanges.append("coinbase")
                
                if self.config.is_exchange_configured("kraken"):
                    self.connectors["kraken"] = KrakenConnector(self.config)
                    await self.connectors["kraken"].initialize()
                    self.active_exchanges.append("kraken")
                
                if self.config.is_exchange_configured("bybit"):
                    self.connectors["bybit"] = BybitConnector(self.config)
                    await self.connectors["bybit"].initialize()
                    self.active_exchanges.append("bybit")
            
            if not self.active_exchanges:
                raise ValueError("No exchanges are properly configured")
            
            # Start background tasks
            await self._start_background_tasks()
            
            self.logger.info(f"✅ Exchange manager initialized with {len(self.active_exchanges)} exchanges: {self.active_exchanges}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize exchange manager: {e}")
            raise
    
    async def _start_background_tasks(self):
        """Start background tasks for market data updates"""
        self.websocket_tasks = [
            asyncio.create_task(self._update_market_data()),
            asyncio.create_task(self._detect_arbitrage_opportunities()),
            asyncio.create_task(self._monitor_exchange_health())
        ]
    
    async def get_ticker(self, symbol: str, exchange: str = None) -> Optional[Ticker]:
        """Get ticker data from specified exchange or best available"""
        try:
            # Handle mock exchange for paper trading
            if self.config.PAPER_TRADING and self.mock_exchange:
                if exchange is None or exchange == 'mock':
                    return await self.mock_exchange.get_ticker(symbol)
            
            if exchange and exchange in self.connectors:
                ticker = await self.connectors[exchange].get_ticker(symbol)
                if ticker:
                    # Cache the ticker
                    if exchange not in self.ticker_cache:
                        self.ticker_cache[exchange] = {}
                    self.ticker_cache[exchange][symbol] = ticker
                return ticker
            
            # Get from all exchanges and return the most recent
            tickers = []
            for exchange_name, connector in self.connectors.items():
                ticker = await connector.get_ticker(symbol)
                if ticker:
                    tickers.append(ticker)
            
            if tickers:
                # Return the most recent ticker
                return max(tickers, key=lambda t: t.timestamp)
            
        except Exception as e:
            self.logger.error(f"Error getting ticker for {symbol}: {e}")
            
        return None
    
    async def get_all_tickers(self, symbol: str) -> Dict[str, Ticker]:
        """Get ticker data from all exchanges"""
        tickers = {}
        
        tasks = []
        for exchange_name, connector in self.connectors.items():
            tasks.append(self._get_ticker_with_exchange(connector, symbol, exchange_name))
        
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        for result in results:
            if isinstance(result, tuple) and len(result) == 2:
                exchange_name, ticker = result
                if ticker:
                    tickers[exchange_name] = ticker
        
        return tickers
    
    async def _get_ticker_with_exchange(self, connector: ExchangeConnector, symbol: str, exchange_name: str) -> Tuple[str, Optional[Ticker]]:
        """Helper method to get ticker with exchange name"""
        ticker = await connector.get_ticker(symbol)
        return exchange_name, ticker
    
    async def get_order_book(self, symbol: str, exchange: str = None, limit: int = 100) -> Optional[OrderBook]:
        """Get order book data from specified exchange or best available"""
        try:
            # Handle mock exchange for paper trading
            if self.config.PAPER_TRADING and self.mock_exchange:
                if exchange is None or exchange == 'mock':
                    return await self.mock_exchange.get_order_book(symbol, limit)
            
            if exchange and exchange in self.connectors:
                order_book = await self.connectors[exchange].get_order_book(symbol, limit)
                if order_book:
                     # Cache the order book
                     if exchange not in self.order_book_cache:
                         self.order_book_cache[exchange] = {}
                     self.order_book_cache[exchange][symbol] = order_book
                return order_book
            
            # Get from the exchange with the best liquidity
            best_exchange = await self._get_best_exchange_for_symbol(symbol)
            if best_exchange and best_exchange in self.connectors:
                return await self.connectors[best_exchange].get_order_book(symbol, limit)
            
        except Exception as e:
            self.logger.error(f"Error getting order book for {symbol}: {e}")
            
        return None
    
    async def get_balance(self, exchange: str = None) -> Dict[str, Dict[str, Balance]]:
        """Get balance from specified exchange or all exchanges"""
        balances = {}
        
        try:
            # Handle mock exchange for paper trading
            if self.config.PAPER_TRADING and self.mock_exchange:
                if exchange is None or exchange == 'mock':
                    mock_balances = await self.mock_exchange.get_balance()
                    balances['mock'] = mock_balances
                    return balances
            
            if exchange and exchange in self.connectors:
                balances[exchange] = await self.connectors[exchange].get_balance()
            else:
                for exchange_name, connector in self.connectors.items():
                    try:
                        balances[exchange_name] = await connector.get_balance()
                    except Exception as e:
                        self.logger.error(f"Error getting balance from {exchange_name}: {e}")
                        balances[exchange_name] = {}
        
        except Exception as e:
            self.logger.error(f"Error getting balance: {e}")
        
        return balances
    
    async def create_order(self, symbol: str, order_type: OrderType, side: OrderSide, 
                          amount: float, price: Optional[float] = None, 
                          exchange: str = None, params: Dict = None) -> Optional[Order]:
        """Create order on specified exchange or best available"""
        try:
            # Handle mock exchange for paper trading
            if self.config.PAPER_TRADING and self.mock_exchange:
                if exchange is None or exchange == 'mock':
                    order = await self.mock_exchange.create_order(
                        symbol, order_type, side, amount, price, params
                    )
                    
                    if order:
                        self.logger.info(
                            f"📝 Paper trade order created: {order.side} {order.amount} {order.symbol} "
                            f"@ {order.price or 'MARKET'} on mock exchange"
                        )
                    
                    return order
            
            target_exchange = exchange or await self._get_best_exchange_for_trading(symbol, side)
            
            if target_exchange and target_exchange in self.connectors:
                order = await self.connectors[target_exchange].create_order(
                    symbol, order_type, side, amount, price, params
                )
                
                if order:
                    self.logger.info(
                        f"💰 Live order created: {order.side} {order.amount} {order.symbol} "
                        f"@ {order.price or 'MARKET'} on {target_exchange}"
                    )
                
                return order
            else:
                self.logger.error(f"No suitable exchange found for trading {symbol}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error creating order: {e}")
            return None
    
    async def cancel_order(self, order_id: str, symbol: str, exchange: str) -> bool:
        """Cancel order on specified exchange"""
        try:
            # Handle mock exchange for paper trading
            if self.config.PAPER_TRADING and self.mock_exchange:
                if exchange == 'mock':
                    result = await self.mock_exchange.cancel_order(order_id, symbol)
                    if result:
                        self.logger.info(f"📝 Paper trade order cancelled: {order_id} on mock exchange")
                    return result
            
            if exchange in self.connectors:
                result = await self.connectors[exchange].cancel_order(order_id, symbol)
                if result:
                    self.logger.info(f"💰 Live order cancelled: {order_id} on {exchange}")
                return result
                
        except Exception as e:
            self.logger.error(f"Error cancelling order {order_id}: {e}")
            
        return False
    
    async def get_order(self, order_id: str, symbol: str, exchange: str) -> Optional[Order]:
        """Get order details from specified exchange"""
        try:
            # Handle mock exchange for paper trading
            if self.config.PAPER_TRADING and self.mock_exchange:
                if exchange == 'mock':
                    return await self.mock_exchange.get_order(order_id, symbol)
            
            if exchange in self.connectors:
                return await self.connectors[exchange].get_order(order_id, symbol)
                
        except Exception as e:
            self.logger.error(f"Error getting order {order_id}: {e}")
            
        return None
    
    async def get_arbitrage_opportunities(self, min_profit_threshold: float = 0.005) -> List[Dict[str, Any]]:
        """Get current arbitrage opportunities"""
        opportunities = []
        
        for symbol in self.config.TRADING_PAIRS:
            tickers = await self.get_all_tickers(symbol)
            
            if len(tickers) < 2:
                continue
            
            # Find price differences
            prices = [(exchange, ticker.last) for exchange, ticker in tickers.items()]
            prices.sort(key=lambda x: x[1])
            
            lowest_exchange, lowest_price = prices[0]
            highest_exchange, highest_price = prices[-1]
            
            profit_percentage = (highest_price - lowest_price) / lowest_price
            
            if profit_percentage >= min_profit_threshold:
                opportunities.append({
                    'symbol': symbol,
                    'buy_exchange': lowest_exchange,
                    'sell_exchange': highest_exchange,
                    'buy_price': lowest_price,
                    'sell_price': highest_price,
                    'profit_percentage': profit_percentage,
                    'profit_absolute': highest_price - lowest_price,
                    'timestamp': datetime.utcnow()
                })
        
        return opportunities
    
    async def execute_arbitrage(self, opportunity: Dict[str, Any], amount: float) -> Dict[str, Any]:
        """Execute an arbitrage opportunity"""
        try:
            symbol = opportunity['symbol']
            buy_exchange = opportunity['buy_exchange']
            sell_exchange = opportunity['sell_exchange']
            
            # Create buy order on lower price exchange
            buy_order = await self.create_order(
                symbol=symbol,
                order_type=OrderType.MARKET,
                side=OrderSide.BUY,
                amount=amount,
                exchange=buy_exchange
            )
            
            if not buy_order:
                return {'success': False, 'error': 'Failed to create buy order'}
            
            # Create sell order on higher price exchange
            sell_order = await self.create_order(
                symbol=symbol,
                order_type=OrderType.MARKET,
                side=OrderSide.SELL,
                amount=amount,
                exchange=sell_exchange
            )
            
            if not sell_order:
                # Cancel buy order if sell order fails
                await self.cancel_order(buy_order.id, symbol, buy_exchange)
                return {'success': False, 'error': 'Failed to create sell order'}
            
            return {
                'success': True,
                'buy_order': buy_order,
                'sell_order': sell_order,
                'expected_profit': opportunity['profit_absolute'] * amount
            }
            
        except Exception as e:
            self.logger.error(f"Error executing arbitrage: {e}")
            return {'success': False, 'error': str(e)}
    
    async def _get_best_exchange_for_symbol(self, symbol: str) -> Optional[str]:
        """Get the best exchange for a symbol based on volume"""
        tickers = await self.get_all_tickers(symbol)
        
        if not tickers:
            return None
        
        # Return exchange with highest volume
        best_exchange = max(tickers.items(), key=lambda x: x[1].volume)
        return best_exchange[0]
    
    async def _get_best_exchange_for_trading(self, symbol: str, side: OrderSide) -> Optional[str]:
        """Get the best exchange for trading based on spread and liquidity"""
        order_books = {}
        
        for exchange_name, connector in self.connectors.items():
            order_book = await connector.get_order_book(symbol, limit=10)
            if order_book:
                order_books[exchange_name] = order_book
        
        if not order_books:
            return None
        
        # Choose exchange with best price for the side
        if side == OrderSide.BUY:
            # For buying, choose exchange with lowest ask
            best_exchange = min(order_books.items(), key=lambda x: x[1].asks[0][0] if x[1].asks else float('inf'))
        else:
            # For selling, choose exchange with highest bid
            best_exchange = max(order_books.items(), key=lambda x: x[1].bids[0][0] if x[1].bids else 0)
        
        return best_exchange[0]
    
    async def _update_market_data(self):
        """Background task to update market data cache"""
        while True:
            try:
                for symbol in self.config.TRADING_PAIRS:
                    tickers = await self.get_all_tickers(symbol)
                    self.ticker_cache[symbol] = tickers
                
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Error updating market data: {e}")
                await asyncio.sleep(5)
    
    async def _detect_arbitrage_opportunities(self):
        """Background task to detect arbitrage opportunities"""
        while True:
            try:
                opportunities = await self.get_arbitrage_opportunities()
                self.arbitrage_opportunities = opportunities
                
                if opportunities:
                    self.logger.info(f"Found {len(opportunities)} arbitrage opportunities")
                
                await asyncio.sleep(10)  # Check every 10 seconds
                
            except Exception as e:
                self.logger.error(f"Error detecting arbitrage opportunities: {e}")
                await asyncio.sleep(10)
    
    async def _monitor_exchange_health(self):
        """Background task to monitor exchange health"""
        while True:
            try:
                for exchange_name, connector in self.connectors.items():
                    # Update heartbeat
                    connector.last_heartbeat = datetime.utcnow()
                    
                    # Check if exchange is responsive
                    try:
                        await connector.get_ticker(self.config.TRADING_PAIRS[0])
                        connector.is_connected = True
                    except Exception:
                        connector.is_connected = False
                        self.logger.warning(f"Exchange {exchange_name} appears unresponsive")
                
                await asyncio.sleep(30)  # Check every 30 seconds
                
            except Exception as e:
                self.logger.error(f"Error monitoring exchange health: {e}")
                await asyncio.sleep(30)
    
    async def get_exchange_status(self) -> Dict[str, Any]:
        """Get status of all exchanges"""
        status = {}
        
        for exchange_name, connector in self.connectors.items():
            status[exchange_name] = {
                'connected': connector.is_connected,
                'last_heartbeat': connector.last_heartbeat,
                'rate_limits': connector.rate_limits
            }
        
        return status
    
    async def get_market_summary(self) -> Dict[str, Any]:
        """Get market summary across all exchanges"""
        summary = {
            'total_exchanges': len(self.active_exchanges),
            'active_exchanges': self.active_exchanges,
            'trading_pairs': len(self.config.TRADING_PAIRS),
            'arbitrage_opportunities': len(self.arbitrage_opportunities),
            'last_update': datetime.utcnow()
        }
        
        return summary
    
    async def close(self):
        """Close all exchange connections and cleanup"""
        self.logger.info("🔌 Closing exchange manager...")
        
        # Cancel background tasks
        for task in self.websocket_tasks:
            if not task.done():
                task.cancel()
                try:
                    await task
                except asyncio.CancelledError:
                    pass
        
        self.websocket_tasks.clear()
        
        # Close mock exchange if running
        if self.mock_exchange:
            try:
                await self.mock_exchange.close()
                self.logger.info("✅ Mock exchange closed")
            except Exception as e:
                self.logger.error(f"❌ Error closing mock exchange: {e}")
        
        # Disconnect from all exchanges
        for name, connector in self.connectors.items():
            try:
                await connector.disconnect()
                self.logger.info(f"✅ Disconnected from {name}")
            except Exception as e:
                self.logger.error(f"❌ Error disconnecting from {name}: {e}")
        
        self.logger.info("🔌 Exchange manager closed")