#!/usr/bin/env python3
"""
Noryon V2 - Mock Exchange
Simulated exchange for paper trading and testing
"""

import asyncio
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal
import json
from dataclasses import dataclass, field
from enum import Enum

from src.core.config import Config
from src.core.logger import get_logger
from .exchange_manager import OrderType, OrderSide, OrderStatus, Ticker, OrderBook, Trade, Order, Balance

class MockExchange:
    """Mock exchange for paper trading and simulation"""
    
    def __init__(self, config: Config = None):
        self.config = config
        self.logger = get_logger(__name__)
        
        # Simulated market data
        self.market_data: Dict[str, Dict[str, Any]] = {}
        self.order_book_data: Dict[str, OrderBook] = {}
        
        # Simulated account data
        self.balances: Dict[str, Balance] = {
            'USDT': Balance(
                currency='USDT',
                free=float(config.INITIAL_BALANCE) if config else 10000.0,
                used=0.0,
                total=float(config.INITIAL_BALANCE) if config else 10000.0,
                exchange='mock'
            )
        }
        
        # Order management
        self.orders: Dict[str, Order] = {}
        self.trades: List[Trade] = []
        self.order_counter = 0
        
        # Market simulation
        self.base_prices = {
            'BTC/USDT': 45000.0,
            'ETH/USDT': 3000.0,
            'BNB/USDT': 300.0,
            'ADA/USDT': 0.5,
            'SOL/USDT': 100.0,
            'XRP/USDT': 0.6,
            'DOT/USDT': 7.0,
            'AVAX/USDT': 35.0,
            'MATIC/USDT': 0.8,
            'LINK/USDT': 15.0
        }
        
        # Initialize market data
        self._initialize_market_data()
        
        # Start price simulation
        self._simulation_task = None
        
    def _initialize_market_data(self):
        """Initialize simulated market data"""
        for symbol, base_price in self.base_prices.items():
            # Generate realistic ticker data
            change_pct = random.uniform(-5, 5)  # -5% to +5% daily change
            current_price = base_price * (1 + change_pct / 100)
            
            self.market_data[symbol] = {
                'price': current_price,
                'volume': random.uniform(1000000, 10000000),
                'high': current_price * random.uniform(1.01, 1.05),
                'low': current_price * random.uniform(0.95, 0.99),
                'change_24h': change_pct,
                'last_update': datetime.utcnow()
            }
            
            # Generate order book
            self._generate_order_book(symbol, current_price)
    
    def _generate_order_book(self, symbol: str, price: float):
        """Generate realistic order book data"""
        spread = price * 0.001  # 0.1% spread
        
        # Generate bids (buy orders)
        bids = []
        for i in range(20):
            bid_price = price - spread/2 - (i * spread * 0.1)
            bid_quantity = random.uniform(0.1, 10.0)
            bids.append((bid_price, bid_quantity))
        
        # Generate asks (sell orders)
        asks = []
        for i in range(20):
            ask_price = price + spread/2 + (i * spread * 0.1)
            ask_quantity = random.uniform(0.1, 10.0)
            asks.append((ask_price, ask_quantity))
        
        self.order_book_data[symbol] = OrderBook(
            symbol=symbol,
            bids=bids,
            asks=asks,
            timestamp=datetime.utcnow(),
            exchange='mock'
        )
    
    async def start_simulation(self):
        """Start price simulation"""
        if not self._simulation_task:
            self._simulation_task = asyncio.create_task(self._simulate_price_movements())
    
    async def stop_simulation(self):
        """Stop price simulation"""
        if self._simulation_task:
            self._simulation_task.cancel()
            try:
                await self._simulation_task
            except asyncio.CancelledError:
                pass
            self._simulation_task = None
    
    async def _simulate_price_movements(self):
        """Simulate realistic price movements"""
        while True:
            try:
                for symbol in self.market_data:
                    # Random walk with mean reversion
                    current_price = self.market_data[symbol]['price']
                    base_price = self.base_prices[symbol]
                    
                    # Mean reversion factor
                    reversion_factor = (base_price - current_price) / base_price * 0.1
                    
                    # Random movement
                    random_change = random.gauss(0, 0.002)  # 0.2% standard deviation
                    
                    # Combine movements
                    total_change = reversion_factor + random_change
                    new_price = current_price * (1 + total_change)
                    
                    # Update market data
                    self.market_data[symbol]['price'] = new_price
                    self.market_data[symbol]['last_update'] = datetime.utcnow()
                    
                    # Update order book
                    self._generate_order_book(symbol, new_price)
                    
                    # Process pending orders
                    await self._process_pending_orders(symbol, new_price)
                
                await asyncio.sleep(1)  # Update every second
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                self.logger.error(f"Error in price simulation: {e}")
                await asyncio.sleep(1)
    
    async def _process_pending_orders(self, symbol: str, current_price: float):
        """Process pending orders that should be filled"""
        for order_id, order in list(self.orders.items()):
            if order.symbol != symbol or order.status != 'open':
                continue
            
            should_fill = False
            fill_price = current_price
            
            if order.type == 'market':
                should_fill = True
            elif order.type == 'limit':
                if order.side == 'buy' and current_price <= order.price:
                    should_fill = True
                    fill_price = order.price
                elif order.side == 'sell' and current_price >= order.price:
                    should_fill = True
                    fill_price = order.price
            
            if should_fill:
                await self._fill_order(order, fill_price)
    
    async def _fill_order(self, order: Order, fill_price: float):
        """Fill an order"""
        try:
            # Update order status
            order.status = 'filled'
            order.filled = order.amount
            order.remaining = 0.0
            order.price = fill_price
            
            # Create trade record
            trade = Trade(
                id=f"trade_{len(self.trades) + 1}",
                symbol=order.symbol,
                side=order.side,
                amount=order.amount,
                price=fill_price,
                cost=order.amount * fill_price,
                fee=order.amount * fill_price * 0.001,  # 0.1% fee
                timestamp=datetime.utcnow(),
                exchange='mock'
            )
            
            self.trades.append(trade)
            
            # Update balances
            await self._update_balances_after_trade(trade)
            
            self.logger.info(f"📈 Mock order filled: {order.side.upper()} {order.amount} {order.symbol} @ ${fill_price:.2f}")
            
        except Exception as e:
            self.logger.error(f"Error filling order: {e}")
    
    async def _update_balances_after_trade(self, trade: Trade):
        """Update account balances after a trade"""
        base_currency, quote_currency = trade.symbol.split('/')
        
        # Initialize currencies if not exist
        if base_currency not in self.balances:
            self.balances[base_currency] = Balance(
                currency=base_currency,
                free=0.0,
                used=0.0,
                total=0.0,
                exchange='mock'
            )
        
        if quote_currency not in self.balances:
            self.balances[quote_currency] = Balance(
                currency=quote_currency,
                free=0.0,
                used=0.0,
                total=0.0,
                exchange='mock'
            )
        
        if trade.side == 'buy':
            # Buying: decrease quote currency, increase base currency
            self.balances[quote_currency].free -= trade.cost + trade.fee
            self.balances[quote_currency].total -= trade.cost + trade.fee
            
            self.balances[base_currency].free += trade.amount
            self.balances[base_currency].total += trade.amount
        else:
            # Selling: decrease base currency, increase quote currency
            self.balances[base_currency].free -= trade.amount
            self.balances[base_currency].total -= trade.amount
            
            self.balances[quote_currency].free += trade.cost - trade.fee
            self.balances[quote_currency].total += trade.cost - trade.fee
    
    # Exchange interface methods
    async def get_ticker(self, symbol: str) -> Optional[Ticker]:
        """Get ticker data for a symbol"""
        if symbol not in self.market_data:
            return None
        
        data = self.market_data[symbol]
        order_book = self.order_book_data.get(symbol)
        
        return Ticker(
            symbol=symbol,
            last=data['price'],
            bid=order_book.bids[0][0] if order_book and order_book.bids else data['price'] * 0.999,
            ask=order_book.asks[0][0] if order_book and order_book.asks else data['price'] * 1.001,
            high=data['high'],
            low=data['low'],
            volume=data['volume'],
            change=data['change_24h'],
            percentage=data['change_24h'],
            timestamp=data['last_update'],
            exchange='mock'
        )
    
    async def get_order_book(self, symbol: str, limit: int = 100) -> Optional[OrderBook]:
        """Get order book for a symbol"""
        return self.order_book_data.get(symbol)
    
    async def get_balance(self) -> Dict[str, Balance]:
        """Get account balance"""
        return self.balances.copy()
    
    async def create_order(self, symbol: str, order_type: OrderType, side: OrderSide,
                          amount: float, price: Optional[float] = None,
                          params: Dict = None) -> Optional[Order]:
        """Create an order"""
        try:
            self.order_counter += 1
            order_id = f"mock_order_{self.order_counter}"
            
            # Validate order
            if symbol not in self.market_data:
                raise ValueError(f"Symbol {symbol} not supported")
            
            # Check balance for buy orders
            if side == OrderSide.BUY:
                quote_currency = symbol.split('/')[1]
                required_balance = amount * (price or self.market_data[symbol]['price'])
                
                if quote_currency not in self.balances or self.balances[quote_currency].free < required_balance:
                    raise ValueError("Insufficient balance")
            
            # Check balance for sell orders
            if side == OrderSide.SELL:
                base_currency = symbol.split('/')[0]
                
                if base_currency not in self.balances or self.balances[base_currency].free < amount:
                    raise ValueError("Insufficient balance")
            
            # Create order
            order = Order(
                id=order_id,
                symbol=symbol,
                type=order_type.value,
                side=side.value,
                amount=amount,
                price=price,
                filled=0.0,
                remaining=amount,
                status='open',
                timestamp=datetime.utcnow(),
                exchange='mock',
                fees={}
            )
            
            self.orders[order_id] = order
            
            # For market orders, fill immediately
            if order_type == OrderType.MARKET:
                current_price = self.market_data[symbol]['price']
                await self._fill_order(order, current_price)
            
            self.logger.info(f"📝 Mock order created: {order.side.upper()} {order.amount} {order.symbol} @ ${price or 'MARKET'}")
            
            return order
            
        except Exception as e:
            self.logger.error(f"Error creating mock order: {e}")
            return None
    
    async def cancel_order(self, order_id: str, symbol: str) -> bool:
        """Cancel an order"""
        if order_id in self.orders:
            self.orders[order_id].status = 'cancelled'
            self.logger.info(f"❌ Mock order cancelled: {order_id}")
            return True
        return False
    
    async def get_order(self, order_id: str, symbol: str) -> Optional[Order]:
        """Get order details"""
        return self.orders.get(order_id)
    
    async def get_trades(self, symbol: str, limit: int = 100) -> List[Trade]:
        """Get recent trades"""
        symbol_trades = [trade for trade in self.trades if trade.symbol == symbol]
        return symbol_trades[-limit:] if symbol_trades else []
    
    async def close(self):
        """Close the mock exchange"""
        await self.stop_simulation()
        self.logger.info("🔌 Mock exchange closed")