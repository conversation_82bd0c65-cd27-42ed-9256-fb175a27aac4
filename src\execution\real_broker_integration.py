"""
Real Broker Integration for Live Trading
Production-ready order execution with actual brokers and exchanges
"""

import asyncio
import aiohttp
import hmac
import hashlib
import base64
import time
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

logger = logging.getLogger(__name__)


class BrokerType(Enum):
    """Supported broker types"""
    BINANCE = "BINANCE"
    COINBASE_PRO = "COINBASE_PRO"
    KRAKEN = "KRAKEN"
    ALPACA = "ALPACA"
    INTERACTIVE_BROKERS = "INTERACTIVE_BROKERS"


class OrderType(Enum):
    """Order types"""
    MARKET = "MARKET"
    LIMIT = "LIMIT"
    STOP_LOSS = "STOP_LOSS"
    STOP_LOSS_LIMIT = "STOP_LOSS_LIMIT"
    TAKE_PROFIT = "TAKE_PROFIT"
    TAKE_PROFIT_LIMIT = "TAKE_PROFIT_LIMIT"


class OrderSide(Enum):
    """Order sides"""
    BUY = "BUY"
    SELL = "SELL"


class OrderStatus(Enum):
    """Order status"""
    NEW = "NEW"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    FILLED = "FILLED"
    CANCELED = "CANCELED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"


@dataclass
class RealOrder:
    """Real order structure"""
    client_order_id: str
    symbol: str
    side: OrderSide
    order_type: OrderType
    quantity: float
    price: Optional[float] = None
    stop_price: Optional[float] = None
    time_in_force: str = "GTC"  # Good Till Canceled
    
    # Execution details
    broker_order_id: Optional[str] = None
    status: OrderStatus = OrderStatus.NEW
    filled_quantity: float = 0.0
    avg_fill_price: float = 0.0
    commission: float = 0.0
    
    # Timestamps
    created_time: Optional[datetime] = None
    updated_time: Optional[datetime] = None
    
    # Metadata
    broker: Optional[str] = None
    raw_response: Optional[Dict[str, Any]] = None


@dataclass
class OrderFill:
    """Order fill/execution details"""
    fill_id: str
    order_id: str
    symbol: str
    side: OrderSide
    quantity: float
    price: float
    commission: float
    commission_asset: str
    timestamp: datetime
    trade_id: Optional[str] = None


@dataclass
class Position:
    """Trading position"""
    symbol: str
    side: str  # LONG or SHORT
    quantity: float
    avg_price: float
    unrealized_pnl: float
    realized_pnl: float
    margin_used: float
    timestamp: datetime


@dataclass
class AccountBalance:
    """Account balance information"""
    asset: str
    free: float
    locked: float
    total: float


class BinanceBroker:
    """Real Binance broker integration"""
    
    def __init__(self, api_key: str, api_secret: str, testnet: bool = True):
        self.api_key = api_key
        self.api_secret = api_secret
        self.testnet = testnet
        
        if testnet:
            self.base_url = "https://testnet.binance.vision"
        else:
            self.base_url = "https://api.binance.com"
            
        self.session = None
        self.logger = logging.getLogger(f"{__name__}.BinanceBroker")
        
    async def initialize(self):
        """Initialize broker connection"""
        try:
            self.session = aiohttp.ClientSession()
            
            # Test API connectivity and permissions
            account_info = await self.get_account_info()
            if account_info:
                self.logger.info("✅ Binance broker connection established")
                return True
            else:
                self.logger.error("❌ Failed to establish Binance broker connection")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize Binance broker: {e}")
            return False
    
    def _generate_signature(self, query_string: str) -> str:
        """Generate HMAC SHA256 signature"""
        return hmac.new(
            self.api_secret.encode('utf-8'),
            query_string.encode('utf-8'),
            hashlib.sha256
        ).hexdigest()
    
    def _get_headers(self) -> Dict[str, str]:
        """Get request headers"""
        return {
            'X-MBX-APIKEY': self.api_key,
            'Content-Type': 'application/json'
        }
    
    async def get_account_info(self) -> Optional[Dict[str, Any]]:
        """Get account information"""
        try:
            timestamp = int(time.time() * 1000)
            query_string = f"timestamp={timestamp}"
            signature = self._generate_signature(query_string)
            
            url = f"{self.base_url}/api/v3/account"
            params = {
                "timestamp": timestamp,
                "signature": signature
            }
            
            async with self.session.get(url, params=params, headers=self._get_headers()) as response:
                if response.status == 200:
                    account_data = await response.json()
                    self.logger.info(f"📊 Account info retrieved: {len(account_data['balances'])} balances")
                    return account_data
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Failed to get account info: {response.status} - {error_text}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"❌ Error getting account info: {e}")
            return None
    
    async def get_balances(self) -> List[AccountBalance]:
        """Get account balances"""
        try:
            account_info = await self.get_account_info()
            if not account_info:
                return []
            
            balances = []
            for balance_data in account_info['balances']:
                free = float(balance_data['free'])
                locked = float(balance_data['locked'])
                
                if free > 0 or locked > 0:  # Only include non-zero balances
                    balance = AccountBalance(
                        asset=balance_data['asset'],
                        free=free,
                        locked=locked,
                        total=free + locked
                    )
                    balances.append(balance)
            
            self.logger.info(f"💰 Retrieved {len(balances)} non-zero balances")
            return balances
            
        except Exception as e:
            self.logger.error(f"❌ Error getting balances: {e}")
            return []
    
    async def place_order(self, order: RealOrder) -> Optional[RealOrder]:
        """Place a real order"""
        try:
            timestamp = int(time.time() * 1000)
            
            # Build order parameters
            params = {
                "symbol": order.symbol,
                "side": order.side.value,
                "type": order.order_type.value,
                "quantity": f"{order.quantity:.8f}",
                "newClientOrderId": order.client_order_id,
                "timestamp": timestamp
            }
            
            # Add price for limit orders
            if order.order_type in [OrderType.LIMIT, OrderType.STOP_LOSS_LIMIT, OrderType.TAKE_PROFIT_LIMIT]:
                if order.price:
                    params["price"] = f"{order.price:.8f}"
            
            # Add stop price for stop orders
            if order.order_type in [OrderType.STOP_LOSS, OrderType.STOP_LOSS_LIMIT]:
                if order.stop_price:
                    params["stopPrice"] = f"{order.stop_price:.8f}"
            
            # Add time in force
            if order.order_type != OrderType.MARKET:
                params["timeInForce"] = order.time_in_force
            
            # Create query string and signature
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            signature = self._generate_signature(query_string)
            params["signature"] = signature
            
            url = f"{self.base_url}/api/v3/order"
            
            async with self.session.post(url, data=params, headers=self._get_headers()) as response:
                if response.status == 200:
                    response_data = await response.json()
                    
                    # Update order with response data
                    order.broker_order_id = str(response_data["orderId"])
                    order.status = OrderStatus(response_data["status"])
                    order.created_time = datetime.fromtimestamp(response_data["transactTime"] / 1000)
                    order.updated_time = order.created_time
                    order.broker = "BINANCE"
                    order.raw_response = response_data
                    
                    # Handle fills if any
                    if "fills" in response_data:
                        total_qty = 0.0
                        total_cost = 0.0
                        total_commission = 0.0
                        
                        for fill in response_data["fills"]:
                            fill_qty = float(fill["qty"])
                            fill_price = float(fill["price"])
                            fill_commission = float(fill["commission"])
                            
                            total_qty += fill_qty
                            total_cost += fill_qty * fill_price
                            total_commission += fill_commission
                        
                        if total_qty > 0:
                            order.filled_quantity = total_qty
                            order.avg_fill_price = total_cost / total_qty
                            order.commission = total_commission
                    
                    self.logger.info(f"✅ Order placed: {order.client_order_id} - "
                                   f"{order.side.value} {order.quantity} {order.symbol} @ "
                                   f"{order.price or 'MARKET'}")
                    return order
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Failed to place order: {response.status} - {error_text}")
                    
                    # Update order status to rejected
                    order.status = OrderStatus.REJECTED
                    order.updated_time = datetime.now()
                    return order
                    
        except Exception as e:
            self.logger.error(f"❌ Error placing order: {e}")
            order.status = OrderStatus.REJECTED
            order.updated_time = datetime.now()
            return order
    
    async def cancel_order(self, symbol: str, order_id: str) -> bool:
        """Cancel an existing order"""
        try:
            timestamp = int(time.time() * 1000)
            
            params = {
                "symbol": symbol,
                "orderId": order_id,
                "timestamp": timestamp
            }
            
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            signature = self._generate_signature(query_string)
            params["signature"] = signature
            
            url = f"{self.base_url}/api/v3/order"
            
            async with self.session.delete(url, params=params, headers=self._get_headers()) as response:
                if response.status == 200:
                    response_data = await response.json()
                    self.logger.info(f"✅ Order canceled: {order_id}")
                    return True
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Failed to cancel order: {response.status} - {error_text}")
                    return False
                    
        except Exception as e:
            self.logger.error(f"❌ Error canceling order: {e}")
            return False
    
    async def get_order_status(self, symbol: str, order_id: str) -> Optional[RealOrder]:
        """Get order status"""
        try:
            timestamp = int(time.time() * 1000)
            
            params = {
                "symbol": symbol,
                "orderId": order_id,
                "timestamp": timestamp
            }
            
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            signature = self._generate_signature(query_string)
            params["signature"] = signature
            
            url = f"{self.base_url}/api/v3/order"
            
            async with self.session.get(url, params=params, headers=self._get_headers()) as response:
                if response.status == 200:
                    response_data = await response.json()
                    
                    # Convert to RealOrder
                    order = RealOrder(
                        client_order_id=response_data["clientOrderId"],
                        symbol=response_data["symbol"],
                        side=OrderSide(response_data["side"]),
                        order_type=OrderType(response_data["type"]),
                        quantity=float(response_data["origQty"]),
                        price=float(response_data["price"]) if response_data["price"] != "0.00000000" else None,
                        broker_order_id=str(response_data["orderId"]),
                        status=OrderStatus(response_data["status"]),
                        filled_quantity=float(response_data["executedQty"]),
                        avg_fill_price=float(response_data["price"]) if response_data["price"] != "0.00000000" else 0.0,
                        created_time=datetime.fromtimestamp(response_data["time"] / 1000),
                        updated_time=datetime.fromtimestamp(response_data["updateTime"] / 1000),
                        broker="BINANCE",
                        raw_response=response_data
                    )
                    
                    return order
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Failed to get order status: {response.status} - {error_text}")
                    return None
                    
        except Exception as e:
            self.logger.error(f"❌ Error getting order status: {e}")
            return None
    
    async def get_open_orders(self, symbol: Optional[str] = None) -> List[RealOrder]:
        """Get all open orders"""
        try:
            timestamp = int(time.time() * 1000)
            
            params = {
                "timestamp": timestamp
            }
            
            if symbol:
                params["symbol"] = symbol
            
            query_string = "&".join([f"{k}={v}" for k, v in params.items()])
            signature = self._generate_signature(query_string)
            params["signature"] = signature
            
            url = f"{self.base_url}/api/v3/openOrders"
            
            async with self.session.get(url, params=params, headers=self._get_headers()) as response:
                if response.status == 200:
                    orders_data = await response.json()
                    
                    orders = []
                    for order_data in orders_data:
                        order = RealOrder(
                            client_order_id=order_data["clientOrderId"],
                            symbol=order_data["symbol"],
                            side=OrderSide(order_data["side"]),
                            order_type=OrderType(order_data["type"]),
                            quantity=float(order_data["origQty"]),
                            price=float(order_data["price"]) if order_data["price"] != "0.00000000" else None,
                            broker_order_id=str(order_data["orderId"]),
                            status=OrderStatus(order_data["status"]),
                            filled_quantity=float(order_data["executedQty"]),
                            created_time=datetime.fromtimestamp(order_data["time"] / 1000),
                            updated_time=datetime.fromtimestamp(order_data["updateTime"] / 1000),
                            broker="BINANCE",
                            raw_response=order_data
                        )
                        orders.append(order)
                    
                    self.logger.info(f"📋 Retrieved {len(orders)} open orders")
                    return orders
                else:
                    error_text = await response.text()
                    self.logger.error(f"❌ Failed to get open orders: {response.status} - {error_text}")
                    return []
                    
        except Exception as e:
            self.logger.error(f"❌ Error getting open orders: {e}")
            return []
    
    async def close(self):
        """Close broker connection"""
        try:
            if self.session:
                await self.session.close()
            self.logger.info("🔌 Binance broker connection closed")
        except Exception as e:
            self.logger.error(f"❌ Error closing Binance broker: {e}")


class RealBrokerManager:
    """Manager for real broker integrations"""
    
    def __init__(self):
        self.brokers = {}
        self.active_orders = {}
        self.order_history = []
        self.logger = logging.getLogger(f"{__name__}.RealBrokerManager")
    
    async def initialize_broker(self, broker_type: BrokerType, **credentials) -> bool:
        """Initialize broker connection"""
        try:
            if broker_type == BrokerType.BINANCE:
                broker = BinanceBroker(
                    api_key=credentials["api_key"],
                    api_secret=credentials["api_secret"],
                    testnet=credentials.get("testnet", True)
                )
            else:
                self.logger.error(f"❌ Unsupported broker type: {broker_type}")
                return False
            
            success = await broker.initialize()
            if success:
                self.brokers[broker_type] = broker
                self.logger.info(f"✅ Initialized {broker_type.value} broker")
                return True
            else:
                self.logger.error(f"❌ Failed to initialize {broker_type.value} broker")
                return False
                
        except Exception as e:
            self.logger.error(f"❌ Error initializing {broker_type.value} broker: {e}")
            return False
    
    async def place_order(self, broker_type: BrokerType, order: RealOrder) -> Optional[RealOrder]:
        """Place order with specific broker"""
        try:
            if broker_type not in self.brokers:
                self.logger.error(f"❌ Broker {broker_type.value} not initialized")
                return None
            
            broker = self.brokers[broker_type]
            executed_order = await broker.place_order(order)
            
            if executed_order:
                self.active_orders[executed_order.client_order_id] = executed_order
                self.order_history.append(executed_order)
                
                # Keep only recent history
                if len(self.order_history) > 1000:
                    self.order_history = self.order_history[-1000:]
            
            return executed_order
            
        except Exception as e:
            self.logger.error(f"❌ Error placing order with {broker_type.value}: {e}")
            return None
    
    async def cancel_order(self, broker_type: BrokerType, symbol: str, order_id: str) -> bool:
        """Cancel order with specific broker"""
        try:
            if broker_type not in self.brokers:
                self.logger.error(f"❌ Broker {broker_type.value} not initialized")
                return False
            
            broker = self.brokers[broker_type]
            success = await broker.cancel_order(symbol, order_id)
            
            if success:
                # Update local order status
                for order in self.active_orders.values():
                    if order.broker_order_id == order_id:
                        order.status = OrderStatus.CANCELED
                        order.updated_time = datetime.now()
                        break
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Error canceling order with {broker_type.value}: {e}")
            return False
    
    async def get_account_balances(self, broker_type: BrokerType) -> List[AccountBalance]:
        """Get account balances from specific broker"""
        try:
            if broker_type not in self.brokers:
                self.logger.error(f"❌ Broker {broker_type.value} not initialized")
                return []
            
            broker = self.brokers[broker_type]
            return await broker.get_balances()
            
        except Exception as e:
            self.logger.error(f"❌ Error getting balances from {broker_type.value}: {e}")
            return []
    
    async def get_open_orders(self, broker_type: BrokerType, symbol: Optional[str] = None) -> List[RealOrder]:
        """Get open orders from specific broker"""
        try:
            if broker_type not in self.brokers:
                self.logger.error(f"❌ Broker {broker_type.value} not initialized")
                return []
            
            broker = self.brokers[broker_type]
            return await broker.get_open_orders(symbol)
            
        except Exception as e:
            self.logger.error(f"❌ Error getting open orders from {broker_type.value}: {e}")
            return []
    
    async def close_all_brokers(self):
        """Close all broker connections"""
        for broker_type, broker in self.brokers.items():
            try:
                await broker.close()
                self.logger.info(f"🔌 Closed {broker_type.value} broker")
            except Exception as e:
                self.logger.error(f"❌ Error closing {broker_type.value} broker: {e}")


# Global instance
real_broker_manager = RealBrokerManager()
