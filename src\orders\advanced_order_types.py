"""
Advanced Order Types for AI Trading System
Production-ready implementations of sophisticated order types
"""

import asyncio
import logging
import time
import random
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass, asdict
from enum import Enum
import uuid

logger = logging.getLogger(__name__)


class AdvancedOrderType(Enum):
    """Advanced order types"""
    ICEBERG = "ICEBERG"
    HIDDEN = "HIDDEN"
    TIME_WEIGHTED = "TIME_WEIGHTED"
    RESERVE = "RESERVE"
    BLOCK_FINDER = "BLOCK_FINDER"
    SNIPER = "SNIPER"
    GUERRILLA = "GUERRILLA"


class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "PENDING"
    ACTIVE = "ACTIVE"
    PARTIALLY_FILLED = "PARTIALLY_FILLED"
    FILLED = "FILLED"
    CANCELLED = "CANCELLED"
    REJECTED = "REJECTED"
    EXPIRED = "EXPIRED"


class TriggerCondition(Enum):
    """Trigger conditions for advanced orders"""
    PRICE_ABOVE = "PRICE_ABOVE"
    PRICE_BELOW = "PRICE_BELOW"
    VOLUME_ABOVE = "VOLUME_ABOVE"
    VOLATILITY_ABOVE = "VOLATILITY_ABOVE"
    TIME_BASED = "TIME_BASED"
    LIQUIDITY_BASED = "LIQUIDITY_BASED"


@dataclass
class AdvancedOrderParameters:
    """Parameters for advanced orders"""
    order_type: AdvancedOrderType
    symbol: str
    side: str  # BUY or SELL
    total_quantity: float
    price: Optional[float] = None
    
    # Iceberg parameters
    display_quantity: Optional[float] = None
    refresh_threshold: float = 0.1  # Refresh when 10% remaining
    
    # Time-weighted parameters
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    time_intervals: int = 10
    
    # Hidden order parameters
    hidden_percentage: float = 1.0  # 100% hidden by default
    reveal_threshold: Optional[float] = None
    
    # Reserve order parameters
    reserve_quantity: Optional[float] = None
    
    # Trigger conditions
    trigger_conditions: List[Dict[str, Any]] = None
    
    # Advanced features
    randomization_factor: float = 0.05  # 5% randomization
    adaptive_sizing: bool = True
    market_impact_limit: float = 0.001  # 0.1% max impact
    
    def __post_init__(self):
        if self.trigger_conditions is None:
            self.trigger_conditions = []
        if self.display_quantity is None and self.order_type == AdvancedOrderType.ICEBERG:
            self.display_quantity = min(self.total_quantity * 0.1, 1000)  # 10% or 1000 units


@dataclass
class OrderExecution:
    """Order execution record"""
    execution_id: str
    order_id: str
    quantity: float
    price: float
    timestamp: datetime
    execution_type: str
    market_data: Dict[str, Any]


class IcebergOrder:
    """Iceberg order implementation"""
    
    def __init__(self, order_id: str, parameters: AdvancedOrderParameters):
        self.order_id = order_id
        self.parameters = parameters
        self.status = OrderStatus.PENDING
        self.executed_quantity = 0.0
        self.current_display_quantity = parameters.display_quantity
        self.executions = []
        self.creation_time = datetime.now()
        self.logger = logging.getLogger(f"{__name__}.IcebergOrder")
        
    def calculate_next_display_size(self, market_data: Dict[str, Any]) -> float:
        """Calculate next display size based on market conditions"""
        try:
            remaining_quantity = self.parameters.total_quantity - self.executed_quantity
            
            if remaining_quantity <= 0:
                return 0.0
            
            # Base display size
            base_size = self.parameters.display_quantity
            
            # Adaptive sizing based on market conditions
            if self.parameters.adaptive_sizing:
                volatility = market_data.get('volatility', 0.02)
                volume = market_data.get('volume', 1000000)
                liquidity_score = market_data.get('liquidity_score', 0.8)
                
                # Adjust for volatility (smaller sizes in high volatility)
                volatility_factor = max(0.5, 1.0 - volatility * 10)
                
                # Adjust for liquidity (larger sizes in high liquidity)
                liquidity_factor = min(1.5, liquidity_score * 1.2)
                
                # Adjust for volume (larger sizes in high volume)
                volume_factor = min(1.3, volume / 1000000)
                
                adjusted_size = base_size * volatility_factor * liquidity_factor * volume_factor
            else:
                adjusted_size = base_size
            
            # Add randomization to avoid detection
            randomization = 1.0 + (random.random() - 0.5) * 2 * self.parameters.randomization_factor
            final_size = adjusted_size * randomization
            
            # Ensure we don't exceed remaining quantity
            final_size = min(final_size, remaining_quantity)
            
            # Minimum size check
            min_size = min(remaining_quantity, base_size * 0.1)
            final_size = max(final_size, min_size)
            
            return final_size
            
        except Exception as e:
            self.logger.error(f"Error calculating display size: {e}")
            return min(self.parameters.display_quantity, remaining_quantity)
    
    def should_refresh(self) -> bool:
        """Check if order should be refreshed"""
        try:
            if self.current_display_quantity <= 0:
                return True
            
            # Refresh when below threshold
            threshold_quantity = self.parameters.display_quantity * self.parameters.refresh_threshold
            return self.current_display_quantity <= threshold_quantity
            
        except Exception as e:
            self.logger.error(f"Error checking refresh condition: {e}")
            return True
    
    def execute_partial_fill(self, quantity: float, price: float, market_data: Dict[str, Any]) -> OrderExecution:
        """Execute partial fill"""
        try:
            execution = OrderExecution(
                execution_id=str(uuid.uuid4()),
                order_id=self.order_id,
                quantity=quantity,
                price=price,
                timestamp=datetime.now(),
                execution_type="ICEBERG_FILL",
                market_data=market_data.copy()
            )
            
            self.executions.append(execution)
            self.executed_quantity += quantity
            self.current_display_quantity -= quantity
            
            # Update status
            if self.executed_quantity >= self.parameters.total_quantity:
                self.status = OrderStatus.FILLED
            else:
                self.status = OrderStatus.PARTIALLY_FILLED
            
            self.logger.info(f"Iceberg order {self.order_id} executed {quantity} @ {price}")
            return execution
            
        except Exception as e:
            self.logger.error(f"Error executing partial fill: {e}")
            return None
    
    def refresh_display(self, market_data: Dict[str, Any]) -> float:
        """Refresh display quantity"""
        try:
            if not self.should_refresh():
                return self.current_display_quantity
            
            new_display_size = self.calculate_next_display_size(market_data)
            self.current_display_quantity = new_display_size
            
            self.logger.info(f"Iceberg order {self.order_id} refreshed display to {new_display_size}")
            return new_display_size
            
        except Exception as e:
            self.logger.error(f"Error refreshing display: {e}")
            return self.current_display_quantity


class TimeWeightedOrder:
    """Time-weighted order implementation"""
    
    def __init__(self, order_id: str, parameters: AdvancedOrderParameters):
        self.order_id = order_id
        self.parameters = parameters
        self.status = OrderStatus.PENDING
        self.executed_quantity = 0.0
        self.executions = []
        self.creation_time = datetime.now()
        self.time_slices = []
        self.current_slice_index = 0
        self.logger = logging.getLogger(f"{__name__}.TimeWeightedOrder")
        
        self._generate_time_slices()
    
    def _generate_time_slices(self):
        """Generate time-based execution slices"""
        try:
            start_time = self.parameters.start_time or datetime.now()
            end_time = self.parameters.end_time or (start_time + timedelta(hours=1))
            
            total_duration = (end_time - start_time).total_seconds()
            slice_duration = total_duration / self.parameters.time_intervals
            
            quantity_per_slice = self.parameters.total_quantity / self.parameters.time_intervals
            
            for i in range(self.parameters.time_intervals):
                slice_start = start_time + timedelta(seconds=i * slice_duration)
                slice_end = start_time + timedelta(seconds=(i + 1) * slice_duration)
                
                # Add randomization to slice sizes
                randomization = 1.0 + (random.random() - 0.5) * 2 * self.parameters.randomization_factor
                slice_quantity = quantity_per_slice * randomization
                
                time_slice = {
                    "slice_id": i,
                    "start_time": slice_start,
                    "end_time": slice_end,
                    "target_quantity": slice_quantity,
                    "executed_quantity": 0.0,
                    "status": "PENDING"
                }
                
                self.time_slices.append(time_slice)
            
            self.logger.info(f"Generated {len(self.time_slices)} time slices for order {self.order_id}")
            
        except Exception as e:
            self.logger.error(f"Error generating time slices: {e}")
    
    def get_current_slice(self) -> Optional[Dict[str, Any]]:
        """Get current active time slice"""
        try:
            current_time = datetime.now()
            
            for i, slice_info in enumerate(self.time_slices):
                if (slice_info["start_time"] <= current_time <= slice_info["end_time"] and 
                    slice_info["status"] != "COMPLETED"):
                    self.current_slice_index = i
                    return slice_info
            
            return None
            
        except Exception as e:
            self.logger.error(f"Error getting current slice: {e}")
            return None
    
    def calculate_slice_execution_rate(self, market_data: Dict[str, Any]) -> float:
        """Calculate execution rate for current slice"""
        try:
            current_slice = self.get_current_slice()
            if not current_slice:
                return 0.0
            
            remaining_quantity = current_slice["target_quantity"] - current_slice["executed_quantity"]
            current_time = datetime.now()
            time_remaining = (current_slice["end_time"] - current_time).total_seconds()
            
            if time_remaining <= 0:
                return remaining_quantity  # Execute all remaining
            
            # Base execution rate
            base_rate = remaining_quantity / max(time_remaining / 60, 1)  # per minute
            
            # Adjust for market conditions
            volatility = market_data.get('volatility', 0.02)
            liquidity_score = market_data.get('liquidity_score', 0.8)
            
            # Higher volatility = slower execution
            volatility_factor = max(0.5, 1.0 - volatility * 5)
            
            # Higher liquidity = faster execution
            liquidity_factor = min(1.5, liquidity_score * 1.3)
            
            adjusted_rate = base_rate * volatility_factor * liquidity_factor
            
            return max(adjusted_rate, remaining_quantity * 0.01)  # Minimum 1% per update
            
        except Exception as e:
            self.logger.error(f"Error calculating execution rate: {e}")
            return 0.0
    
    def execute_slice_quantity(self, quantity: float, price: float, market_data: Dict[str, Any]) -> OrderExecution:
        """Execute quantity for current slice"""
        try:
            current_slice = self.get_current_slice()
            if not current_slice:
                return None
            
            execution = OrderExecution(
                execution_id=str(uuid.uuid4()),
                order_id=self.order_id,
                quantity=quantity,
                price=price,
                timestamp=datetime.now(),
                execution_type="TIME_WEIGHTED_FILL",
                market_data=market_data.copy()
            )
            
            self.executions.append(execution)
            self.executed_quantity += quantity
            current_slice["executed_quantity"] += quantity
            
            # Check if slice is completed
            if current_slice["executed_quantity"] >= current_slice["target_quantity"]:
                current_slice["status"] = "COMPLETED"
            
            # Update order status
            if self.executed_quantity >= self.parameters.total_quantity:
                self.status = OrderStatus.FILLED
            else:
                self.status = OrderStatus.PARTIALLY_FILLED
            
            self.logger.info(f"Time-weighted order {self.order_id} executed {quantity} @ {price} in slice {current_slice['slice_id']}")
            return execution
            
        except Exception as e:
            self.logger.error(f"Error executing slice quantity: {e}")
            return None


class HiddenOrder:
    """Hidden order implementation"""
    
    def __init__(self, order_id: str, parameters: AdvancedOrderParameters):
        self.order_id = order_id
        self.parameters = parameters
        self.status = OrderStatus.PENDING
        self.executed_quantity = 0.0
        self.executions = []
        self.creation_time = datetime.now()
        self.revealed_quantity = 0.0
        self.logger = logging.getLogger(f"{__name__}.HiddenOrder")
    
    def calculate_reveal_quantity(self, market_data: Dict[str, Any]) -> float:
        """Calculate how much quantity to reveal"""
        try:
            remaining_quantity = self.parameters.total_quantity - self.executed_quantity
            
            if remaining_quantity <= 0:
                return 0.0
            
            # Base reveal percentage
            reveal_percentage = 1.0 - self.parameters.hidden_percentage
            
            # Adaptive revealing based on market conditions
            if self.parameters.adaptive_sizing:
                liquidity_score = market_data.get('liquidity_score', 0.8)
                volatility = market_data.get('volatility', 0.02)
                
                # Reveal more in high liquidity
                liquidity_factor = liquidity_score * 1.2
                
                # Reveal less in high volatility
                volatility_factor = max(0.3, 1.0 - volatility * 10)
                
                adjusted_percentage = reveal_percentage * liquidity_factor * volatility_factor
            else:
                adjusted_percentage = reveal_percentage
            
            # Apply reveal threshold if set
            if self.parameters.reveal_threshold:
                current_price = market_data.get('price', 100.0)
                if self.parameters.side == "BUY" and current_price <= self.parameters.reveal_threshold:
                    adjusted_percentage = min(1.0, adjusted_percentage * 2)  # Reveal more
                elif self.parameters.side == "SELL" and current_price >= self.parameters.reveal_threshold:
                    adjusted_percentage = min(1.0, adjusted_percentage * 2)  # Reveal more
            
            reveal_quantity = remaining_quantity * adjusted_percentage
            
            # Add randomization
            randomization = 1.0 + (random.random() - 0.5) * 2 * self.parameters.randomization_factor
            final_reveal = reveal_quantity * randomization
            
            return min(final_reveal, remaining_quantity)
            
        except Exception as e:
            self.logger.error(f"Error calculating reveal quantity: {e}")
            return remaining_quantity * 0.1  # Default 10% reveal
    
    def update_revealed_quantity(self, market_data: Dict[str, Any]):
        """Update revealed quantity based on market conditions"""
        try:
            new_reveal = self.calculate_reveal_quantity(market_data)
            self.revealed_quantity = new_reveal
            
            self.logger.debug(f"Hidden order {self.order_id} revealed quantity updated to {new_reveal}")
            
        except Exception as e:
            self.logger.error(f"Error updating revealed quantity: {e}")
    
    def execute_hidden_fill(self, quantity: float, price: float, market_data: Dict[str, Any]) -> OrderExecution:
        """Execute fill for hidden order"""
        try:
            execution = OrderExecution(
                execution_id=str(uuid.uuid4()),
                order_id=self.order_id,
                quantity=quantity,
                price=price,
                timestamp=datetime.now(),
                execution_type="HIDDEN_FILL",
                market_data=market_data.copy()
            )
            
            self.executions.append(execution)
            self.executed_quantity += quantity
            
            # Update status
            if self.executed_quantity >= self.parameters.total_quantity:
                self.status = OrderStatus.FILLED
            else:
                self.status = OrderStatus.PARTIALLY_FILLED
            
            # Update revealed quantity after execution
            self.update_revealed_quantity(market_data)
            
            self.logger.info(f"Hidden order {self.order_id} executed {quantity} @ {price}")
            return execution
            
        except Exception as e:
            self.logger.error(f"Error executing hidden fill: {e}")
            return None


class AdvancedOrderManager:
    """Manager for advanced order types"""
    
    def __init__(self):
        self.active_orders = {}
        self.order_history = []
        self.logger = logging.getLogger(f"{__name__}.AdvancedOrderManager")
    
    def create_order(self, parameters: AdvancedOrderParameters) -> str:
        """Create advanced order"""
        try:
            order_id = f"{parameters.order_type.value}_{parameters.symbol}_{int(time.time())}_{random.randint(1000, 9999)}"
            
            if parameters.order_type == AdvancedOrderType.ICEBERG:
                order = IcebergOrder(order_id, parameters)
            elif parameters.order_type == AdvancedOrderType.TIME_WEIGHTED:
                order = TimeWeightedOrder(order_id, parameters)
            elif parameters.order_type == AdvancedOrderType.HIDDEN:
                order = HiddenOrder(order_id, parameters)
            else:
                self.logger.warning(f"Order type {parameters.order_type} not implemented yet")
                return None
            
            self.active_orders[order_id] = order
            self.logger.info(f"Created {parameters.order_type.value} order: {order_id}")
            
            return order_id
            
        except Exception as e:
            self.logger.error(f"Error creating order: {e}")
            return None
    
    def process_order_updates(self, market_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Process updates for all active orders"""
        try:
            order_updates = []
            
            for order_id, order in self.active_orders.items():
                if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.EXPIRED]:
                    continue
                
                update = {
                    "order_id": order_id,
                    "order_type": order.parameters.order_type.value,
                    "symbol": order.parameters.symbol,
                    "status": order.status.value,
                    "executed_quantity": order.executed_quantity,
                    "remaining_quantity": order.parameters.total_quantity - order.executed_quantity
                }
                
                # Type-specific updates
                if isinstance(order, IcebergOrder):
                    order.refresh_display(market_data)
                    update["display_quantity"] = order.current_display_quantity
                    update["should_refresh"] = order.should_refresh()
                
                elif isinstance(order, TimeWeightedOrder):
                    current_slice = order.get_current_slice()
                    if current_slice:
                        update["current_slice"] = current_slice["slice_id"]
                        update["execution_rate"] = order.calculate_slice_execution_rate(market_data)
                
                elif isinstance(order, HiddenOrder):
                    order.update_revealed_quantity(market_data)
                    update["revealed_quantity"] = order.revealed_quantity
                    update["hidden_percentage"] = order.parameters.hidden_percentage
                
                order_updates.append(update)
            
            return order_updates
            
        except Exception as e:
            self.logger.error(f"Error processing order updates: {e}")
            return []
    
    def get_order_status(self, order_id: str) -> Dict[str, Any]:
        """Get detailed order status"""
        try:
            if order_id not in self.active_orders:
                return {"status": "NOT_FOUND"}
            
            order = self.active_orders[order_id]
            
            status = {
                "order_id": order_id,
                "order_type": order.parameters.order_type.value,
                "symbol": order.parameters.symbol,
                "side": order.parameters.side,
                "total_quantity": order.parameters.total_quantity,
                "executed_quantity": order.executed_quantity,
                "remaining_quantity": order.parameters.total_quantity - order.executed_quantity,
                "status": order.status.value,
                "creation_time": order.creation_time,
                "executions": len(order.executions)
            }
            
            return status
            
        except Exception as e:
            self.logger.error(f"Error getting order status: {e}")
            return {"status": "ERROR", "error": str(e)}


# Global instance
advanced_order_manager = AdvancedOrderManager()
