"""
Advanced Portfolio Optimization for AI Trading System
Production-ready portfolio optimization algorithms and risk management
"""

import asyncio
import logging
import time
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from enum import Enum
import json

logger = logging.getLogger(__name__)


class OptimizationMethod(Enum):
    """Portfolio optimization methods"""
    MEAN_VARIANCE = "MEAN_VARIANCE"
    BLACK_LITTERMAN = "BLACK_LITTERMAN"
    RISK_PARITY = "RISK_PARITY"
    MINIMUM_VARIANCE = "MINIMUM_VARIANCE"
    MAXIMUM_SHARPE = "MAXIMUM_SHARPE"
    HIERARCHICAL_RISK_PARITY = "HRP"
    KELLY_CRITERION = "KELLY_CRITERION"


class RiskModel(Enum):
    """Risk model types"""
    HISTORICAL_COVARIANCE = "HISTORICAL_COV"
    EXPONENTIAL_WEIGHTED = "EXP_WEIGHTED"
    FACTOR_MODEL = "FACTOR_MODEL"
    SHRINKAGE_ESTIMATOR = "SHRINKAGE"


@dataclass
class OptimizationConstraints:
    """Portfolio optimization constraints"""
    max_weight: float = 0.3  # Maximum weight per asset
    min_weight: float = 0.0  # Minimum weight per asset
    max_turnover: float = 0.5  # Maximum portfolio turnover
    target_volatility: Optional[float] = None  # Target portfolio volatility
    max_leverage: float = 1.0  # Maximum leverage
    sector_limits: Dict[str, float] = None  # Sector exposure limits
    
    def __post_init__(self):
        if self.sector_limits is None:
            self.sector_limits = {}


@dataclass
class PortfolioMetrics:
    """Portfolio performance metrics"""
    portfolio_id: str
    timestamp: datetime
    total_value: float
    weights: Dict[str, float]
    expected_return: float
    volatility: float
    sharpe_ratio: float
    max_drawdown: float
    var_95: float  # Value at Risk 95%
    cvar_95: float  # Conditional Value at Risk 95%
    beta: float
    alpha: float
    tracking_error: float
    information_ratio: float


@dataclass
class RiskDecomposition:
    """Risk decomposition analysis"""
    total_risk: float
    systematic_risk: float
    idiosyncratic_risk: float
    factor_exposures: Dict[str, float]
    marginal_risk_contributions: Dict[str, float]
    component_var: Dict[str, float]


class MeanVarianceOptimizer:
    """Mean-Variance optimization implementation"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.MeanVarianceOptimizer")
    
    def optimize(self, expected_returns: np.ndarray, covariance_matrix: np.ndarray, 
                constraints: OptimizationConstraints, risk_aversion: float = 1.0) -> np.ndarray:
        """Perform mean-variance optimization"""
        try:
            n_assets = len(expected_returns)
            
            # Objective function: maximize utility = expected_return - 0.5 * risk_aversion * variance
            # This is equivalent to: minimize -expected_return + 0.5 * risk_aversion * variance
            
            # Simplified optimization using analytical solution for unconstrained case
            # In practice, would use scipy.optimize or cvxpy for constrained optimization
            
            # Risk-adjusted expected returns
            inv_cov = np.linalg.pinv(covariance_matrix)
            
            # Calculate optimal weights (unconstrained)
            ones = np.ones((n_assets, 1))
            
            # Analytical solution for mean-variance optimization
            A = ones.T @ inv_cov @ ones
            B = ones.T @ inv_cov @ expected_returns.reshape(-1, 1)
            C = expected_returns.T @ inv_cov @ expected_returns
            
            # Optimal weights
            lambda_val = (risk_aversion * A - B) / (A * C - B**2)
            gamma_val = (C - risk_aversion * B) / (A * C - B**2)
            
            weights = (lambda_val * inv_cov @ ones + gamma_val * inv_cov @ expected_returns.reshape(-1, 1)).flatten()
            
            # Apply constraints
            weights = self._apply_constraints(weights, constraints)
            
            self.logger.info(f"Mean-variance optimization completed: {len(weights)} assets")
            return weights
            
        except Exception as e:
            self.logger.error(f"Error in mean-variance optimization: {e}")
            # Return equal weights as fallback
            return np.ones(len(expected_returns)) / len(expected_returns)
    
    def _apply_constraints(self, weights: np.ndarray, constraints: OptimizationConstraints) -> np.ndarray:
        """Apply portfolio constraints"""
        try:
            # Clip weights to min/max bounds
            weights = np.clip(weights, constraints.min_weight, constraints.max_weight)
            
            # Normalize to sum to 1 (or max_leverage)
            weight_sum = np.sum(weights)
            if weight_sum > 0:
                weights = weights * constraints.max_leverage / weight_sum
            
            # Ensure non-negative weights if min_weight >= 0
            if constraints.min_weight >= 0:
                weights = np.maximum(weights, 0)
                # Renormalize
                weight_sum = np.sum(weights)
                if weight_sum > 0:
                    weights = weights / weight_sum * constraints.max_leverage
            
            return weights
            
        except Exception as e:
            self.logger.error(f"Error applying constraints: {e}")
            return weights


class RiskParityOptimizer:
    """Risk Parity optimization implementation"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.RiskParityOptimizer")
    
    def optimize(self, covariance_matrix: np.ndarray, constraints: OptimizationConstraints,
                target_risk_contributions: Optional[np.ndarray] = None) -> np.ndarray:
        """Perform risk parity optimization"""
        try:
            n_assets = covariance_matrix.shape[0]
            
            if target_risk_contributions is None:
                target_risk_contributions = np.ones(n_assets) / n_assets
            
            # Initialize with equal weights
            weights = np.ones(n_assets) / n_assets
            
            # Iterative optimization using gradient descent
            learning_rate = 0.01
            max_iterations = 1000
            tolerance = 1e-6
            
            for iteration in range(max_iterations):
                # Calculate risk contributions
                portfolio_vol = np.sqrt(weights.T @ covariance_matrix @ weights)
                marginal_risk = (covariance_matrix @ weights) / portfolio_vol
                risk_contributions = weights * marginal_risk / portfolio_vol
                
                # Calculate gradient of objective function
                # Objective: minimize sum of squared deviations from target risk contributions
                gradient = 2 * (risk_contributions - target_risk_contributions)
                
                # Update weights
                weights_new = weights - learning_rate * gradient
                
                # Apply constraints
                weights_new = self._apply_constraints(weights_new, constraints)
                
                # Check convergence
                if np.linalg.norm(weights_new - weights) < tolerance:
                    break
                
                weights = weights_new
            
            self.logger.info(f"Risk parity optimization completed in {iteration+1} iterations")
            return weights
            
        except Exception as e:
            self.logger.error(f"Error in risk parity optimization: {e}")
            # Return equal weights as fallback
            return np.ones(covariance_matrix.shape[0]) / covariance_matrix.shape[0]
    
    def _apply_constraints(self, weights: np.ndarray, constraints: OptimizationConstraints) -> np.ndarray:
        """Apply portfolio constraints for risk parity"""
        try:
            # Clip weights to bounds
            weights = np.clip(weights, constraints.min_weight, constraints.max_weight)
            
            # Normalize to sum to max_leverage
            weight_sum = np.sum(weights)
            if weight_sum > 0:
                weights = weights * constraints.max_leverage / weight_sum
            
            return weights
            
        except Exception as e:
            self.logger.error(f"Error applying constraints: {e}")
            return weights


class BlackLittermanOptimizer:
    """Black-Litterman optimization implementation"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.BlackLittermanOptimizer")
    
    def optimize(self, market_caps: np.ndarray, covariance_matrix: np.ndarray,
                views: Dict[str, Tuple[float, float]], constraints: OptimizationConstraints,
                risk_aversion: float = 3.0, tau: float = 0.025) -> np.ndarray:
        """Perform Black-Litterman optimization"""
        try:
            n_assets = len(market_caps)
            
            # Calculate implied equilibrium returns
            market_weights = market_caps / np.sum(market_caps)
            implied_returns = risk_aversion * covariance_matrix @ market_weights
            
            # Process views
            if views:
                # Create picking matrix P and view vector Q
                view_assets = list(views.keys())
                P = np.zeros((len(view_assets), n_assets))
                Q = np.zeros(len(view_assets))
                omega_diag = np.zeros(len(view_assets))
                
                for i, (asset_idx, (expected_return, confidence)) in enumerate(views.items()):
                    if isinstance(asset_idx, int) and 0 <= asset_idx < n_assets:
                        P[i, asset_idx] = 1.0
                        Q[i] = expected_return
                        omega_diag[i] = 1.0 / confidence  # Higher confidence = lower uncertainty
                
                Omega = np.diag(omega_diag)
                
                # Black-Litterman formula
                tau_cov = tau * covariance_matrix
                M1 = np.linalg.inv(tau_cov)
                M2 = P.T @ np.linalg.inv(Omega) @ P
                M3 = np.linalg.inv(tau_cov) @ implied_returns + P.T @ np.linalg.inv(Omega) @ Q
                
                # New expected returns
                new_expected_returns = np.linalg.inv(M1 + M2) @ M3
                
                # New covariance matrix
                new_covariance = np.linalg.inv(M1 + M2)
            else:
                # No views, use implied returns
                new_expected_returns = implied_returns
                new_covariance = covariance_matrix
            
            # Optimize using mean-variance with new parameters
            mv_optimizer = MeanVarianceOptimizer()
            weights = mv_optimizer.optimize(new_expected_returns, new_covariance, constraints, risk_aversion)
            
            self.logger.info(f"Black-Litterman optimization completed with {len(views)} views")
            return weights
            
        except Exception as e:
            self.logger.error(f"Error in Black-Litterman optimization: {e}")
            # Return market cap weights as fallback
            return market_caps / np.sum(market_caps)


class PortfolioRiskManager:
    """Advanced portfolio risk management"""
    
    def __init__(self):
        self.logger = logging.getLogger(f"{__name__}.PortfolioRiskManager")
    
    def calculate_portfolio_metrics(self, weights: np.ndarray, expected_returns: np.ndarray,
                                  covariance_matrix: np.ndarray, returns_history: np.ndarray,
                                  benchmark_returns: Optional[np.ndarray] = None) -> PortfolioMetrics:
        """Calculate comprehensive portfolio metrics"""
        try:
            # Basic portfolio statistics
            portfolio_return = np.sum(weights * expected_returns)
            portfolio_variance = weights.T @ covariance_matrix @ weights
            portfolio_volatility = np.sqrt(portfolio_variance)
            
            # Sharpe ratio (assuming risk-free rate = 0)
            sharpe_ratio = portfolio_return / portfolio_volatility if portfolio_volatility > 0 else 0
            
            # Calculate historical portfolio returns
            if returns_history.shape[1] == len(weights):
                portfolio_returns = returns_history @ weights
                
                # Maximum drawdown
                cumulative_returns = np.cumprod(1 + portfolio_returns)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = (cumulative_returns - running_max) / running_max
                max_drawdown = np.min(drawdowns)
                
                # Value at Risk (95%)
                var_95 = np.percentile(portfolio_returns, 5)
                
                # Conditional Value at Risk (95%)
                cvar_95 = np.mean(portfolio_returns[portfolio_returns <= var_95])
                
                # Beta and Alpha (if benchmark provided)
                if benchmark_returns is not None and len(benchmark_returns) == len(portfolio_returns):
                    covariance_with_benchmark = np.cov(portfolio_returns, benchmark_returns)[0, 1]
                    benchmark_variance = np.var(benchmark_returns)
                    beta = covariance_with_benchmark / benchmark_variance if benchmark_variance > 0 else 0
                    
                    alpha = np.mean(portfolio_returns) - beta * np.mean(benchmark_returns)
                    
                    # Tracking error and Information ratio
                    active_returns = portfolio_returns - benchmark_returns
                    tracking_error = np.std(active_returns)
                    information_ratio = np.mean(active_returns) / tracking_error if tracking_error > 0 else 0
                else:
                    beta = alpha = tracking_error = information_ratio = 0.0
            else:
                max_drawdown = var_95 = cvar_95 = beta = alpha = tracking_error = information_ratio = 0.0
            
            metrics = PortfolioMetrics(
                portfolio_id=f"portfolio_{int(time.time())}",
                timestamp=datetime.now(),
                total_value=100000.0,  # Placeholder
                weights={f"asset_{i}": w for i, w in enumerate(weights)},
                expected_return=portfolio_return,
                volatility=portfolio_volatility,
                sharpe_ratio=sharpe_ratio,
                max_drawdown=max_drawdown,
                var_95=var_95,
                cvar_95=cvar_95,
                beta=beta,
                alpha=alpha,
                tracking_error=tracking_error,
                information_ratio=information_ratio
            )
            
            self.logger.info(f"Portfolio metrics calculated: Sharpe {sharpe_ratio:.3f}, Vol {portfolio_volatility:.3f}")
            return metrics
            
        except Exception as e:
            self.logger.error(f"Error calculating portfolio metrics: {e}")
            return None
    
    def calculate_risk_decomposition(self, weights: np.ndarray, covariance_matrix: np.ndarray,
                                   factor_exposures: Optional[np.ndarray] = None) -> RiskDecomposition:
        """Calculate portfolio risk decomposition"""
        try:
            # Total portfolio risk
            portfolio_variance = weights.T @ covariance_matrix @ weights
            total_risk = np.sqrt(portfolio_variance)
            
            # Marginal risk contributions
            marginal_risk = (covariance_matrix @ weights) / total_risk
            risk_contributions = weights * marginal_risk
            
            # Component VaR (simplified)
            component_var = {}
            for i, weight in enumerate(weights):
                component_var[f"asset_{i}"] = weight * marginal_risk[i]
            
            # Factor decomposition (if factor exposures provided)
            if factor_exposures is not None:
                # Simplified factor risk decomposition
                factor_risk = np.sum(factor_exposures**2 * np.diag(covariance_matrix)[:len(factor_exposures)])
                systematic_risk = np.sqrt(factor_risk)
                idiosyncratic_risk = np.sqrt(max(0, portfolio_variance - factor_risk))
                
                factor_exp_dict = {f"factor_{i}": exp for i, exp in enumerate(factor_exposures)}
            else:
                systematic_risk = total_risk * 0.7  # Assume 70% systematic
                idiosyncratic_risk = total_risk * 0.3  # Assume 30% idiosyncratic
                factor_exp_dict = {}
            
            decomposition = RiskDecomposition(
                total_risk=total_risk,
                systematic_risk=systematic_risk,
                idiosyncratic_risk=idiosyncratic_risk,
                factor_exposures=factor_exp_dict,
                marginal_risk_contributions={f"asset_{i}": contrib for i, contrib in enumerate(risk_contributions)},
                component_var=component_var
            )
            
            self.logger.info(f"Risk decomposition: Total {total_risk:.4f}, Systematic {systematic_risk:.4f}")
            return decomposition
            
        except Exception as e:
            self.logger.error(f"Error calculating risk decomposition: {e}")
            return None
    
    def stress_test_portfolio(self, weights: np.ndarray, covariance_matrix: np.ndarray,
                            stress_scenarios: List[Dict[str, float]]) -> Dict[str, float]:
        """Perform portfolio stress testing"""
        try:
            stress_results = {}
            
            for i, scenario in enumerate(stress_scenarios):
                scenario_name = scenario.get('name', f'scenario_{i+1}')
                
                # Apply stress to returns
                stressed_returns = np.array([scenario.get(f'asset_{j}', 0.0) for j in range(len(weights))])
                
                # Calculate portfolio return under stress
                portfolio_stress_return = np.sum(weights * stressed_returns)
                stress_results[scenario_name] = portfolio_stress_return
            
            # Add standard stress scenarios
            # Market crash scenario (-20% all assets)
            crash_returns = np.full(len(weights), -0.20)
            stress_results['market_crash'] = np.sum(weights * crash_returns)
            
            # Volatility spike scenario (double volatility)
            vol_spike_returns = np.random.multivariate_normal(
                np.zeros(len(weights)), 
                2 * covariance_matrix, 
                size=1
            )[0]
            stress_results['volatility_spike'] = np.sum(weights * vol_spike_returns)
            
            self.logger.info(f"Stress testing completed: {len(stress_results)} scenarios")
            return stress_results
            
        except Exception as e:
            self.logger.error(f"Error in stress testing: {e}")
            return {}


class AdvancedPortfolioOptimizer:
    """Main portfolio optimization engine"""
    
    def __init__(self):
        self.mv_optimizer = MeanVarianceOptimizer()
        self.rp_optimizer = RiskParityOptimizer()
        self.bl_optimizer = BlackLittermanOptimizer()
        self.risk_manager = PortfolioRiskManager()
        self.optimization_history = []
        self.logger = logging.getLogger(f"{__name__}.AdvancedPortfolioOptimizer")
    
    def optimize_portfolio(self, method: OptimizationMethod, assets_data: Dict[str, Any],
                          constraints: OptimizationConstraints, **kwargs) -> Dict[str, Any]:
        """Optimize portfolio using specified method"""
        try:
            self.logger.info(f"Starting portfolio optimization using {method.value}")
            
            # Extract data
            expected_returns = np.array(assets_data['expected_returns'])
            covariance_matrix = np.array(assets_data['covariance_matrix'])
            
            # Perform optimization based on method
            if method == OptimizationMethod.MEAN_VARIANCE:
                risk_aversion = kwargs.get('risk_aversion', 1.0)
                weights = self.mv_optimizer.optimize(expected_returns, covariance_matrix, constraints, risk_aversion)
                
            elif method == OptimizationMethod.RISK_PARITY:
                target_risk = kwargs.get('target_risk_contributions')
                weights = self.rp_optimizer.optimize(covariance_matrix, constraints, target_risk)
                
            elif method == OptimizationMethod.BLACK_LITTERMAN:
                market_caps = np.array(assets_data.get('market_caps', np.ones(len(expected_returns))))
                views = kwargs.get('views', {})
                risk_aversion = kwargs.get('risk_aversion', 3.0)
                weights = self.bl_optimizer.optimize(market_caps, covariance_matrix, views, constraints, risk_aversion)
                
            elif method == OptimizationMethod.MINIMUM_VARIANCE:
                # Minimum variance is mean-variance with zero expected returns
                zero_returns = np.zeros_like(expected_returns)
                weights = self.mv_optimizer.optimize(zero_returns, covariance_matrix, constraints, 1.0)
                
            elif method == OptimizationMethod.MAXIMUM_SHARPE:
                # Maximum Sharpe is mean-variance with high risk aversion
                weights = self.mv_optimizer.optimize(expected_returns, covariance_matrix, constraints, 0.1)
                
            else:
                self.logger.warning(f"Optimization method {method} not implemented, using equal weights")
                weights = np.ones(len(expected_returns)) / len(expected_returns)
            
            # Calculate portfolio metrics
            returns_history = assets_data.get('returns_history', np.random.randn(252, len(expected_returns)) * 0.02)
            benchmark_returns = assets_data.get('benchmark_returns')
            
            metrics = self.risk_manager.calculate_portfolio_metrics(
                weights, expected_returns, covariance_matrix, returns_history, benchmark_returns
            )
            
            # Calculate risk decomposition
            factor_exposures = assets_data.get('factor_exposures')
            risk_decomp = self.risk_manager.calculate_risk_decomposition(
                weights, covariance_matrix, factor_exposures
            )
            
            # Perform stress testing
            stress_scenarios = kwargs.get('stress_scenarios', [])
            stress_results = self.risk_manager.stress_test_portfolio(
                weights, covariance_matrix, stress_scenarios
            )
            
            # Compile results
            optimization_result = {
                "optimization_id": f"opt_{int(time.time())}",
                "method": method.value,
                "timestamp": datetime.now(),
                "weights": {f"asset_{i}": w for i, w in enumerate(weights)},
                "metrics": asdict(metrics) if metrics else {},
                "risk_decomposition": asdict(risk_decomp) if risk_decomp else {},
                "stress_test_results": stress_results,
                "constraints": asdict(constraints),
                "optimization_parameters": kwargs
            }
            
            # Store in history
            self.optimization_history.append(optimization_result)
            
            # Keep only recent history
            if len(self.optimization_history) > 100:
                self.optimization_history = self.optimization_history[-100:]
            
            self.logger.info(f"Portfolio optimization completed: {method.value}")
            self.logger.info(f"Expected return: {metrics.expected_return:.4f}, Volatility: {metrics.volatility:.4f}")
            self.logger.info(f"Sharpe ratio: {metrics.sharpe_ratio:.3f}")
            
            return optimization_result
            
        except Exception as e:
            self.logger.error(f"Error in portfolio optimization: {e}")
            return {}
    
    def rebalance_portfolio(self, current_weights: Dict[str, float], target_weights: Dict[str, float],
                           transaction_costs: float = 0.001) -> Dict[str, Any]:
        """Calculate optimal rebalancing trades"""
        try:
            rebalancing_trades = {}
            total_turnover = 0.0
            total_cost = 0.0
            
            # Calculate trades needed
            all_assets = set(current_weights.keys()) | set(target_weights.keys())
            
            for asset in all_assets:
                current_weight = current_weights.get(asset, 0.0)
                target_weight = target_weights.get(asset, 0.0)
                
                trade_amount = target_weight - current_weight
                
                if abs(trade_amount) > 0.001:  # Only trade if significant difference
                    rebalancing_trades[asset] = trade_amount
                    total_turnover += abs(trade_amount)
                    total_cost += abs(trade_amount) * transaction_costs
            
            rebalancing_result = {
                "rebalancing_id": f"rebal_{int(time.time())}",
                "timestamp": datetime.now(),
                "trades": rebalancing_trades,
                "total_turnover": total_turnover,
                "estimated_cost": total_cost,
                "transaction_cost_rate": transaction_costs
            }
            
            self.logger.info(f"Rebalancing calculated: {len(rebalancing_trades)} trades, "
                           f"turnover: {total_turnover:.3f}, cost: {total_cost:.4f}")
            
            return rebalancing_result
            
        except Exception as e:
            self.logger.error(f"Error calculating rebalancing: {e}")
            return {}
    
    def get_optimization_history(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent optimization history"""
        return self.optimization_history[-limit:]


# Global instance
advanced_portfolio_optimizer = AdvancedPortfolioOptimizer()
