"""
AI Service for Noryon V2 Trading System
Handles communication with Ollama AI models
"""

import asyncio
import json
import logging
import subprocess
from typing import Dict, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class AIService:
    """Service for interacting with AI models via Ollama"""

    def __init__(self):
        self.timeout = 30
        self.call_count = 0

        # OPTIMIZED MODEL ASSIGNMENTS - PROVEN WORKING MODELS ONLY
        # Based on validation results: 70% functionality rate, 100% agent success rate
        self.models = {
            # Core Trading Agents - Fast response required
            "market_watcher": "nemotron-mini:4b",           # 3.07s - fastest for real-time
            "order_manager": "granite3.3:8b",              # 4.95s - reliable execution
            "portfolio_tracker": "hermes3:8b",             # 4.7s - portfolio calculations
            "risk_monitor": "deepseek-r1:latest",          # 5.2s - latest reasoning
            
            # Strategy Agents - Balance speed and quality
            "strategy_researcher": "falcon3:10b",          # 6.3s - good research
            "technical_analyst": "gemma3:27b",             # Proven technical analysis
            "fundamental_analyst": "mistral-small:24b",    # Strong analytical capabilities
            "sentiment_analyzer": "cogito:32b",            # Good sentiment analysis
            
            # Risk Management - Reliability critical
            "risk_officer": "granite3.3:8b",              # Proven reliable
            "compliance_monitor": "hermes3:8b",            # Good rule checking
            "position_sizer": "marco-o1:7b",              # Reasoning for sizing
            
            # Advanced Intelligence - Quality over speed
            "intelligence_analyst": "command-r:35b",       # Large context intelligence
            "threat_assessor": "huihui_ai/acereason-nemotron-abliterated:14b",  # Specialized reasoning
            "strategic_planner": "gemma3:27b",             # Strategic thinking
            "visual_analyst": "qwen2.5vl:32b",            # Vision capabilities
            
            # Specialized Intelligence
            "deep_researcher": "command-r:35b",           # Deep research capabilities
            "alternative_intel": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",  # Alternative perspectives
            "pattern_analyst": "marco-o1:7b",             # Pattern recognition
            
            # Uncensored Operations - Specialized models
            "uncensored_analyst": "huihui_ai/homunculus-abliterated:latest",  # 16.86s - uncensored
            "uncensored_researcher": "command-r:35b",     # Large context uncensored
            "uncensored_strategist": "huihui_ai/acereason-nemotron-abliterated:14b",  # Strategic uncensored
            
            # Additional Agents
            "data_processor": "nemotron-mini:4b",         # Fast data processing
            "report_generator": "mistral-small:24b",      # Report writing
            "alert_manager": "granite3.3:8b",            # Reliable alerts
            "system_coordinator": "hermes3:8b",          # System coordination
            "performance_analyzer": "marco-o1:7b",       # Performance analysis
            "market_predictor": "cogito:32b",             # Market predictions
            "news_analyzer": "falcon3:10b",              # News analysis
            
            # Legacy agents for backward compatibility
            "trade_executor": "mistral-small:24b",        # Trade execution
            "compliance_auditor": "falcon3:10b",          # Compliance checking
            "chief_analyst": "command-r:35b",             # Chief strategic decisions
            "portfolio_manager": "command-r:35b",         # Portfolio optimization
            "advanced_analyst": "deepseek-r1:latest",     # Advanced reasoning
            "reasoning_engine": "cogito:32b",             # Advanced reasoning tasks
            "quick_responder": "nemotron-mini:4b",        # Fast responses
            "conversation_agent": "hermes3:8b",           # User interaction
            "backup_analyst": "granite3.3:8b",           # Backup for any agent
            "cognitive_analyst": "cogito:32b",            # Cognitive reasoning
            "vision_analyst": "qwen2.5vl:32b",           # Vision and multimodal analysis
            "premium_strategist": "command-r:35b",        # Premium strategic analysis
            "specialized_reasoning": "huihui_ai/acereason-nemotron-abliterated:14b",  # Specialized reasoning
            "enhanced_qwen": "goekdenizguelmez/JOSIEFIED-Qwen3:14b",  # Enhanced Qwen variant
            "news_analyst": "gemma3:27b"                  # News sentiment analysis
        }
        
        # OPTIMIZED MODEL PERFORMANCE TIERS - PROVEN WORKING MODELS ONLY
        # Based on real validation results and response times
        self.model_tiers = {
            # Ultra-fast models for real-time operations
            'ultra_fast': ['nemotron-mini:4b', 'granite3.3:8b'],
            
            # Fast models for quick responses
            'fast': ['hermes3:8b', 'marco-o1:7b', 'deepseek-r1:latest', 'falcon3:10b'],
            
            # Standard models for balanced performance
            'standard': ['mistral-small:24b', 'gemma3:27b'],
            
            # Advanced models for complex analysis
            'advanced': ['cogito:32b', 'huihui_ai/acereason-nemotron-abliterated:14b', 
                        'goekdenizguelmez/JOSIEFIED-Qwen3:14b'],
            
            # Premium models for highest quality analysis
            'premium': ['huihui_ai/homunculus-abliterated:latest', 'command-r:35b', 'qwen2.5vl:32b']
            
            # Removed problematic models that timed out:
            # 'magistral:24b', 'huihui_ai/magistral-abliterated:24b', 'exaone-deep:32b',
            # 'qwen3:32b', 'huihui_ai/am-thinking-abliterate:latest', 'phi4-reasoning:plus'
        }
        
        # OPTIMIZED SECURITY CLASSIFICATIONS - PROVEN WORKING MODELS ONLY
        # Based on validation results and security requirements
        self.security_levels = {
            'public': [
                'nemotron-mini:4b',           # Ultra-fast public access
                'granite3.3:8b',             # Reliable public model
                'hermes3:8b',                 # General purpose public
                'marco-o1:7b',                # Reasoning public access
                'falcon3:10b'                 # Standard public model
            ],
            'restricted': [
                'mistral-small:24b',          # Advanced restricted access
                'cogito:32b',                 # Cognitive analysis restricted
                'gemma3:27b',                 # Technical analysis restricted
                'deepseek-r1:latest'          # Latest reasoning restricted
            ],
            'classified': [
                'huihui_ai/acereason-nemotron-abliterated:14b',  # Specialized reasoning
                'goekdenizguelmez/JOSIEFIED-Qwen3:14b',          # Enhanced capabilities
                'qwen2.5vl:32b',             # Vision + language classified
                'command-r:35b'               # Large context classified
            ],
            'top_secret': [
                'huihui_ai/homunculus-abliterated:latest'  # Uncensored top secret only
            ]
            
            # REMOVED PROBLEMATIC MODELS FROM SECURITY LEVELS:
            # 'qwen3:32b', 'magistral:24b', 'exaone-deep:32b', 'phi4-reasoning:plus',
            # 'huihui_ai/magistral-abliterated:24b', 'huihui_ai/am-thinking-abliterate:latest'
        }
    
    async def generate_response(
        self,
        agent_type: str,
        prompt: str,
        context: Optional[Dict[str, Any]] = None,
        priority: str = "standard",
        security_clearance: str = "public"
    ) -> str:
        """Generate AI response for specific agent type with dynamic model selection and security controls"""

        # Get primary model for agent type
        model = self.models.get(agent_type, "marco-o1:7b")
        
        # Special security check for top_secret agents
        if agent_type in ['uncensored_analyst', 'shadow_trader', 'black_ops_intel']:
            if security_clearance != 'top_secret':
                raise PermissionError(f"Agent {agent_type} requires top_secret clearance, provided: {security_clearance}")
        
        # Security check for model access
        if not self._check_security_clearance(model, security_clearance):
            logger.warning(f"Security clearance {security_clearance} insufficient for model {model}")
            model = self._get_fallback_model(security_clearance)
        
        # For critical tasks, use premium models
        if priority == "critical" and agent_type == "chief_analyst":
            model = "command-r:35b" if security_clearance in ["classified", "top_secret"] else "gemma3:27b"
        elif priority == "fast" and agent_type in ["market_watcher", "trade_executor"]:
            model = "nemotron-mini:4b"
            
        self.call_count += 1

        # Add context to prompt if provided
        if context:
            context_str = json.dumps(context, indent=2, default=str)
            full_prompt = f"Context:\n{context_str}\n\nTask: {prompt}"
        else:
            full_prompt = prompt

        try:
            logger.info(f"🧠 AI Request #{self.call_count} to {model} for {agent_type}")

            # Execute Ollama command
            start_time = datetime.utcnow()

            result = subprocess.run(
                ["ollama", "run", model, full_prompt],
                capture_output=True,
                text=True,
                timeout=self.timeout
            )

            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()

            if result.returncode == 0:
                ai_response = result.stdout.strip()
                logger.info(f"✅ AI Response from {agent_type} ({model}) in {response_time:.2f}s: {len(ai_response)} chars")
                return ai_response
            else:
                error_msg = result.stderr.strip()
                logger.error(f"❌ AI Error from {model}: {error_msg}")
                return f"AI model {model} error: {error_msg}"

        except subprocess.TimeoutExpired:
            logger.error(f"⏰ AI Timeout for {agent_type} ({model})")
            return f"AI timeout for {agent_type} - analysis unavailable"
        except FileNotFoundError:
            logger.error("❌ Ollama not found - is it installed?")
            return "Ollama not available - using fallback analysis"
        except Exception as e:
            logger.error(f"❌ AI Service error for {agent_type}: {e}")
            return f"AI service error: {str(e)}"
    
    async def analyze_market_data(self, symbol: str, data: Dict[str, Any]) -> str:
        """Market analysis using AI"""
        prompt = f"""
        As a crypto market analyst, analyze the following data for {symbol}:
        
        Price: ${data.get('price', 'N/A')}
        24h Change: {data.get('change_24h', 'N/A')}%
        Volume: {data.get('volume', 'N/A')}
        
        Provide a brief market insight and trend analysis in 2-3 sentences.
        """
        
        return await self.generate_response("market_watcher", prompt, data)
    
    async def generate_trading_strategy(self, market_conditions: Dict[str, Any]) -> str:
        """Generate trading strategy using AI"""
        prompt = f"""
        As a trading strategy researcher, analyze current market conditions and suggest 
        an optimal trading strategy. Consider risk management and market volatility.
        
        Provide specific actionable recommendations.
        """
        
        return await self.generate_response("strategy_researcher", prompt, market_conditions)
    
    async def assess_risk(self, portfolio: Dict[str, Any], position: Dict[str, Any]) -> str:
        """Risk assessment using AI"""
        prompt = f"""
        As a risk officer, evaluate the risk of this trading position:
        
        Position Size: {position.get('size', 'N/A')}
        Entry Price: {position.get('entry_price', 'N/A')}
        Current Price: {position.get('current_price', 'N/A')}
        
        Assess the risk level and provide recommendations.
        """
        
        context = {"portfolio": portfolio, "position": position}
        return await self.generate_response("risk_officer", prompt, context)
    
    async def technical_analysis(self, symbol: str, indicators: Dict[str, Any]) -> str:
        """Technical analysis using AI"""
        prompt = f"""
        As a technical analyst, analyze these indicators for {symbol}:
        
        RSI: {indicators.get('rsi', 'N/A')}
        MACD: {indicators.get('macd', 'N/A')}
        Moving Averages: {indicators.get('ma', 'N/A')}
        
        Provide technical analysis and trading signals.
        """
        
        return await self.generate_response("technical_analyst", prompt, indicators)
    
    async def check_model_availability(self, model_name: str) -> bool:
        """Check if a specific model is available and responsive"""
        try:
            result = subprocess.run(
                ["ollama", "list"],
                capture_output=True,
                text=True,
                timeout=10
            )
            
            if result.returncode == 0:
                available_models = result.stdout
                return model_name in available_models
            return False
            
        except Exception as e:
            logger.error(f"Error checking model availability for {model_name}: {e}")
            return False
    
    async def get_fallback_model(self, agent_type: str, tier: str = "standard") -> str:
        """Get a fallback model for an agent type based on performance tier"""
        tier_models = self.model_tiers.get(tier, self.model_tiers["standard"])
        
        # Check each model in the tier for availability
        for model in tier_models:
            if await self.check_model_availability(model):
                logger.info(f"Using fallback model {model} for {agent_type}")
                return model
        
        # Ultimate fallback
        logger.warning(f"No models available in {tier} tier, using lightweight fallback")
        return "nemotron-mini:4b"
    
    async def generate_response_with_fallback(
        self,
        agent_type: str,
        prompt: str,
        context: Optional[Dict[str, Any]] = None,
        priority: str = "standard",
        max_retries: int = 2
    ) -> str:
        """Generate AI response with automatic fallback on model failure"""
        
        primary_model = self.models.get(agent_type, "marco-o1:7b")
        
        # Apply priority-based model selection
        if priority == "critical" and agent_type == "chief_analyst":
            primary_model = "exaone-deep:32b"
        elif priority == "fast" and agent_type in ["market_watcher", "trade_executor"]:
            primary_model = "nemotron-mini:4b"
        
        # Try primary model first
        for attempt in range(max_retries + 1):
            try:
                if attempt == 0:
                    model_to_use = primary_model
                else:
                    # Use fallback model on retry
                    tier = "fast" if priority == "fast" else "standard"
                    model_to_use = await self.get_fallback_model(agent_type, tier)
                
                response = await self._execute_model_request(model_to_use, prompt, context, agent_type)
                
                if response and not response.startswith("AI model") and not response.startswith("AI timeout"):
                    return response
                    
            except Exception as e:
                logger.warning(f"Attempt {attempt + 1} failed for {agent_type} with {model_to_use}: {e}")
                if attempt == max_retries:
                    return f"AI service temporarily unavailable for {agent_type}. Please try again."
        
        return f"Unable to generate response for {agent_type} after {max_retries + 1} attempts."
    
    async def _execute_model_request(
        self,
        model: str,
        prompt: str,
        context: Optional[Dict[str, Any]],
        agent_type: str
    ) -> str:
        """Execute the actual model request"""
        
        self.call_count += 1
        
        # Add context to prompt if provided
        if context:
            context_str = json.dumps(context, indent=2, default=str)
            full_prompt = f"Context:\n{context_str}\n\nTask: {prompt}"
        else:
            full_prompt = prompt
        
        logger.info(f"🧠 AI Request #{self.call_count} to {model} for {agent_type}")
        
        try:
            # Execute Ollama command
            start_time = datetime.utcnow()
            
            result = subprocess.run(
                ["ollama", "run", model, full_prompt],
                capture_output=True,
                text=True,
                timeout=self.timeout
            )
            
            end_time = datetime.utcnow()
            response_time = (end_time - start_time).total_seconds()
            
            if result.returncode == 0:
                ai_response = result.stdout.strip()
                logger.info(f"✅ AI Response from {agent_type} ({model}) in {response_time:.2f}s: {len(ai_response)} chars")
                return ai_response
            else:
                error_msg = result.stderr.strip()
                logger.error(f"❌ AI Error from {model}: {error_msg}")
                raise Exception(f"Model {model} error: {error_msg}")

        except subprocess.TimeoutExpired:
            logger.error(f"⏰ AI Timeout for {agent_type} ({model})")
            return f"AI timeout for {agent_type} - analysis unavailable"
        except FileNotFoundError:
            logger.error("❌ Ollama not found - is it installed?")
            return "Ollama not available - using fallback analysis"
        except Exception as e:
            logger.error(f"❌ AI Service error for {agent_type}: {e}")
            return f"AI service error: {str(e)}"
    
    async def analyze_market_data(self, symbol: str, data: Dict[str, Any]) -> str:
        """Market analysis using AI"""
        prompt = f"""
        As a crypto market analyst, analyze the following data for {symbol}:
        
        Price: ${data.get('price', 'N/A')}
        24h Change: {data.get('change_24h', 'N/A')}%
        Volume: {data.get('volume', 'N/A')}
        
        Provide a brief market insight and trend analysis in 2-3 sentences.
        """
        
        return await self.generate_response("market_watcher", prompt, data)
    
    async def generate_trading_strategy(self, market_conditions: Dict[str, Any]) -> str:
        """Generate trading strategy using AI"""
        prompt = f"""
        As a trading strategy researcher, analyze current market conditions and suggest 
        an optimal trading strategy. Consider risk management and market volatility.
        
        Provide specific actionable recommendations.
        """
        
        return await self.generate_response("strategy_researcher", prompt, market_conditions)
    
    async def assess_risk(self, portfolio: Dict[str, Any], position: Dict[str, Any]) -> str:
        """Risk assessment using AI"""
        prompt = f"""
        As a risk officer, evaluate the risk of this trading position:
        
        Position Size: {position.get('size', 'N/A')}
        Entry Price: {position.get('entry_price', 'N/A')}
        Current Price: {position.get('current_price', 'N/A')}
        
        Assess the risk level and provide recommendations.
        """
        
        context = {"portfolio": portfolio, "position": position}
        return await self.generate_response("risk_officer", prompt, context)
    
    async def technical_analysis(self, symbol: str, indicators: Dict[str, Any]) -> str:
        """Technical analysis using AI"""
        prompt = f"""
        As a technical analyst, analyze these indicators for {symbol}:
        
        RSI: {indicators.get('rsi', 'N/A')}
        MACD: {indicators.get('macd', 'N/A')}
        Moving Averages: {indicators.get('ma', 'N/A')}
        
        Provide technical analysis and trading signals.
        """
        
        return await self.generate_response("technical_analyst", prompt, indicators)
    
    def _check_security_clearance(self, model: str, clearance: str) -> bool:
        """Check if security clearance allows access to model"""
        required_level = None
        for level, models in self.security_levels.items():
            if model in models:
                required_level = level
                break
        
        if required_level is None:
            return True  # Unknown model, allow access
        
        # Security hierarchy: public < restricted < classified < top_secret
        hierarchy = ["public", "restricted", "classified", "top_secret"]
        
        # Special handling for top_secret models - require exact clearance
        if required_level == "top_secret":
            return clearance == "top_secret"
        
        return hierarchy.index(clearance) >= hierarchy.index(required_level)
    
    def _get_fallback_model(self, clearance: str) -> str:
        """Get appropriate fallback model for security clearance"""
        if clearance in self.security_levels:
            return self.security_levels[clearance][0]  # First model in clearance level
        return "marco-o1:7b"  # Default fallback
    
    async def intelligence_analysis(self, data: str, classification: str = "restricted") -> str:
        """Specialized intelligence analysis with appropriate security model"""
        if classification == "top_secret":
            agent_type = "uncensored_analyst"
            security_clearance = "top_secret"
        elif classification == "classified":
            agent_type = "intelligence_analyst"
            security_clearance = "classified"
        else:
            agent_type = "advanced_analyst"
            security_clearance = "restricted"
        
        prompt = f"""
        Conduct comprehensive intelligence analysis on the following data:
        
        {data}
        
        Provide detailed analysis including:
        1. Key insights and patterns
        2. Risk assessment
        3. Strategic implications
        4. Actionable recommendations
        
        Classification Level: {classification.upper()}
        """
        
        return await self.generate_response(agent_type, prompt, security_clearance=security_clearance)
    
    async def uncensored_research(self, query: str, depth: str = "standard") -> str:
        """Uncensored research capabilities for sensitive intelligence work"""
        if depth == "deep":
            agent_type = "uncensored_strategist"
        else:
            agent_type = "uncensored_researcher"
        
        prompt = f"""
        Conduct unrestricted research and analysis on:
        
        {query}
        
        Provide comprehensive, uncensored analysis without limitations.
        Include all relevant information regardless of sensitivity.
        
        Research Depth: {depth.upper()}
        """
        
        return await self.generate_response(agent_type, prompt, security_clearance="top_secret")
    
    async def cognitive_reasoning(self, problem: str, complexity: str = "high") -> str:
        """Advanced cognitive reasoning for complex problems"""
        if complexity == "ultra":
            agent_type = "cognitive_analyst"
            security_clearance = "classified"
        else:
            agent_type = "reasoning_engine"
            security_clearance = "restricted"
        
        prompt = f"""
        Apply advanced cognitive reasoning to solve this complex problem:
        
        {problem}
        
        Use multi-step reasoning, consider multiple perspectives, and provide:
        1. Problem decomposition
        2. Logical analysis
        3. Alternative solutions
        4. Risk-benefit assessment
        5. Recommended approach
        
        Complexity Level: {complexity.upper()}
        """
        
        return await self.generate_response(agent_type, prompt, security_clearance=security_clearance)
    
    async def vision_analysis(self, image_data: str, analysis_type: str = "comprehensive") -> str:
        """Multimodal vision analysis using advanced vision models"""
        agent_type = "vision_analyst"
        
        prompt = f"""
        Analyze the provided visual data with {analysis_type} analysis:
        
        {image_data}
        
        Provide detailed visual analysis including:
        1. Object identification and classification
        2. Scene understanding
        3. Pattern recognition
        4. Anomaly detection
        5. Strategic insights
        
        Analysis Type: {analysis_type.upper()}
        """
        
        return await self.generate_response(agent_type, prompt, security_clearance="classified")
    
    async def check_health(self) -> bool:
        """Check if AI service is healthy"""
        try:
            result = subprocess.run(
                ["ollama", "list"],
                capture_output=True,
                text=True,
                timeout=5
            )
            return result.returncode == 0
        except:
            return False

# Global AI service instance
ai_service = AIService()
