#!/usr/bin/env python3
"""
Noryon V2 - Realistic AI Agent Coordinator
Coordinates multiple AI agents in realistic trading environment

This module manages:
- Multiple AI trading agents with different strategies
- Agent performance tracking and analytics
- Risk management and position monitoring
- Agent communication and coordination
- Real-time decision making and execution
- Learning and adaptation mechanisms
"""

import asyncio
import json
import logging
import random
import time
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal
from dataclasses import dataclass, field
from enum import Enum

from src.core.config import Config
from src.core.logger import get_logger
from src.services.ai_service import AIService
from src.services.realistic_trading_environment import (
    Order, Trade, OrderType, OrderSide, OrderStatus, 
    Portfolio, Position, MarketTick, MarketCondition
)
from src.services.realistic_order_engine import RealisticOrderEngine
from src.services.realistic_market_feed import RealisticMarketFeed

logger = get_logger(__name__)

class AgentType(Enum):
    SCALPER = "scalper"              # High-frequency, small profits
    SWING_TRADER = "swing_trader"    # Medium-term trend following
    ARBITRAGE = "arbitrage"          # Price difference exploitation
    MARKET_MAKER = "market_maker"    # Liquidity provision
    MOMENTUM = "momentum"            # Trend momentum trading
    MEAN_REVERSION = "mean_reversion" # Counter-trend trading
    NEWS_TRADER = "news_trader"      # Event-driven trading
    RISK_MANAGER = "risk_manager"    # Portfolio risk oversight

class AgentStatus(Enum):
    ACTIVE = "active"
    PAUSED = "paused"
    STOPPED = "stopped"
    ERROR = "error"

@dataclass
class AgentConfig:
    agent_id: str
    agent_type: AgentType
    name: str
    model_name: str
    max_position_size: Decimal
    max_daily_trades: int
    risk_tolerance: float  # 0.0 to 1.0
    preferred_symbols: List[str]
    strategy_params: Dict[str, Any]
    initial_balance: Decimal

@dataclass
class AgentPerformance:
    agent_id: str
    total_trades: int = 0
    winning_trades: int = 0
    losing_trades: int = 0
    total_pnl: Decimal = Decimal('0')
    realized_pnl: Decimal = Decimal('0')
    unrealized_pnl: Decimal = Decimal('0')
    max_drawdown: Decimal = Decimal('0')
    sharpe_ratio: float = 0.0
    win_rate: float = 0.0
    avg_trade_duration: float = 0.0
    last_trade_time: Optional[datetime] = None
    daily_pnl: Decimal = Decimal('0')
    weekly_pnl: Decimal = Decimal('0')
    monthly_pnl: Decimal = Decimal('0')

@dataclass
class TradingDecision:
    agent_id: str
    symbol: str
    action: str  # "BUY", "SELL", "HOLD"
    quantity: Decimal
    price: Optional[Decimal]
    order_type: OrderType
    confidence: float  # 0.0 to 1.0
    reasoning: str
    timestamp: datetime
    risk_score: float

class RealisticAgentCoordinator:
    """
    Coordinates multiple AI agents in realistic trading environment
    """
    
    def __init__(self, config: Config, trading_env, order_engine: RealisticOrderEngine, market_feed: RealisticMarketFeed):
        self.config = config
        self.trading_env = trading_env
        self.order_engine = order_engine
        self.market_feed = market_feed
        self.ai_service = AIService(config)
        self.logger = get_logger(__name__)
        
        # Agent management
        self.agents: Dict[str, AgentConfig] = {}
        self.agent_status: Dict[str, AgentStatus] = {}
        self.agent_performance: Dict[str, AgentPerformance] = {}
        self.agent_decisions: Dict[str, List[TradingDecision]] = {}
        
        # Trading state
        self.active_orders: Dict[str, List[str]] = {}  # agent_id -> order_ids
        self.agent_last_action: Dict[str, datetime] = {}
        
        # Risk management
        self.global_risk_limits = {
            'max_total_exposure': Decimal('1000000'),  # $1M total
            'max_agent_exposure': Decimal('100000'),   # $100k per agent
            'max_daily_loss': Decimal('50000'),        # $50k daily loss limit
            'max_correlation': 0.8,                    # Max correlation between agents
        }
        
        # Performance tracking
        self.coordination_stats = {
            'total_decisions': 0,
            'executed_orders': 0,
            'rejected_decisions': 0,
            'risk_violations': 0,
            'coordination_events': 0
        }
        
        # Running state
        self.running = False
        self.coordination_task = None
        self.performance_task = None
        
        # Initialize default agents
        self._initialize_default_agents()
    
    def _initialize_default_agents(self):
        """Initialize a diverse set of AI trading agents"""
        
        default_agents = [
            AgentConfig(
                agent_id="scalper_001",
                agent_type=AgentType.SCALPER,
                name="Lightning Scalper",
                model_name="llama3.2:3b",
                max_position_size=Decimal('10000'),
                max_daily_trades=100,
                risk_tolerance=0.3,
                preferred_symbols=["BTC/USDT", "ETH/USDT"],
                strategy_params={"timeframe": "1m", "profit_target": 0.002, "stop_loss": 0.001},
                initial_balance=Decimal('50000')
            ),
            AgentConfig(
                agent_id="swing_trader_001",
                agent_type=AgentType.SWING_TRADER,
                name="Trend Rider",
                model_name="llama3.2:3b",
                max_position_size=Decimal('25000'),
                max_daily_trades=10,
                risk_tolerance=0.6,
                preferred_symbols=["BTC/USDT", "ETH/USDT", "SOL/USDT"],
                strategy_params={"timeframe": "15m", "trend_threshold": 0.02, "hold_time": "4h"},
                initial_balance=Decimal('100000')
            ),
            AgentConfig(
                agent_id="arbitrage_001",
                agent_type=AgentType.ARBITRAGE,
                name="Arb Hunter",
                model_name="llama3.2:1b",
                max_position_size=Decimal('20000'),
                max_daily_trades=50,
                risk_tolerance=0.2,
                preferred_symbols=["BTC/USDT", "ETH/USDT", "BNB/USDT"],
                strategy_params={"min_spread": 0.001, "max_hold_time": "5m"},
                initial_balance=Decimal('75000')
            ),
            AgentConfig(
                agent_id="market_maker_001",
                agent_type=AgentType.MARKET_MAKER,
                name="Liquidity Provider",
                model_name="llama3.2:3b",
                max_position_size=Decimal('30000'),
                max_daily_trades=200,
                risk_tolerance=0.4,
                preferred_symbols=["BTC/USDT", "ETH/USDT", "ADA/USDT"],
                strategy_params={"spread_target": 0.0005, "inventory_limit": 0.5},
                initial_balance=Decimal('150000')
            ),
            AgentConfig(
                agent_id="momentum_001",
                agent_type=AgentType.MOMENTUM,
                name="Momentum Chaser",
                model_name="llama3.2:3b",
                max_position_size=Decimal('40000'),
                max_daily_trades=20,
                risk_tolerance=0.7,
                preferred_symbols=["SOL/USDT", "AVAX/USDT", "MATIC/USDT"],
                strategy_params={"momentum_threshold": 0.03, "breakout_volume": 2.0},
                initial_balance=Decimal('120000')
            ),
            AgentConfig(
                agent_id="mean_reversion_001",
                agent_type=AgentType.MEAN_REVERSION,
                name="Contrarian",
                model_name="llama3.2:3b",
                max_position_size=Decimal('35000'),
                max_daily_trades=15,
                risk_tolerance=0.5,
                preferred_symbols=["XRP/USDT", "DOT/USDT", "LINK/USDT"],
                strategy_params={"deviation_threshold": 0.025, "reversion_target": 0.015},
                initial_balance=Decimal('100000')
            ),
            AgentConfig(
                agent_id="news_trader_001",
                agent_type=AgentType.NEWS_TRADER,
                name="News Hawk",
                model_name="llama3.2:3b",
                max_position_size=Decimal('50000'),
                max_daily_trades=30,
                risk_tolerance=0.8,
                preferred_symbols=self.trading_env.symbols,
                strategy_params={"reaction_time": "30s", "news_confidence": 0.7},
                initial_balance=Decimal('200000')
            ),
            AgentConfig(
                agent_id="risk_manager_001",
                agent_type=AgentType.RISK_MANAGER,
                name="Risk Guardian",
                model_name="llama3.2:3b",
                max_position_size=Decimal('0'),  # Risk manager doesn't trade
                max_daily_trades=0,
                risk_tolerance=0.1,
                preferred_symbols=self.trading_env.symbols,
                strategy_params={"monitoring_interval": "1m", "alert_threshold": 0.05},
                initial_balance=Decimal('0')
            )
        ]
        
        # Register agents
        for agent_config in default_agents:
            self.register_agent(agent_config)
    
    def register_agent(self, agent_config: AgentConfig):
        """Register a new trading agent"""
        self.agents[agent_config.agent_id] = agent_config
        self.agent_status[agent_config.agent_id] = AgentStatus.ACTIVE
        self.agent_performance[agent_config.agent_id] = AgentPerformance(agent_id=agent_config.agent_id)
        self.agent_decisions[agent_config.agent_id] = []
        self.active_orders[agent_config.agent_id] = []
        
        # Initialize portfolio if agent trades
        if agent_config.initial_balance > 0:
            self.trading_env.initialize_agent_portfolio(agent_config.agent_id, agent_config.initial_balance)
        
        self.logger.info(f"🤖 Registered agent: {agent_config.name} ({agent_config.agent_type.value})")
    
    async def start(self):
        """Start the agent coordination system"""
        if self.running:
            return
        
        self.running = True
        
        # Subscribe to market data
        self.market_feed.subscribe(self._on_market_tick)
        
        # Start coordination tasks
        self.coordination_task = asyncio.create_task(self._coordination_loop())
        self.performance_task = asyncio.create_task(self._performance_tracking_loop())
        
        self.logger.info(f"🎯 Agent Coordinator started with {len(self.agents)} agents")
    
    async def stop(self):
        """Stop the agent coordination system"""
        self.running = False
        
        # Unsubscribe from market data
        self.market_feed.unsubscribe(self._on_market_tick)
        
        # Stop tasks
        if self.coordination_task:
            self.coordination_task.cancel()
        if self.performance_task:
            self.performance_task.cancel()
        
        try:
            if self.coordination_task:
                await self.coordination_task
            if self.performance_task:
                await self.performance_task
        except asyncio.CancelledError:
            pass
        
        self.logger.info("🛑 Agent Coordinator stopped")
    
    def _on_market_tick(self, tick: MarketTick):
        """Handle incoming market tick data"""
        # This will trigger agent decision making in the coordination loop
        pass
    
    async def _coordination_loop(self):
        """Main coordination loop for agent decision making"""
        while self.running:
            try:
                # Process each active agent
                for agent_id, agent_config in self.agents.items():
                    if self.agent_status[agent_id] != AgentStatus.ACTIVE:
                        continue
                    
                    # Check if agent should make a decision
                    if await self._should_agent_act(agent_id):
                        decision = await self._get_agent_decision(agent_id, agent_config)
                        if decision:
                            await self._process_agent_decision(decision)
                
                # Coordination between agents
                await self._coordinate_agents()
                
                # Risk management checks
                await self._perform_risk_checks()
                
                # Control loop frequency
                await asyncio.sleep(1.0)  # 1 second intervals
                
            except Exception as e:
                self.logger.error(f"Error in coordination loop: {e}")
                await asyncio.sleep(5.0)
    
    async def _should_agent_act(self, agent_id: str) -> bool:
        """Determine if an agent should make a trading decision"""
        agent_config = self.agents[agent_id]
        
        # Check time since last action
        last_action = self.agent_last_action.get(agent_id)
        if last_action:
            time_since_last = datetime.now(timezone.utc) - last_action
            
            # Different agents have different decision frequencies
            min_intervals = {
                AgentType.SCALPER: 10,        # 10 seconds
                AgentType.SWING_TRADER: 300,  # 5 minutes
                AgentType.ARBITRAGE: 5,       # 5 seconds
                AgentType.MARKET_MAKER: 30,   # 30 seconds
                AgentType.MOMENTUM: 60,       # 1 minute
                AgentType.MEAN_REVERSION: 120, # 2 minutes
                AgentType.NEWS_TRADER: 30,    # 30 seconds
                AgentType.RISK_MANAGER: 60    # 1 minute
            }
            
            min_interval = min_intervals.get(agent_config.agent_type, 60)
            if time_since_last.total_seconds() < min_interval:
                return False
        
        # Check daily trade limits
        performance = self.agent_performance[agent_id]
        if performance.total_trades >= agent_config.max_daily_trades:
            return False
        
        # Random factor to add realism
        decision_probability = {
            AgentType.SCALPER: 0.3,
            AgentType.SWING_TRADER: 0.1,
            AgentType.ARBITRAGE: 0.4,
            AgentType.MARKET_MAKER: 0.5,
            AgentType.MOMENTUM: 0.2,
            AgentType.MEAN_REVERSION: 0.15,
            AgentType.NEWS_TRADER: 0.25,
            AgentType.RISK_MANAGER: 0.8
        }
        
        probability = decision_probability.get(agent_config.agent_type, 0.2)
        return random.random() < probability
    
    async def _get_agent_decision(self, agent_id: str, agent_config: AgentConfig) -> Optional[TradingDecision]:
        """Get trading decision from an AI agent"""
        try:
            # Prepare market context
            market_context = self._prepare_market_context(agent_config)
            
            # Prepare agent context
            agent_context = self._prepare_agent_context(agent_id)
            
            # Create prompt for AI agent
            prompt = self._create_agent_prompt(agent_config, market_context, agent_context)
            
            # Get AI response
            response = await self.ai_service.generate_response(
                prompt=prompt,
                agent_type=agent_config.agent_type.value,
                model_name=agent_config.model_name
            )
            
            # Parse decision from response
            decision = self._parse_agent_decision(agent_id, response)
            
            if decision:
                self.agent_decisions[agent_id].append(decision)
                # Keep only recent decisions (last 100)
                if len(self.agent_decisions[agent_id]) > 100:
                    self.agent_decisions[agent_id] = self.agent_decisions[agent_id][-100:]
                
                self.coordination_stats['total_decisions'] += 1
                self.agent_last_action[agent_id] = datetime.now(timezone.utc)
            
            return decision
            
        except Exception as e:
            self.logger.error(f"Error getting decision from agent {agent_id}: {e}")
            return None
    
    def _prepare_market_context(self, agent_config: AgentConfig) -> Dict[str, Any]:
        """Prepare current market context for agent decision making"""
        context = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'market_condition': self.trading_env.market_condition.value,
            'symbols': {}
        }
        
        # Add data for preferred symbols
        for symbol in agent_config.preferred_symbols:
            if symbol in self.trading_env.current_prices:
                recent_ticks = self.trading_env.market_ticks[symbol][-10:]  # Last 10 ticks
                
                context['symbols'][symbol] = {
                    'current_price': float(self.trading_env.current_prices[symbol]),
                    'volatility': self.trading_env.volatility_levels[symbol],
                    'trend_direction': self.trading_env.trend_directions.get(symbol, 0),
                    'trend_strength': self.trading_env.trend_strengths.get(symbol, 0),
                    'recent_prices': [float(tick.price) for tick in recent_ticks],
                    'recent_volumes': [float(tick.volume) for tick in recent_ticks],
                    'order_book': {
                        'best_bid': float(self.trading_env.order_books[symbol].bids[0].price) if self.trading_env.order_books[symbol].bids else 0,
                        'best_ask': float(self.trading_env.order_books[symbol].asks[0].price) if self.trading_env.order_books[symbol].asks else 0,
                        'bid_depth': sum(float(level.quantity) for level in self.trading_env.order_books[symbol].bids[:5]),
                        'ask_depth': sum(float(level.quantity) for level in self.trading_env.order_books[symbol].asks[:5])
                    }
                }
        
        # Add recent news if available
        if hasattr(self.market_feed, 'get_recent_news'):
            context['recent_news'] = self.market_feed.get_recent_news(limit=5)
        
        return context
    
    def _prepare_agent_context(self, agent_id: str) -> Dict[str, Any]:
        """Prepare agent-specific context"""
        portfolio = self.trading_env.portfolios.get(agent_id)
        performance = self.agent_performance[agent_id]
        
        context = {
            'agent_id': agent_id,
            'performance': {
                'total_trades': performance.total_trades,
                'win_rate': performance.win_rate,
                'total_pnl': float(performance.total_pnl),
                'daily_pnl': float(performance.daily_pnl),
                'max_drawdown': float(performance.max_drawdown)
            },
            'active_orders': len(self.active_orders[agent_id]),
            'recent_decisions': [{
                'symbol': d.symbol,
                'action': d.action,
                'confidence': d.confidence,
                'timestamp': d.timestamp.isoformat()
            } for d in self.agent_decisions[agent_id][-5:]]  # Last 5 decisions
        }
        
        if portfolio:
            context['portfolio'] = {
                'cash_balance': float(portfolio.cash_balance),
                'total_value': float(portfolio.total_value),
                'positions': {
                    symbol: {
                        'quantity': float(pos.quantity),
                        'avg_price': float(pos.avg_price),
                        'unrealized_pnl': float(pos.unrealized_pnl)
                    } for symbol, pos in portfolio.positions.items()
                }
            }
        
        return context
    
    def _create_agent_prompt(self, agent_config: AgentConfig, market_context: Dict[str, Any], agent_context: Dict[str, Any]) -> str:
        """Create AI prompt for agent decision making"""
        
        base_prompt = f"""
You are {agent_config.name}, a {agent_config.agent_type.value} trading agent in the Noryon V2 system.

Your Strategy: {agent_config.agent_type.value}
Risk Tolerance: {agent_config.risk_tolerance}
Max Position Size: ${agent_config.max_position_size}
Strategy Parameters: {json.dumps(agent_config.strategy_params, indent=2)}

Current Market Context:
{json.dumps(market_context, indent=2)}

Your Current Status:
{json.dumps(agent_context, indent=2)}

Based on your strategy and the current market conditions, make a trading decision.

Respond with a JSON object containing:
{{
    "action": "BUY" | "SELL" | "HOLD",
    "symbol": "symbol to trade (if not HOLD)",
    "quantity": "amount to trade (if not HOLD)",
    "order_type": "MARKET" | "LIMIT" | "STOP",
    "price": "limit/stop price (if applicable)",
    "confidence": "0.0 to 1.0",
    "reasoning": "explanation of your decision",
    "risk_score": "0.0 to 1.0 (risk level of this trade)"
}}

Only trade symbols from your preferred list: {agent_config.preferred_symbols}
Consider your risk tolerance and current portfolio exposure.
"""
        
        return base_prompt
    
    def _parse_agent_decision(self, agent_id: str, response: str) -> Optional[TradingDecision]:
        """Parse AI agent response into trading decision"""
        try:
            # Extract JSON from response
            start_idx = response.find('{')
            end_idx = response.rfind('}') + 1
            
            if start_idx == -1 or end_idx == 0:
                return None
            
            json_str = response[start_idx:end_idx]
            decision_data = json.loads(json_str)
            
            # Validate decision
            if decision_data.get('action') == 'HOLD':
                return None
            
            if not all(key in decision_data for key in ['action', 'symbol', 'quantity', 'confidence', 'reasoning']):
                return None
            
            # Create decision object
            decision = TradingDecision(
                agent_id=agent_id,
                symbol=decision_data['symbol'],
                action=decision_data['action'],
                quantity=Decimal(str(decision_data['quantity'])),
                price=Decimal(str(decision_data['price'])) if decision_data.get('price') else None,
                order_type=OrderType(decision_data.get('order_type', 'MARKET')),
                confidence=float(decision_data['confidence']),
                reasoning=decision_data['reasoning'],
                timestamp=datetime.now(timezone.utc),
                risk_score=float(decision_data.get('risk_score', 0.5))
            )
            
            return decision
            
        except Exception as e:
            self.logger.error(f"Error parsing agent decision: {e}")
            return None
    
    async def _process_agent_decision(self, decision: TradingDecision):
        """Process and execute agent trading decision"""
        try:
            # Validate decision
            if not await self._validate_decision(decision):
                self.coordination_stats['rejected_decisions'] += 1
                return
            
            # Create order
            order = Order(
                symbol=decision.symbol,
                side=OrderSide.BUY if decision.action == 'BUY' else OrderSide.SELL,
                type=decision.order_type,
                quantity=decision.quantity,
                price=decision.price,
                agent_id=decision.agent_id,
                timestamp=decision.timestamp
            )
            
            # Submit order
            order_id = await self.order_engine.submit_order(order)
            self.active_orders[decision.agent_id].append(order_id)
            
            self.coordination_stats['executed_orders'] += 1
            
            self.logger.info(f"🎯 Agent {decision.agent_id} decision executed: {decision.action} {decision.quantity} {decision.symbol} (confidence: {decision.confidence:.2f})")
            self.logger.info(f"💭 Reasoning: {decision.reasoning}")
            
        except Exception as e:
            self.logger.error(f"Error processing agent decision: {e}")
    
    async def _validate_decision(self, decision: TradingDecision) -> bool:
        """Validate agent trading decision against risk limits"""
        agent_config = self.agents[decision.agent_id]
        
        # Check symbol is in preferred list
        if decision.symbol not in agent_config.preferred_symbols:
            self.logger.warning(f"Agent {decision.agent_id} tried to trade non-preferred symbol {decision.symbol}")
            return False
        
        # Check position size limits
        current_price = self.trading_env.current_prices[decision.symbol]
        order_value = decision.quantity * current_price
        
        if order_value > agent_config.max_position_size:
            self.logger.warning(f"Agent {decision.agent_id} order value ${order_value} exceeds limit ${agent_config.max_position_size}")
            return False
        
        # Check confidence threshold
        if decision.confidence < 0.3:  # Minimum confidence threshold
            self.logger.warning(f"Agent {decision.agent_id} decision confidence {decision.confidence} too low")
            return False
        
        # Check risk score
        if decision.risk_score > agent_config.risk_tolerance:
            self.logger.warning(f"Agent {decision.agent_id} risk score {decision.risk_score} exceeds tolerance {agent_config.risk_tolerance}")
            return False
        
        return True
    
    async def _coordinate_agents(self):
        """Coordinate between agents to avoid conflicts"""
        # Simple coordination: prevent opposing trades on same symbol
        recent_decisions = {}
        
        for agent_id, decisions in self.agent_decisions.items():
            if decisions:
                recent_decision = decisions[-1]
                if (datetime.now(timezone.utc) - recent_decision.timestamp).total_seconds() < 300:  # Last 5 minutes
                    if recent_decision.symbol not in recent_decisions:
                        recent_decisions[recent_decision.symbol] = []
                    recent_decisions[recent_decision.symbol].append(recent_decision)
        
        # Check for conflicts
        for symbol, decisions in recent_decisions.items():
            if len(decisions) > 1:
                buy_decisions = [d for d in decisions if d.action == 'BUY']
                sell_decisions = [d for d in decisions if d.action == 'SELL']
                
                if buy_decisions and sell_decisions:
                    self.coordination_stats['coordination_events'] += 1
                    self.logger.info(f"🤝 Coordination event detected for {symbol}: {len(buy_decisions)} BUY vs {len(sell_decisions)} SELL decisions")
    
    async def _perform_risk_checks(self):
        """Perform global risk management checks"""
        try:
            total_exposure = Decimal('0')
            daily_pnl = Decimal('0')
            
            for agent_id, portfolio in self.trading_env.portfolios.items():
                if portfolio:
                    total_exposure += portfolio.total_value
                    daily_pnl += self.agent_performance[agent_id].daily_pnl
            
            # Check global exposure limit
            if total_exposure > self.global_risk_limits['max_total_exposure']:
                self.coordination_stats['risk_violations'] += 1
                self.logger.warning(f"⚠️ Global exposure ${total_exposure} exceeds limit ${self.global_risk_limits['max_total_exposure']}")
            
            # Check daily loss limit
            if daily_pnl < -self.global_risk_limits['max_daily_loss']:
                self.coordination_stats['risk_violations'] += 1
                self.logger.warning(f"⚠️ Daily loss ${abs(daily_pnl)} exceeds limit ${self.global_risk_limits['max_daily_loss']}")
                
                # Pause all agents if daily loss limit exceeded
                for agent_id in self.agents:
                    if self.agent_status[agent_id] == AgentStatus.ACTIVE:
                        self.agent_status[agent_id] = AgentStatus.PAUSED
                        self.logger.warning(f"⏸️ Agent {agent_id} paused due to daily loss limit")
            
        except Exception as e:
            self.logger.error(f"Error in risk checks: {e}")
    
    async def _performance_tracking_loop(self):
        """Track and update agent performance metrics"""
        while self.running:
            try:
                await self._update_performance_metrics()
                await asyncio.sleep(60)  # Update every minute
            except Exception as e:
                self.logger.error(f"Error in performance tracking: {e}")
                await asyncio.sleep(60)
    
    async def _update_performance_metrics(self):
        """Update performance metrics for all agents"""
        for agent_id, portfolio in self.trading_env.portfolios.items():
            if not portfolio or agent_id not in self.agent_performance:
                continue
            
            performance = self.agent_performance[agent_id]
            
            # Update basic metrics
            performance.total_pnl = portfolio.realized_pnl + portfolio.unrealized_pnl
            performance.realized_pnl = portfolio.realized_pnl
            performance.unrealized_pnl = portfolio.unrealized_pnl
            
            # Calculate win rate
            if performance.total_trades > 0:
                performance.win_rate = performance.winning_trades / performance.total_trades
            
            # Update daily/weekly/monthly P&L (simplified)
            performance.daily_pnl = performance.total_pnl  # Simplified for demo
            performance.weekly_pnl = performance.total_pnl
            performance.monthly_pnl = performance.total_pnl
    
    def get_coordination_statistics(self) -> Dict[str, Any]:
        """Get coordination system statistics"""
        active_agents = sum(1 for status in self.agent_status.values() if status == AgentStatus.ACTIVE)
        
        return {
            'total_agents': len(self.agents),
            'active_agents': active_agents,
            'total_decisions': self.coordination_stats['total_decisions'],
            'executed_orders': self.coordination_stats['executed_orders'],
            'rejected_decisions': self.coordination_stats['rejected_decisions'],
            'risk_violations': self.coordination_stats['risk_violations'],
            'coordination_events': self.coordination_stats['coordination_events'],
            'decision_execution_rate': (self.coordination_stats['executed_orders'] / max(1, self.coordination_stats['total_decisions'])) * 100
        }
    
    def get_agent_summary(self) -> List[Dict[str, Any]]:
        """Get summary of all agents"""
        summary = []
        
        for agent_id, agent_config in self.agents.items():
            performance = self.agent_performance[agent_id]
            portfolio = self.trading_env.portfolios.get(agent_id)
            
            agent_summary = {
                'agent_id': agent_id,
                'name': agent_config.name,
                'type': agent_config.agent_type.value,
                'status': self.agent_status[agent_id].value,
                'total_trades': performance.total_trades,
                'win_rate': performance.win_rate,
                'total_pnl': float(performance.total_pnl),
                'daily_pnl': float(performance.daily_pnl),
                'active_orders': len(self.active_orders[agent_id]),
                'portfolio_value': float(portfolio.total_value) if portfolio else 0,
                'cash_balance': float(portfolio.cash_balance) if portfolio else 0,
                'recent_decisions': len(self.agent_decisions[agent_id])
            }
            
            summary.append(agent_summary)
        
        return summary