#!/usr/bin/env python3
"""
Noryon V2 - Realistic Market Data Feed
Simulates real-world market data feeds with realistic price movements, volatility, and market events

This module provides:
- Real-time price feeds with realistic movements
- Market volatility simulation
- News event impact simulation
- Order book depth simulation
- Market hours and session management
- Historical data replay capabilities
"""

import asyncio
import json
import logging
import random
import time
import math
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Callable
from decimal import Decimal, ROUND_HALF_UP
from dataclasses import dataclass, field
from enum import Enum

from src.core.config import Config
from src.core.logger import get_logger
from src.services.realistic_trading_environment import (
    MarketTick, OrderBook, OrderBookLevel, MarketCondition
)

logger = get_logger(__name__)

class NewsImpact(Enum):
    VERY_BULLISH = "very_bullish"
    BULLISH = "bullish"
    NEUTRAL = "neutral"
    BEARISH = "bearish"
    VERY_BEARISH = "very_bearish"

@dataclass
class NewsEvent:
    timestamp: datetime
    symbol: str
    impact: NewsImpact
    magnitude: float  # 0.0 to 1.0
    duration_minutes: int
    description: str

@dataclass
class MarketSession:
    name: str
    start_hour: int
    end_hour: int
    timezone_offset: int
    volatility_multiplier: float
    volume_multiplier: float

@dataclass
class PriceMovement:
    symbol: str
    timestamp: datetime
    price: Decimal
    volume: Decimal
    volatility: float
    trend_strength: float
    market_condition: MarketCondition

class RealisticMarketFeed:
    """
    Realistic market data feed that simulates real-world trading conditions
    """
    
    def __init__(self, config: Config, trading_env):
        self.config = config
        self.trading_env = trading_env
        self.logger = get_logger(__name__)
        
        # Market sessions (simplified global trading)
        self.market_sessions = {
            "asian": MarketSession("Asian", 0, 8, 8, 0.8, 0.7),
            "european": MarketSession("European", 8, 16, 0, 1.2, 1.1),
            "american": MarketSession("American", 16, 24, -5, 1.0, 1.0)
        }
        
        # Price movement parameters
        self.base_volatilities = {
            "BTC/USDT": 0.02,   # 2% daily volatility
            "ETH/USDT": 0.025,  # 2.5%
            "BNB/USDT": 0.03,   # 3%
            "ADA/USDT": 0.04,   # 4%
            "SOL/USDT": 0.045,  # 4.5%
            "XRP/USDT": 0.035,  # 3.5%
            "DOT/USDT": 0.04,   # 4%
            "AVAX/USDT": 0.05,  # 5%
            "MATIC/USDT": 0.045, # 4.5%
            "LINK/USDT": 0.04   # 4%
        }
        
        # Trend parameters
        self.trend_persistence = 0.7  # How likely trends continue
        self.trend_reversal_probability = 0.05  # Chance of trend reversal
        self.mean_reversion_strength = 0.1  # Pull back to moving average
        
        # News event simulation
        self.news_events = []
        self.active_news_impacts = {}
        
        # Market microstructure
        self.tick_sizes = {
            "BTC/USDT": Decimal('0.01'),
            "ETH/USDT": Decimal('0.01'),
            "BNB/USDT": Decimal('0.01'),
            "ADA/USDT": Decimal('0.0001'),
            "SOL/USDT": Decimal('0.01'),
            "XRP/USDT": Decimal('0.0001'),
            "DOT/USDT": Decimal('0.001'),
            "AVAX/USDT": Decimal('0.01'),
            "MATIC/USDT": Decimal('0.0001'),
            "LINK/USDT": Decimal('0.001')
        }
        
        # Moving averages for mean reversion
        self.moving_averages = {symbol: [] for symbol in self.trading_env.symbols}
        self.ma_period = 20  # 20-tick moving average
        
        # Market data subscribers
        self.subscribers = []
        
        # Running state
        self.running = False
        self.feed_task = None
        self.news_task = None
        
        # Performance tracking
        self.ticks_generated = 0
        self.last_performance_log = time.time()
    
    async def start(self):
        """Start the market data feed"""
        if self.running:
            return
        
        self.running = True
        
        # Initialize moving averages
        for symbol in self.trading_env.symbols:
            current_price = self.trading_env.current_prices[symbol]
            self.moving_averages[symbol] = [current_price] * self.ma_period
        
        # Start feed tasks
        self.feed_task = asyncio.create_task(self._generate_market_data())
        self.news_task = asyncio.create_task(self._generate_news_events())
        
        self.logger.info("📈 Realistic Market Feed started")
    
    async def stop(self):
        """Stop the market data feed"""
        self.running = False
        
        if self.feed_task:
            self.feed_task.cancel()
        if self.news_task:
            self.news_task.cancel()
        
        try:
            if self.feed_task:
                await self.feed_task
            if self.news_task:
                await self.news_task
        except asyncio.CancelledError:
            pass
        
        self.logger.info("🛑 Realistic Market Feed stopped")
    
    def subscribe(self, callback: Callable[[MarketTick], None]):
        """Subscribe to market data updates"""
        self.subscribers.append(callback)
    
    def unsubscribe(self, callback: Callable[[MarketTick], None]):
        """Unsubscribe from market data updates"""
        if callback in self.subscribers:
            self.subscribers.remove(callback)
    
    async def _generate_market_data(self):
        """Main market data generation loop"""
        while self.running:
            try:
                # Generate ticks for all symbols
                for symbol in self.trading_env.symbols:
                    tick = await self._generate_tick(symbol)
                    if tick:
                        # Update trading environment
                        self.trading_env.current_prices[symbol] = tick.price
                        self.trading_env.market_ticks[symbol].append(tick)
                        
                        # Keep only recent ticks (last 1000)
                        if len(self.trading_env.market_ticks[symbol]) > 1000:
                            self.trading_env.market_ticks[symbol] = self.trading_env.market_ticks[symbol][-1000:]
                        
                        # Update order book
                        await self._update_order_book(symbol, tick)
                        
                        # Notify subscribers
                        for callback in self.subscribers:
                            try:
                                callback(tick)
                            except Exception as e:
                                self.logger.error(f"Error in market data callback: {e}")
                        
                        self.ticks_generated += 1
                
                # Log performance periodically
                if time.time() - self.last_performance_log > 60:  # Every minute
                    self._log_performance()
                    self.last_performance_log = time.time()
                
                # Control tick frequency (10 ticks per second)
                await asyncio.sleep(0.1)
                
            except Exception as e:
                self.logger.error(f"Error generating market data: {e}")
                await asyncio.sleep(1.0)
    
    async def _generate_tick(self, symbol: str) -> Optional[MarketTick]:
        """Generate a realistic price tick for a symbol"""
        try:
            current_price = self.trading_env.current_prices[symbol]
            base_volatility = self.base_volatilities[symbol]
            
            # Get current market session
            session = self._get_current_session()
            session_volatility = base_volatility * session.volatility_multiplier
            
            # Apply news impact
            news_impact = self._get_news_impact(symbol)
            total_volatility = session_volatility * (1 + news_impact)
            
            # Calculate price movement
            price_change = self._calculate_price_movement(symbol, current_price, total_volatility)
            new_price = current_price + price_change
            
            # Ensure minimum tick size
            tick_size = self.tick_sizes[symbol]
            new_price = (new_price / tick_size).quantize(Decimal('1'), rounding=ROUND_HALF_UP) * tick_size
            
            # Generate volume (realistic volume patterns)
            base_volume = Decimal(str(random.uniform(0.1, 2.0)))
            volume_multiplier = session.volume_multiplier * (1 + abs(news_impact))
            volume = base_volume * Decimal(str(volume_multiplier))
            
            # Update moving average
            self.moving_averages[symbol].append(new_price)
            if len(self.moving_averages[symbol]) > self.ma_period:
                self.moving_averages[symbol].pop(0)
            
            # Create tick
            tick = MarketTick(
                symbol=symbol,
                timestamp=datetime.now(timezone.utc),
                price=new_price,
                volume=volume,
                bid=new_price - tick_size,
                ask=new_price + tick_size,
                high_24h=new_price,  # Simplified
                low_24h=new_price,   # Simplified
                change_24h=Decimal('0')  # Simplified
            )
            
            return tick
            
        except Exception as e:
            self.logger.error(f"Error generating tick for {symbol}: {e}")
            return None
    
    def _calculate_price_movement(self, symbol: str, current_price: Decimal, volatility: float) -> Decimal:
        """Calculate realistic price movement using multiple factors"""
        
        # Random walk component
        random_component = random.gauss(0, volatility / math.sqrt(24 * 60 * 6))  # 10-second intervals
        
        # Trend component
        trend_component = self._get_trend_component(symbol)
        
        # Mean reversion component
        mean_reversion_component = self._get_mean_reversion_component(symbol, current_price)
        
        # Combine components
        total_change_pct = random_component + trend_component + mean_reversion_component
        
        # Apply to price
        price_change = current_price * Decimal(str(total_change_pct))
        
        return price_change
    
    def _get_trend_component(self, symbol: str) -> float:
        """Calculate trend-following component"""
        if symbol not in self.trading_env.trend_directions:
            return 0.0
        
        trend_direction = self.trading_env.trend_directions[symbol]
        trend_strength = self.trading_env.trend_strengths[symbol]
        
        # Trend persistence with some randomness
        if random.random() < self.trend_persistence:
            return trend_direction * trend_strength * 0.0001  # Small trend component
        else:
            return 0.0
    
    def _get_mean_reversion_component(self, symbol: str, current_price: Decimal) -> float:
        """Calculate mean reversion component"""
        if len(self.moving_averages[symbol]) < self.ma_period:
            return 0.0
        
        ma = sum(self.moving_averages[symbol]) / len(self.moving_averages[symbol])
        deviation = (current_price - ma) / ma
        
        # Mean reversion force
        return -float(deviation) * self.mean_reversion_strength * 0.1
    
    def _get_current_session(self) -> MarketSession:
        """Get current market session based on time"""
        current_hour = datetime.now(timezone.utc).hour
        
        for session in self.market_sessions.values():
            if session.start_hour <= current_hour < session.end_hour:
                return session
        
        # Default to American session
        return self.market_sessions["american"]
    
    def _get_news_impact(self, symbol: str) -> float:
        """Get current news impact for a symbol"""
        if symbol not in self.active_news_impacts:
            return 0.0
        
        impact_data = self.active_news_impacts[symbol]
        
        # Check if impact has expired
        if datetime.now(timezone.utc) > impact_data['expires']:
            del self.active_news_impacts[symbol]
            return 0.0
        
        return impact_data['magnitude']
    
    async def _update_order_book(self, symbol: str, tick: MarketTick):
        """Update order book based on new tick"""
        try:
            order_book = self.trading_env.order_books[symbol]
            tick_size = self.tick_sizes[symbol]
            
            # Generate realistic bid/ask levels
            spread = tick_size * random.randint(1, 3)  # 1-3 tick spread
            mid_price = tick.price
            
            # Clear and rebuild order book
            order_book.bids.clear()
            order_book.asks.clear()
            
            # Generate bid levels
            for i in range(5):  # 5 levels deep
                price = mid_price - spread/2 - (tick_size * i)
                quantity = Decimal(str(random.uniform(0.1, 5.0))) * (1 + i * 0.2)
                order_book.bids.append(OrderBookLevel(price=price, quantity=quantity))
            
            # Generate ask levels
            for i in range(5):  # 5 levels deep
                price = mid_price + spread/2 + (tick_size * i)
                quantity = Decimal(str(random.uniform(0.1, 5.0))) * (1 + i * 0.2)
                order_book.asks.append(OrderBookLevel(price=price, quantity=quantity))
            
            order_book.timestamp = tick.timestamp
            
        except Exception as e:
            self.logger.error(f"Error updating order book for {symbol}: {e}")
    
    async def _generate_news_events(self):
        """Generate random news events that impact prices"""
        while self.running:
            try:
                # Generate news event every 5-30 minutes
                wait_time = random.uniform(300, 1800)  # 5-30 minutes
                await asyncio.sleep(wait_time)
                
                if not self.running:
                    break
                
                # Select random symbol
                symbol = random.choice(self.trading_env.symbols)
                
                # Generate news event
                impact = random.choice(list(NewsImpact))
                magnitude = random.uniform(0.1, 0.8)  # 10-80% impact
                duration = random.randint(5, 60)  # 5-60 minutes
                
                # Create news event
                news_event = NewsEvent(
                    timestamp=datetime.now(timezone.utc),
                    symbol=symbol,
                    impact=impact,
                    magnitude=magnitude,
                    duration_minutes=duration,
                    description=self._generate_news_description(symbol, impact)
                )
                
                # Apply impact
                impact_multiplier = 1.0
                if impact == NewsImpact.VERY_BULLISH:
                    impact_multiplier = magnitude
                elif impact == NewsImpact.BULLISH:
                    impact_multiplier = magnitude * 0.5
                elif impact == NewsImpact.BEARISH:
                    impact_multiplier = -magnitude * 0.5
                elif impact == NewsImpact.VERY_BEARISH:
                    impact_multiplier = -magnitude
                
                self.active_news_impacts[symbol] = {
                    'magnitude': impact_multiplier,
                    'expires': datetime.now(timezone.utc) + timedelta(minutes=duration)
                }
                
                self.news_events.append(news_event)
                
                self.logger.info(f"📰 News Event: {symbol} - {impact.value} (magnitude: {magnitude:.2f}, duration: {duration}m)")
                self.logger.info(f"📰 Description: {news_event.description}")
                
            except Exception as e:
                self.logger.error(f"Error generating news events: {e}")
                await asyncio.sleep(60)
    
    def _generate_news_description(self, symbol: str, impact: NewsImpact) -> str:
        """Generate realistic news descriptions"""
        base_symbol = symbol.split('/')[0]
        
        positive_news = [
            f"{base_symbol} announces major partnership with tech giant",
            f"Institutional adoption of {base_symbol} reaches new highs",
            f"{base_symbol} network upgrade shows significant performance improvements",
            f"Major exchange lists {base_symbol} with high trading volume",
            f"Regulatory clarity boosts {base_symbol} investor confidence"
        ]
        
        negative_news = [
            f"Security concerns raised about {base_symbol} network",
            f"Regulatory uncertainty impacts {base_symbol} trading",
            f"Major {base_symbol} holder announces large sell-off",
            f"Technical issues reported on {base_symbol} blockchain",
            f"Market manipulation allegations affect {base_symbol} price"
        ]
        
        neutral_news = [
            f"{base_symbol} development team releases routine update",
            f"Market analysis shows mixed signals for {base_symbol}",
            f"{base_symbol} trading volume remains stable",
            f"Community discussion around {base_symbol} future roadmap"
        ]
        
        if impact in [NewsImpact.VERY_BULLISH, NewsImpact.BULLISH]:
            return random.choice(positive_news)
        elif impact in [NewsImpact.VERY_BEARISH, NewsImpact.BEARISH]:
            return random.choice(negative_news)
        else:
            return random.choice(neutral_news)
    
    def _log_performance(self):
        """Log performance metrics"""
        ticks_per_minute = self.ticks_generated / max(1, (time.time() - self.last_performance_log) / 60)
        
        self.logger.info(f"📊 Market Feed Performance: {ticks_per_minute:.1f} ticks/min, {len(self.active_news_impacts)} active news impacts")
        
        # Reset counter
        self.ticks_generated = 0
    
    def get_market_statistics(self) -> Dict[str, Any]:
        """Get current market statistics"""
        stats = {
            'active_symbols': len(self.trading_env.symbols),
            'active_news_impacts': len(self.active_news_impacts),
            'total_news_events': len(self.news_events),
            'current_session': self._get_current_session().name,
            'price_summary': {}
        }
        
        # Add price summary for each symbol
        for symbol in self.trading_env.symbols:
            current_price = self.trading_env.current_prices[symbol]
            volatility = self.trading_env.volatility_levels[symbol]
            
            stats['price_summary'][symbol] = {
                'current_price': float(current_price),
                'volatility': volatility,
                'trend_direction': self.trading_env.trend_directions.get(symbol, 0),
                'trend_strength': self.trading_env.trend_strengths.get(symbol, 0),
                'has_news_impact': symbol in self.active_news_impacts
            }
        
        return stats
    
    def get_recent_news(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get recent news events"""
        recent_events = sorted(self.news_events, key=lambda x: x.timestamp, reverse=True)[:limit]
        
        return [{
            'timestamp': event.timestamp.isoformat(),
            'symbol': event.symbol,
            'impact': event.impact.value,
            'magnitude': event.magnitude,
            'duration_minutes': event.duration_minutes,
            'description': event.description
        } for event in recent_events]