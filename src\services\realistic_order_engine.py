#!/usr/bin/env python3
"""
Noryon V2 - Realistic Order Execution Engine
Simulates real-world order execution with latency, slippage, and market impact

This module handles:
- Order validation and risk checks
- Realistic order execution with slippage
- Market impact simulation
- Order book updates
- Trade reporting and settlement
- Compliance and regulatory checks
"""

import asyncio
import json
import logging
import random
import time
import uuid
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional, Tuple
from decimal import Decimal, ROUND_HALF_UP
from dataclasses import dataclass, field
from enum import Enum

from src.core.config import Config
from src.core.logger import get_logger
from src.services.realistic_trading_environment import (
    Order, Trade, OrderType, OrderSide, OrderStatus, 
    Portfolio, Position, MarketTick, OrderBook
)

logger = get_logger(__name__)

class RejectionReason(Enum):
    INSUFFICIENT_FUNDS = "insufficient_funds"
    INVALID_PRICE = "invalid_price"
    INVALID_QUANTITY = "invalid_quantity"
    MARKET_CLOSED = "market_closed"
    SYMBOL_NOT_FOUND = "symbol_not_found"
    RISK_LIMIT_EXCEEDED = "risk_limit_exceeded"
    POSITION_LIMIT_EXCEEDED = "position_limit_exceeded"
    COMPLIANCE_VIOLATION = "compliance_violation"
    SYSTEM_ERROR = "system_error"

@dataclass
class OrderValidationResult:
    valid: bool
    rejection_reason: Optional[RejectionReason] = None
    message: str = ""

@dataclass
class ExecutionResult:
    success: bool
    trades: List[Trade] = field(default_factory=list)
    remaining_quantity: Decimal = Decimal('0')
    avg_price: Optional[Decimal] = None
    total_commission: Decimal = Decimal('0')
    slippage: Decimal = Decimal('0')
    market_impact: Decimal = Decimal('0')
    execution_time_ms: float = 0.0

class RealisticOrderEngine:
    """
    Realistic order execution engine that simulates real-world trading conditions
    """
    
    def __init__(self, config: Config, trading_env):
        self.config = config
        self.trading_env = trading_env
        self.logger = get_logger(__name__)
        
        # Order processing queue
        self.order_queue = asyncio.Queue()
        self.processing_orders = {}
        
        # Risk limits
        self.max_order_value = Decimal('50000')  # $50k max order
        self.max_position_size = Decimal('100000')  # $100k max position
        self.max_daily_volume = Decimal('500000')  # $500k daily volume limit
        
        # Market impact parameters
        self.impact_factor = 0.0001  # 0.01% impact per $10k order
        self.liquidity_levels = {
            "BTC/USDT": Decimal('1000000'),  # High liquidity
            "ETH/USDT": Decimal('500000'),
            "BNB/USDT": Decimal('200000'),
            "ADA/USDT": Decimal('100000'),
            "SOL/USDT": Decimal('150000'),
            "XRP/USDT": Decimal('300000'),
            "DOT/USDT": Decimal('80000'),
            "AVAX/USDT": Decimal('120000'),
            "MATIC/USDT": Decimal('90000'),
            "LINK/USDT": Decimal('110000')
        }
        
        # Execution statistics
        self.execution_stats = {
            'total_orders': 0,
            'filled_orders': 0,
            'rejected_orders': 0,
            'avg_execution_time': 0.0,
            'total_slippage': Decimal('0'),
            'total_commission': Decimal('0')
        }
        
        # Running state
        self.running = False
        self.processing_task = None
    
    async def start(self):
        """Start the order processing engine"""
        if self.running:
            return
        
        self.running = True
        self.processing_task = asyncio.create_task(self._process_orders())
        self.logger.info("🔧 Realistic Order Engine started")
    
    async def stop(self):
        """Stop the order processing engine"""
        self.running = False
        if self.processing_task:
            self.processing_task.cancel()
            try:
                await self.processing_task
            except asyncio.CancelledError:
                pass
        self.logger.info("🛑 Realistic Order Engine stopped")
    
    async def submit_order(self, order: Order) -> str:
        """Submit an order for processing"""
        order.id = str(uuid.uuid4())
        order.timestamp = datetime.now(timezone.utc)
        order.status = OrderStatus.PENDING
        
        # Add to processing queue
        await self.order_queue.put(order)
        self.trading_env.orders[order.id] = order
        
        self.logger.info(f"📝 Order submitted: {order.id} - {order.side.value} {order.quantity} {order.symbol}")
        return order.id
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an existing order"""
        if order_id not in self.trading_env.orders:
            return False
        
        order = self.trading_env.orders[order_id]
        if order.status in [OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED]:
            return False
        
        order.status = OrderStatus.CANCELLED
        self.logger.info(f"❌ Order cancelled: {order_id}")
        return True
    
    async def _process_orders(self):
        """Main order processing loop"""
        while self.running:
            try:
                # Get next order from queue (with timeout)
                try:
                    order = await asyncio.wait_for(self.order_queue.get(), timeout=1.0)
                except asyncio.TimeoutError:
                    continue
                
                # Simulate order processing latency
                latency_ms = random.uniform(*self.trading_env.order_latency_ms)
                await asyncio.sleep(latency_ms / 1000)
                
                # Process the order
                await self._execute_order(order)
                
            except Exception as e:
                self.logger.error(f"Error processing orders: {e}")
                await asyncio.sleep(0.1)
    
    async def _execute_order(self, order: Order):
        """Execute a single order with realistic simulation"""
        start_time = time.time()
        
        try:
            # Validate order
            validation = await self._validate_order(order)
            if not validation.valid:
                order.status = OrderStatus.REJECTED
                self.execution_stats['rejected_orders'] += 1
                self.logger.warning(f"❌ Order rejected: {order.id} - {validation.message}")
                return
            
            # Execute based on order type
            if order.type == OrderType.MARKET:
                result = await self._execute_market_order(order)
            elif order.type == OrderType.LIMIT:
                result = await self._execute_limit_order(order)
            elif order.type == OrderType.STOP:
                result = await self._execute_stop_order(order)
            else:
                order.status = OrderStatus.REJECTED
                self.logger.warning(f"❌ Unsupported order type: {order.type}")
                return
            
            # Update order status and statistics
            if result.success:
                if result.remaining_quantity == Decimal('0'):
                    order.status = OrderStatus.FILLED
                else:
                    order.status = OrderStatus.PARTIALLY_FILLED
                
                order.filled_quantity = order.quantity - result.remaining_quantity
                order.avg_fill_price = result.avg_price
                order.commission = result.total_commission
                
                # Update portfolio
                await self._update_portfolio(order, result.trades)
                
                # Store trades
                self.trading_env.trades.extend(result.trades)
                
                self.execution_stats['filled_orders'] += 1
                self.logger.info(f"✅ Order executed: {order.id} - Filled {order.filled_quantity}/{order.quantity} @ {result.avg_price}")
            
            # Update execution statistics
            execution_time = (time.time() - start_time) * 1000
            self.execution_stats['total_orders'] += 1
            self.execution_stats['avg_execution_time'] = (
                (self.execution_stats['avg_execution_time'] * (self.execution_stats['total_orders'] - 1) + execution_time) /
                self.execution_stats['total_orders']
            )
            
        except Exception as e:
            order.status = OrderStatus.REJECTED
            self.logger.error(f"❌ Error executing order {order.id}: {e}")
    
    async def _validate_order(self, order: Order) -> OrderValidationResult:
        """Comprehensive order validation"""
        
        # Check symbol exists
        if order.symbol not in self.trading_env.symbols:
            return OrderValidationResult(
                valid=False,
                rejection_reason=RejectionReason.SYMBOL_NOT_FOUND,
                message=f"Symbol {order.symbol} not found"
            )
        
        # Check quantity
        if order.quantity <= Decimal('0'):
            return OrderValidationResult(
                valid=False,
                rejection_reason=RejectionReason.INVALID_QUANTITY,
                message="Quantity must be positive"
            )
        
        # Check minimum order size
        current_price = self.trading_env.current_prices[order.symbol]
        order_value = order.quantity * current_price
        
        if order_value < self.config.MIN_TRADE_AMOUNT:
            return OrderValidationResult(
                valid=False,
                rejection_reason=RejectionReason.INVALID_QUANTITY,
                message=f"Order value ${order_value} below minimum ${self.config.MIN_TRADE_AMOUNT}"
            )
        
        # Check maximum order value
        if order_value > self.max_order_value:
            return OrderValidationResult(
                valid=False,
                rejection_reason=RejectionReason.RISK_LIMIT_EXCEEDED,
                message=f"Order value ${order_value} exceeds maximum ${self.max_order_value}"
            )
        
        # Check portfolio balance for buy orders
        if order.side == OrderSide.BUY:
            portfolio = self.trading_env.portfolios.get(order.agent_id)
            if portfolio and portfolio.cash_balance < order_value:
                return OrderValidationResult(
                    valid=False,
                    rejection_reason=RejectionReason.INSUFFICIENT_FUNDS,
                    message=f"Insufficient funds: ${portfolio.cash_balance} < ${order_value}"
                )
        
        # Check position limits for sell orders
        if order.side == OrderSide.SELL:
            portfolio = self.trading_env.portfolios.get(order.agent_id)
            if portfolio:
                position = portfolio.positions.get(order.symbol)
                if not position or position.quantity < order.quantity:
                    return OrderValidationResult(
                        valid=False,
                        rejection_reason=RejectionReason.INSUFFICIENT_FUNDS,
                        message=f"Insufficient position: {position.quantity if position else 0} < {order.quantity}"
                    )
        
        return OrderValidationResult(valid=True)
    
    async def _execute_market_order(self, order: Order) -> ExecutionResult:
        """Execute market order with realistic slippage and market impact"""
        symbol = order.symbol
        current_price = self.trading_env.current_prices[symbol]
        order_book = self.trading_env.order_books[symbol]
        
        # Calculate market impact
        order_value = order.quantity * current_price
        liquidity = self.liquidity_levels.get(symbol, Decimal('100000'))
        market_impact = (order_value / liquidity) * Decimal(str(self.impact_factor))
        
        # Calculate slippage based on order size and market conditions
        base_slippage = Decimal('0.0005')  # 0.05% base slippage
        size_slippage = (order_value / Decimal('10000')) * Decimal('0.0001')  # 0.01% per $10k
        volatility_slippage = Decimal(str(self.trading_env.volatility_levels[symbol])) * Decimal('0.1')
        
        total_slippage = base_slippage + size_slippage + volatility_slippage + market_impact
        
        # Apply slippage to execution price
        if order.side == OrderSide.BUY:
            execution_price = current_price * (Decimal('1') + total_slippage)
        else:
            execution_price = current_price * (Decimal('1') - total_slippage)
        
        # Calculate commission
        commission = order.quantity * execution_price * self.config.commission_rate
        
        # Create trade
        trade = Trade(
            id=str(uuid.uuid4()),
            symbol=symbol,
            timestamp=datetime.now(timezone.utc),
            side=order.side,
            price=execution_price,
            quantity=order.quantity,
            commission=commission,
            order_id=order.id
        )
        
        # Update execution statistics
        self.execution_stats['total_slippage'] += total_slippage
        self.execution_stats['total_commission'] += commission
        
        return ExecutionResult(
            success=True,
            trades=[trade],
            remaining_quantity=Decimal('0'),
            avg_price=execution_price,
            total_commission=commission,
            slippage=total_slippage,
            market_impact=market_impact
        )
    
    async def _execute_limit_order(self, order: Order) -> ExecutionResult:
        """Execute limit order (simplified - immediate fill if price is favorable)"""
        symbol = order.symbol
        current_price = self.trading_env.current_prices[symbol]
        
        # Check if limit order can be filled immediately
        can_fill = False
        if order.side == OrderSide.BUY and order.price >= current_price:
            can_fill = True
        elif order.side == OrderSide.SELL and order.price <= current_price:
            can_fill = True
        
        if can_fill:
            # Fill at limit price (no slippage for limit orders)
            commission = order.quantity * order.price * self.config.commission_rate
            
            trade = Trade(
                id=str(uuid.uuid4()),
                symbol=symbol,
                timestamp=datetime.now(timezone.utc),
                side=order.side,
                price=order.price,
                quantity=order.quantity,
                commission=commission,
                order_id=order.id
            )
            
            return ExecutionResult(
                success=True,
                trades=[trade],
                remaining_quantity=Decimal('0'),
                avg_price=order.price,
                total_commission=commission
            )
        else:
            # Order remains open (simplified - in reality would be added to order book)
            order.status = OrderStatus.OPEN
            return ExecutionResult(success=False, remaining_quantity=order.quantity)
    
    async def _execute_stop_order(self, order: Order) -> ExecutionResult:
        """Execute stop order (simplified implementation)"""
        symbol = order.symbol
        current_price = self.trading_env.current_prices[symbol]
        
        # Check if stop is triggered
        triggered = False
        if order.side == OrderSide.BUY and current_price >= order.stop_price:
            triggered = True
        elif order.side == OrderSide.SELL and current_price <= order.stop_price:
            triggered = True
        
        if triggered:
            # Convert to market order
            order.type = OrderType.MARKET
            return await self._execute_market_order(order)
        else:
            # Order remains open
            order.status = OrderStatus.OPEN
            return ExecutionResult(success=False, remaining_quantity=order.quantity)
    
    async def _update_portfolio(self, order: Order, trades: List[Trade]):
        """Update portfolio based on executed trades"""
        if not order.agent_id or order.agent_id not in self.trading_env.portfolios:
            return
        
        portfolio = self.trading_env.portfolios[order.agent_id]
        
        for trade in trades:
            if trade.side == OrderSide.BUY:
                # Add position
                if trade.symbol in portfolio.positions:
                    position = portfolio.positions[trade.symbol]
                    total_quantity = position.quantity + trade.quantity
                    total_cost = (position.quantity * position.avg_price) + (trade.quantity * trade.price)
                    position.avg_price = total_cost / total_quantity
                    position.quantity = total_quantity
                else:
                    portfolio.positions[trade.symbol] = Position(
                        symbol=trade.symbol,
                        quantity=trade.quantity,
                        avg_price=trade.price,
                        unrealized_pnl=Decimal('0'),
                        realized_pnl=Decimal('0'),
                        timestamp=trade.timestamp
                    )
                
                # Reduce cash
                portfolio.cash_balance -= (trade.quantity * trade.price + trade.commission)
                
            else:  # SELL
                if trade.symbol in portfolio.positions:
                    position = portfolio.positions[trade.symbol]
                    
                    # Calculate realized P&L
                    realized_pnl = (trade.price - position.avg_price) * trade.quantity - trade.commission
                    portfolio.realized_pnl += realized_pnl
                    
                    # Reduce position
                    position.quantity -= trade.quantity
                    if position.quantity <= Decimal('0'):
                        del portfolio.positions[trade.symbol]
                    
                    # Add cash
                    portfolio.cash_balance += (trade.quantity * trade.price - trade.commission)
        
        # Update portfolio timestamp
        portfolio.timestamp = datetime.now(timezone.utc)
        
        # Calculate total portfolio value
        await self._calculate_portfolio_value(portfolio)
    
    async def _calculate_portfolio_value(self, portfolio: Portfolio):
        """Calculate total portfolio value and unrealized P&L"""
        total_value = portfolio.cash_balance
        total_unrealized_pnl = Decimal('0')
        
        for symbol, position in portfolio.positions.items():
            current_price = self.trading_env.current_prices[symbol]
            position_value = position.quantity * current_price
            total_value += position_value
            
            # Calculate unrealized P&L
            unrealized_pnl = (current_price - position.avg_price) * position.quantity
            position.unrealized_pnl = unrealized_pnl
            total_unrealized_pnl += unrealized_pnl
        
        portfolio.total_value = total_value
        portfolio.unrealized_pnl = total_unrealized_pnl
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get current execution statistics"""
        return {
            'total_orders': self.execution_stats['total_orders'],
            'filled_orders': self.execution_stats['filled_orders'],
            'rejected_orders': self.execution_stats['rejected_orders'],
            'fill_rate': (self.execution_stats['filled_orders'] / max(1, self.execution_stats['total_orders'])) * 100,
            'avg_execution_time_ms': self.execution_stats['avg_execution_time'],
            'total_slippage': float(self.execution_stats['total_slippage']),
            'total_commission': float(self.execution_stats['total_commission'])
        }