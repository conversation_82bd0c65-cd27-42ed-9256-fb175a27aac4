#!/usr/bin/env python3
"""
Noryon V2 - Realistic Trading Simulator
Main orchestrator for realistic trading environment simulation

This module provides:
- Complete trading environment orchestration
- Real-time monitoring and dashboard
- Performance analytics and reporting
- System health monitoring
- Configuration management
- Data persistence and logging
"""

import asyncio
import json
import logging
import time
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Any, Optional
from decimal import Decimal
from dataclasses import dataclass, asdict

from src.core.config import Config
from src.core.logger import get_logger
from src.core.database_manager import DatabaseManager
from src.services.realistic_trading_environment import RealisticTradingEnvironment
from src.services.realistic_order_engine import RealisticOrderEngine
from src.services.realistic_market_feed import RealisticMarketFeed
from src.services.realistic_agent_coordinator import RealisticAgentCoordinator

logger = get_logger(__name__)

@dataclass
class SimulationMetrics:
    start_time: datetime
    uptime_seconds: float
    total_ticks_processed: int
    total_orders_executed: int
    total_trades_completed: int
    total_agents_active: int
    total_portfolio_value: Decimal
    total_pnl: Decimal
    system_health_score: float
    memory_usage_mb: float
    cpu_usage_percent: float

@dataclass
class SystemStatus:
    trading_environment: str
    order_engine: str
    market_feed: str
    agent_coordinator: str
    database: str
    overall_status: str
    last_updated: datetime

class RealisticTradingSimulator:
    """
    Main orchestrator for realistic trading environment simulation
    """
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = get_logger(__name__)
        
        # Core components
        self.db_manager = DatabaseManager(config)
        self.trading_env = RealisticTradingEnvironment(config)
        self.order_engine = RealisticOrderEngine(config, self.trading_env)
        self.market_feed = RealisticMarketFeed(config, self.trading_env)
        self.agent_coordinator = RealisticAgentCoordinator(
            config, self.trading_env, self.order_engine, self.market_feed
        )
        
        # Simulation state
        self.running = False
        self.start_time = None
        self.simulation_id = None
        
        # Monitoring tasks
        self.monitoring_task = None
        self.persistence_task = None
        self.health_check_task = None
        
        # Performance metrics
        self.metrics = SimulationMetrics(
            start_time=datetime.now(timezone.utc),
            uptime_seconds=0,
            total_ticks_processed=0,
            total_orders_executed=0,
            total_trades_completed=0,
            total_agents_active=0,
            total_portfolio_value=Decimal('0'),
            total_pnl=Decimal('0'),
            system_health_score=100.0,
            memory_usage_mb=0.0,
            cpu_usage_percent=0.0
        )
        
        # System status
        self.system_status = SystemStatus(
            trading_environment="stopped",
            order_engine="stopped",
            market_feed="stopped",
            agent_coordinator="stopped",
            database="disconnected",
            overall_status="stopped",
            last_updated=datetime.now(timezone.utc)
        )
        
        # Dashboard data
        self.dashboard_data = {
            'real_time_prices': {},
            'agent_performance': {},
            'order_flow': [],
            'market_events': [],
            'system_alerts': [],
            'portfolio_summary': {},
            'risk_metrics': {}
        }
        
        # Configuration for realistic simulation
        self.simulation_config = {
            'market_hours': '24/7',  # Crypto markets
            'tick_frequency': 0.1,   # 10 ticks per second
            'volatility_factor': 1.0,
            'news_frequency': 0.5,   # News events per hour
            'slippage_factor': 1.0,
            'commission_rate': 0.001, # 0.1%
            'max_agents': 20,
            'risk_monitoring': True,
            'real_time_analytics': True
        }
    
    async def initialize(self):
        """Initialize all simulation components"""
        try:
            self.logger.info("🚀 Initializing Realistic Trading Simulator...")
            
            # Initialize database connections
            await self.db_manager.initialize()
            self.system_status.database = "connected"
            
            # Initialize trading environment
            await self.trading_env.initialize()
            self.system_status.trading_environment = "initialized"
            
            # Generate simulation ID
            self.simulation_id = f"sim_{int(time.time())}"
            
            self.logger.info(f"✅ Simulation initialized with ID: {self.simulation_id}")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize simulation: {e}")
            raise
    
    async def start(self):
        """Start the complete trading simulation"""
        if self.running:
            self.logger.warning("Simulation is already running")
            return
        
        try:
            self.logger.info("🎬 Starting Realistic Trading Simulation...")
            self.running = True
            self.start_time = datetime.now(timezone.utc)
            self.metrics.start_time = self.start_time
            
            # Start core components in order
            await self.trading_env.start()
            self.system_status.trading_environment = "running"
            
            await self.order_engine.start()
            self.system_status.order_engine = "running"
            
            await self.market_feed.start()
            self.system_status.market_feed = "running"
            
            await self.agent_coordinator.start()
            self.system_status.agent_coordinator = "running"
            
            # Start monitoring tasks
            self.monitoring_task = asyncio.create_task(self._monitoring_loop())
            self.persistence_task = asyncio.create_task(self._persistence_loop())
            self.health_check_task = asyncio.create_task(self._health_check_loop())
            
            self.system_status.overall_status = "running"
            self.system_status.last_updated = datetime.now(timezone.utc)
            
            # Log startup summary
            await self._log_startup_summary()
            
            self.logger.info("🎯 Realistic Trading Simulation is now LIVE!")
            self.logger.info("📊 Dashboard data will be updated in real-time")
            self.logger.info("🤖 AI agents are actively trading")
            self.logger.info("📈 Market data feed is streaming")
            
        except Exception as e:
            self.logger.error(f"❌ Failed to start simulation: {e}")
            await self.stop()
            raise
    
    async def stop(self):
        """Stop the trading simulation gracefully"""
        if not self.running:
            return
        
        self.logger.info("🛑 Stopping Realistic Trading Simulation...")
        self.running = False
        
        try:
            # Stop monitoring tasks
            if self.monitoring_task:
                self.monitoring_task.cancel()
            if self.persistence_task:
                self.persistence_task.cancel()
            if self.health_check_task:
                self.health_check_task.cancel()
            
            # Stop core components in reverse order
            await self.agent_coordinator.stop()
            self.system_status.agent_coordinator = "stopped"
            
            await self.market_feed.stop()
            self.system_status.market_feed = "stopped"
            
            await self.order_engine.stop()
            self.system_status.order_engine = "stopped"
            
            await self.trading_env.stop()
            self.system_status.trading_environment = "stopped"
            
            # Final data persistence
            await self._save_final_results()
            
            self.system_status.overall_status = "stopped"
            self.system_status.last_updated = datetime.now(timezone.utc)
            
            # Log shutdown summary
            await self._log_shutdown_summary()
            
            self.logger.info("✅ Realistic Trading Simulation stopped successfully")
            
        except Exception as e:
            self.logger.error(f"❌ Error during simulation shutdown: {e}")
    
    async def _monitoring_loop(self):
        """Main monitoring loop for real-time metrics"""
        while self.running:
            try:
                await self._update_metrics()
                await self._update_dashboard_data()
                await self._check_alerts()
                
                # Log periodic status
                if int(time.time()) % 300 == 0:  # Every 5 minutes
                    await self._log_periodic_status()
                
                await asyncio.sleep(5)  # Update every 5 seconds
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                await asyncio.sleep(10)
    
    async def _persistence_loop(self):
        """Data persistence loop"""
        while self.running:
            try:
                await self._persist_simulation_data()
                await asyncio.sleep(60)  # Persist every minute
            except Exception as e:
                self.logger.error(f"Error in persistence loop: {e}")
                await asyncio.sleep(60)
    
    async def _health_check_loop(self):
        """System health monitoring loop"""
        while self.running:
            try:
                await self._perform_health_checks()
                await asyncio.sleep(30)  # Health check every 30 seconds
            except Exception as e:
                self.logger.error(f"Error in health check loop: {e}")
                await asyncio.sleep(30)
    
    async def _update_metrics(self):
        """Update simulation metrics"""
        try:
            # Update uptime
            if self.start_time:
                self.metrics.uptime_seconds = (datetime.now(timezone.utc) - self.start_time).total_seconds()
            
            # Update tick count
            total_ticks = sum(len(ticks) for ticks in self.trading_env.market_ticks.values())
            self.metrics.total_ticks_processed = total_ticks
            
            # Update order and trade counts
            self.metrics.total_orders_executed = len(self.trading_env.orders)
            self.metrics.total_trades_completed = len(self.trading_env.trades)
            
            # Update agent count
            active_agents = sum(1 for status in self.agent_coordinator.agent_status.values() 
                              if status.value == "active")
            self.metrics.total_agents_active = active_agents
            
            # Update portfolio metrics
            total_value = Decimal('0')
            total_pnl = Decimal('0')
            
            for portfolio in self.trading_env.portfolios.values():
                if portfolio:
                    total_value += portfolio.total_value
                    total_pnl += portfolio.realized_pnl + portfolio.unrealized_pnl
            
            self.metrics.total_portfolio_value = total_value
            self.metrics.total_pnl = total_pnl
            
            # Update system resource usage (simplified)
            import psutil
            process = psutil.Process()
            self.metrics.memory_usage_mb = process.memory_info().rss / 1024 / 1024
            self.metrics.cpu_usage_percent = process.cpu_percent()
            
        except Exception as e:
            self.logger.error(f"Error updating metrics: {e}")
    
    async def _update_dashboard_data(self):
        """Update real-time dashboard data"""
        try:
            # Update real-time prices
            self.dashboard_data['real_time_prices'] = {
                symbol: {
                    'price': float(price),
                    'change_24h': 0.0,  # Simplified
                    'volume_24h': 0.0,  # Simplified
                    'timestamp': datetime.now(timezone.utc).isoformat()
                } for symbol, price in self.trading_env.current_prices.items()
            }
            
            # Update agent performance
            self.dashboard_data['agent_performance'] = self.agent_coordinator.get_agent_summary()
            
            # Update recent orders (last 50)
            recent_orders = list(self.trading_env.orders.values())[-50:]
            self.dashboard_data['order_flow'] = [{
                'id': order.id,
                'symbol': order.symbol,
                'side': order.side.value,
                'type': order.type.value,
                'quantity': float(order.quantity),
                'price': float(order.price) if order.price else None,
                'status': order.status.value,
                'agent_id': order.agent_id,
                'timestamp': order.timestamp.isoformat() if order.timestamp else None
            } for order in recent_orders]
            
            # Update market events (recent news)
            if hasattr(self.market_feed, 'get_recent_news'):
                self.dashboard_data['market_events'] = self.market_feed.get_recent_news(limit=20)
            
            # Update portfolio summary
            self.dashboard_data['portfolio_summary'] = {
                'total_value': float(self.metrics.total_portfolio_value),
                'total_pnl': float(self.metrics.total_pnl),
                'active_agents': self.metrics.total_agents_active,
                'total_trades': self.metrics.total_trades_completed,
                'uptime_hours': self.metrics.uptime_seconds / 3600
            }
            
            # Update risk metrics
            self.dashboard_data['risk_metrics'] = {
                'system_health': self.metrics.system_health_score,
                'memory_usage': self.metrics.memory_usage_mb,
                'cpu_usage': self.metrics.cpu_usage_percent,
                'active_orders': len([o for o in self.trading_env.orders.values() 
                                    if o.status.value in ['pending', 'open', 'partially_filled']]),
                'risk_violations': self.agent_coordinator.coordination_stats.get('risk_violations', 0)
            }
            
        except Exception as e:
            self.logger.error(f"Error updating dashboard data: {e}")
    
    async def _check_alerts(self):
        """Check for system alerts and warnings"""
        try:
            alerts = []
            
            # Memory usage alert
            if self.metrics.memory_usage_mb > 1000:  # 1GB
                alerts.append({
                    'type': 'warning',
                    'message': f'High memory usage: {self.metrics.memory_usage_mb:.1f} MB',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })
            
            # CPU usage alert
            if self.metrics.cpu_usage_percent > 80:
                alerts.append({
                    'type': 'warning',
                    'message': f'High CPU usage: {self.metrics.cpu_usage_percent:.1f}%',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })
            
            # Large loss alert
            if self.metrics.total_pnl < -10000:  # $10k loss
                alerts.append({
                    'type': 'critical',
                    'message': f'Large portfolio loss: ${abs(float(self.metrics.total_pnl)):.2f}',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })
            
            # Add new alerts to dashboard
            for alert in alerts:
                if alert not in self.dashboard_data['system_alerts']:
                    self.dashboard_data['system_alerts'].append(alert)
            
            # Keep only recent alerts (last 100)
            if len(self.dashboard_data['system_alerts']) > 100:
                self.dashboard_data['system_alerts'] = self.dashboard_data['system_alerts'][-100:]
            
        except Exception as e:
            self.logger.error(f"Error checking alerts: {e}")
    
    async def _perform_health_checks(self):
        """Perform comprehensive system health checks"""
        try:
            health_score = 100.0
            
            # Check component status
            if self.system_status.trading_environment != "running":
                health_score -= 25
            if self.system_status.order_engine != "running":
                health_score -= 25
            if self.system_status.market_feed != "running":
                health_score -= 25
            if self.system_status.agent_coordinator != "running":
                health_score -= 25
            
            # Check resource usage
            if self.metrics.memory_usage_mb > 2000:  # 2GB
                health_score -= 10
            if self.metrics.cpu_usage_percent > 90:
                health_score -= 10
            
            # Check error rates
            coordination_stats = self.agent_coordinator.get_coordination_statistics()
            if coordination_stats['total_decisions'] > 0:
                error_rate = coordination_stats['rejected_decisions'] / coordination_stats['total_decisions']
                if error_rate > 0.1:  # 10% error rate
                    health_score -= 20
            
            self.metrics.system_health_score = max(0, health_score)
            
        except Exception as e:
            self.logger.error(f"Error in health checks: {e}")
            self.metrics.system_health_score = 50.0  # Degraded health
    
    async def _persist_simulation_data(self):
        """Persist simulation data to database"""
        try:
            # This would normally save to ClickHouse/PostgreSQL
            # For now, we'll just log the data
            
            simulation_snapshot = {
                'simulation_id': self.simulation_id,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'metrics': asdict(self.metrics),
                'system_status': asdict(self.system_status),
                'agent_summary': self.agent_coordinator.get_agent_summary(),
                'market_summary': self.market_feed.get_market_statistics() if hasattr(self.market_feed, 'get_market_statistics') else {},
                'order_summary': self.order_engine.get_execution_statistics()
            }
            
            # In a real implementation, this would be saved to database
            # await self.db_manager.save_simulation_snapshot(simulation_snapshot)
            
        except Exception as e:
            self.logger.error(f"Error persisting simulation data: {e}")
    
    async def _log_startup_summary(self):
        """Log comprehensive startup summary"""
        self.logger.info("" + "="*80)
        self.logger.info("🎯 NORYON V2 REALISTIC TRADING SIMULATION - STARTUP SUMMARY")
        self.logger.info("" + "="*80)
        self.logger.info(f"📅 Start Time: {self.start_time.strftime('%Y-%m-%d %H:%M:%S UTC')}")
        self.logger.info(f"🆔 Simulation ID: {self.simulation_id}")
        self.logger.info(f"🏪 Trading Symbols: {', '.join(self.trading_env.symbols)}")
        self.logger.info(f"🤖 Active Agents: {len(self.agent_coordinator.agents)}")
        
        # Agent summary
        agent_types = {}
        for agent in self.agent_coordinator.agents.values():
            agent_type = agent.agent_type.value
            agent_types[agent_type] = agent_types.get(agent_type, 0) + 1
        
        self.logger.info("🤖 Agent Distribution:")
        for agent_type, count in agent_types.items():
            self.logger.info(f"   • {agent_type}: {count}")
        
        # Initial portfolio values
        total_initial_capital = sum(float(p.cash_balance) for p in self.trading_env.portfolios.values() if p)
        self.logger.info(f"💰 Total Initial Capital: ${total_initial_capital:,.2f}")
        
        # System configuration
        self.logger.info("⚙️ Configuration:")
        self.logger.info(f"   • Market Hours: {self.simulation_config['market_hours']}")
        self.logger.info(f"   • Tick Frequency: {1/self.simulation_config['tick_frequency']:.1f} ticks/second")
        self.logger.info(f"   • Commission Rate: {self.simulation_config['commission_rate']*100:.2f}%")
        self.logger.info(f"   • Risk Monitoring: {'Enabled' if self.simulation_config['risk_monitoring'] else 'Disabled'}")
        
        self.logger.info("" + "="*80)
        self.logger.info("🚀 SIMULATION IS NOW LIVE - AI AGENTS ARE TRADING!")
        self.logger.info("" + "="*80)
    
    async def _log_periodic_status(self):
        """Log periodic status update"""
        uptime_hours = self.metrics.uptime_seconds / 3600
        
        self.logger.info("" + "-"*60)
        self.logger.info(f"📊 SIMULATION STATUS - Uptime: {uptime_hours:.1f}h")
        self.logger.info(f"💹 Market Ticks: {self.metrics.total_ticks_processed:,}")
        self.logger.info(f"📋 Orders: {self.metrics.total_orders_executed:,} | Trades: {self.metrics.total_trades_completed:,}")
        self.logger.info(f"🤖 Active Agents: {self.metrics.total_agents_active}")
        self.logger.info(f"💰 Total Portfolio: ${float(self.metrics.total_portfolio_value):,.2f}")
        self.logger.info(f"📈 Total P&L: ${float(self.metrics.total_pnl):+,.2f}")
        self.logger.info(f"🏥 Health Score: {self.metrics.system_health_score:.1f}%")
        self.logger.info(f"💾 Memory: {self.metrics.memory_usage_mb:.1f}MB | CPU: {self.metrics.cpu_usage_percent:.1f}%")
        self.logger.info("" + "-"*60)
    
    async def _log_shutdown_summary(self):
        """Log comprehensive shutdown summary"""
        uptime_hours = self.metrics.uptime_seconds / 3600
        
        self.logger.info("" + "="*80)
        self.logger.info("🏁 NORYON V2 REALISTIC TRADING SIMULATION - SHUTDOWN SUMMARY")
        self.logger.info("" + "="*80)
        self.logger.info(f"⏱️ Total Uptime: {uptime_hours:.2f} hours")
        self.logger.info(f"📊 Performance Summary:")
        self.logger.info(f"   • Market Ticks Processed: {self.metrics.total_ticks_processed:,}")
        self.logger.info(f"   • Orders Executed: {self.metrics.total_orders_executed:,}")
        self.logger.info(f"   • Trades Completed: {self.metrics.total_trades_completed:,}")
        self.logger.info(f"   • Final Portfolio Value: ${float(self.metrics.total_portfolio_value):,.2f}")
        self.logger.info(f"   • Total P&L: ${float(self.metrics.total_pnl):+,.2f}")
        
        # Agent performance summary
        agent_summary = self.agent_coordinator.get_agent_summary()
        profitable_agents = sum(1 for agent in agent_summary if agent['total_pnl'] > 0)
        
        self.logger.info(f"🤖 Agent Performance:")
        self.logger.info(f"   • Profitable Agents: {profitable_agents}/{len(agent_summary)}")
        
        # Top performing agents
        top_agents = sorted(agent_summary, key=lambda x: x['total_pnl'], reverse=True)[:3]
        self.logger.info(f"   • Top Performers:")
        for i, agent in enumerate(top_agents, 1):
            self.logger.info(f"     {i}. {agent['name']}: ${agent['total_pnl']:+,.2f} ({agent['total_trades']} trades)")
        
        self.logger.info("" + "="*80)
        self.logger.info("✅ SIMULATION COMPLETED SUCCESSFULLY")
        self.logger.info("" + "="*80)
    
    async def _save_final_results(self):
        """Save final simulation results"""
        try:
            final_results = {
                'simulation_id': self.simulation_id,
                'start_time': self.start_time.isoformat(),
                'end_time': datetime.now(timezone.utc).isoformat(),
                'final_metrics': asdict(self.metrics),
                'agent_final_performance': self.agent_coordinator.get_agent_summary(),
                'coordination_statistics': self.agent_coordinator.get_coordination_statistics(),
                'execution_statistics': self.order_engine.get_execution_statistics(),
                'market_statistics': self.market_feed.get_market_statistics() if hasattr(self.market_feed, 'get_market_statistics') else {},
                'configuration': self.simulation_config
            }
            
            # Save to file for now (in production, would save to database)
            import os
            results_dir = "simulation_results"
            os.makedirs(results_dir, exist_ok=True)
            
            filename = f"{results_dir}/simulation_{self.simulation_id}_results.json"
            with open(filename, 'w') as f:
                json.dump(final_results, f, indent=2, default=str)
            
            self.logger.info(f"💾 Final results saved to: {filename}")
            
        except Exception as e:
            self.logger.error(f"Error saving final results: {e}")
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """Get current dashboard data for web interface"""
        return {
            'simulation_id': self.simulation_id,
            'status': self.system_status.overall_status,
            'metrics': asdict(self.metrics),
            'dashboard': self.dashboard_data,
            'last_updated': datetime.now(timezone.utc).isoformat()
        }
    
    def get_simulation_summary(self) -> Dict[str, Any]:
        """Get comprehensive simulation summary"""
        return {
            'simulation_id': self.simulation_id,
            'status': asdict(self.system_status),
            'metrics': asdict(self.metrics),
            'agent_summary': self.agent_coordinator.get_agent_summary(),
            'coordination_stats': self.agent_coordinator.get_coordination_statistics(),
            'execution_stats': self.order_engine.get_execution_statistics(),
            'market_stats': self.market_feed.get_market_statistics() if hasattr(self.market_feed, 'get_market_statistics') else {},
            'configuration': self.simulation_config
        }