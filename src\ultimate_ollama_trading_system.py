"""
ULTIMATE OLLAMA AI TRADING SYSTEM
Using ALL available Ollama models as specialized trading agents
NO SIMULATED AGENTS - ONLY REAL AI MODELS
"""

import asyncio
import logging
import time
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import json

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# Import real components
from src.ai.real_ollama_integration import RealOllamaAI, OllamaAnalysis
from src.data.real_market_feeds import RealMarketDataManager, DataProvider
from src.db.redis_manager import SimplifiedRedisManager
from src.db.clickhouse import ClickHouseManager
from simple_simulation import SimpleMarketSimulator

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class OllamaAIAgent:
    """Individual Ollama AI Agent with specialized role"""
    
    def __init__(self, model_name: str, role: str, specialty: str):
        self.model_name = model_name
        self.role = role
        self.specialty = specialty
        self.ollama_ai = RealOllamaAI()
        self.analysis_count = 0
        self.total_processing_time = 0.0
        self.confidence_scores = []
        self.logger = logging.getLogger(f"OllamaAgent.{model_name}")
        
    async def initialize(self) -> bool:
        """Initialize the AI agent"""
        try:
            success = await self.ollama_ai.initialize()
            if success:
                self.logger.info(f"🤖 {self.model_name} ({self.role}) initialized successfully")
                return True
            return False
        except Exception as e:
            self.logger.error(f"❌ Failed to initialize {self.model_name}: {e}")
            return False
    
    async def analyze_market(self, symbol: str, market_data: Dict[str, Any]) -> Optional[OllamaAnalysis]:
        """Perform specialized market analysis"""
        try:
            # Create role-specific prompt
            prompt = self._create_specialized_prompt(symbol, market_data)
            
            # Get AI analysis
            analysis = await self.ollama_ai.analyze_market_with_ai(self.model_name, symbol, market_data)
            
            if analysis:
                # Update statistics
                self.analysis_count += 1
                self.total_processing_time += analysis.processing_time
                self.confidence_scores.append(analysis.confidence)
                
                self.logger.info(f"🧠 {self.role} analyzed {symbol}: {analysis.recommendation} "
                               f"(confidence: {analysis.confidence:.2f})")
                
                return analysis
            else:
                self.logger.warning(f"⚠️ No analysis from {self.model_name}")
                return None
                
        except Exception as e:
            self.logger.error(f"❌ Analysis error in {self.model_name}: {e}")
            return None
    
    def _create_specialized_prompt(self, symbol: str, market_data: Dict[str, Any]) -> str:
        """Create role-specific analysis prompt"""
        base_data = f"""
MARKET DATA FOR {symbol}:
- Price: ${market_data.get('price', 0):.4f}
- Volume: {market_data.get('volume', 0):,.0f}
- Bid: ${market_data.get('bid', 0):.4f}
- Ask: ${market_data.get('ask', 0):.4f}
- Source: {market_data.get('source', 'UNKNOWN')}
"""
        
        role_prompts = {
            "TECHNICAL_ANALYST": f"""
You are a TECHNICAL ANALYSIS EXPERT. Analyze {symbol} focusing on:
- Chart patterns and price action
- Support and resistance levels
- Technical indicators (RSI, MACD, moving averages)
- Volume analysis and momentum
- Entry/exit points based on technical signals

{base_data}

Provide technical analysis recommendation: BUY/SELL/HOLD
CONFIDENCE: [0.0-1.0]
REASONING: [Your technical analysis]
""",
            
            "FUNDAMENTAL_ANALYST": f"""
You are a FUNDAMENTAL ANALYSIS EXPERT. Analyze {symbol} focusing on:
- Market fundamentals and economic factors
- Asset valuation and intrinsic value
- Market trends and adoption
- Regulatory environment
- Long-term growth prospects

{base_data}

Provide fundamental analysis recommendation: BUY/SELL/HOLD
CONFIDENCE: [0.0-1.0]
REASONING: [Your fundamental analysis]
""",
            
            "RISK_MANAGER": f"""
You are a RISK MANAGEMENT EXPERT. Analyze {symbol} focusing on:
- Volatility and risk assessment
- Position sizing recommendations
- Stop-loss and take-profit levels
- Portfolio correlation and diversification
- Maximum drawdown considerations

{base_data}

Provide risk-adjusted recommendation: BUY/SELL/HOLD
CONFIDENCE: [0.0-1.0]
REASONING: [Your risk analysis]
""",
            
            "SENTIMENT_ANALYST": f"""
You are a MARKET SENTIMENT EXPERT. Analyze {symbol} focusing on:
- Market psychology and crowd behavior
- Fear and greed indicators
- Social sentiment and news impact
- Market momentum and trend strength
- Contrarian vs momentum signals

{base_data}

Provide sentiment-based recommendation: BUY/SELL/HOLD
CONFIDENCE: [0.0-1.0]
REASONING: [Your sentiment analysis]
""",
            
            "QUANTITATIVE_ANALYST": f"""
You are a QUANTITATIVE ANALYSIS EXPERT. Analyze {symbol} focusing on:
- Statistical analysis and probability
- Mathematical models and algorithms
- Backtesting and historical performance
- Correlation analysis and factor models
- Optimization and portfolio theory

{base_data}

Provide quantitative recommendation: BUY/SELL/HOLD
CONFIDENCE: [0.0-1.0]
REASONING: [Your quantitative analysis]
""",
            
            "STRATEGY_EXPERT": f"""
You are a TRADING STRATEGY EXPERT. Analyze {symbol} focusing on:
- Trading strategy optimization
- Entry and exit timing
- Position management
- Strategy performance metrics
- Adaptive strategy selection

{base_data}

Provide strategic recommendation: BUY/SELL/HOLD
CONFIDENCE: [0.0-1.0]
REASONING: [Your strategic analysis]
""",
            
            "MARKET_MAKER": f"""
You are a MARKET MAKING EXPERT. Analyze {symbol} focusing on:
- Bid-ask spread analysis
- Liquidity and order flow
- Market microstructure
- Price discovery mechanisms
- Arbitrage opportunities

{base_data}

Provide market-making recommendation: BUY/SELL/HOLD
CONFIDENCE: [0.0-1.0]
REASONING: [Your market structure analysis]
""",
            
            "MACRO_ANALYST": f"""
You are a MACROECONOMIC EXPERT. Analyze {symbol} focusing on:
- Global economic trends
- Central bank policies and interest rates
- Inflation and currency impacts
- Geopolitical events
- Cross-asset correlations

{base_data}

Provide macro-based recommendation: BUY/SELL/HOLD
CONFIDENCE: [0.0-1.0]
REASONING: [Your macroeconomic analysis]
"""
        }
        
        return role_prompts.get(self.role, role_prompts["TECHNICAL_ANALYST"])
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get agent performance statistics"""
        if self.analysis_count == 0:
            return {"analyses": 0, "avg_time": 0, "avg_confidence": 0}
        
        return {
            "analyses": self.analysis_count,
            "avg_processing_time": self.total_processing_time / self.analysis_count,
            "avg_confidence": sum(self.confidence_scores) / len(self.confidence_scores),
            "total_time": self.total_processing_time
        }
    
    async def close(self):
        """Close AI agent"""
        await self.ollama_ai.close()


class UltimateOllamaTrading:
    """Ultimate Ollama AI Trading System with ALL real models"""
    
    def __init__(self):
        self.start_time = datetime.now()
        self.running = False
        
        # REAL AI AGENTS - NO SIMULATIONS
        self.ai_agents: Dict[str, OllamaAIAgent] = {}
        
        # SYSTEM COMPONENTS
        self.market_manager = RealMarketDataManager()
        self.redis_manager = SimplifiedRedisManager()
        self.clickhouse_manager = ClickHouseManager()
        self.market_simulator = SimpleMarketSimulator(["BTCUSDT", "ETHUSDT", "ADAUSDT"])
        
        # TRADING DATA
        self.real_symbols = ["BTC-USD", "ETH-USD", "ADA-USD", "SOL-USD"]
        self.sim_symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
        self.all_symbols = self.real_symbols + self.sim_symbols
        
        # ANALYSIS STORAGE
        self.ai_analyses: Dict[str, List[OllamaAnalysis]] = {}
        self.consensus_decisions = []
        self.system_metrics = {}
        
        self.logger = logging.getLogger(f"{__name__}.UltimateOllamaTrading")
    
    def _define_ai_agents(self) -> Dict[str, Tuple[str, str]]:
        """Define all Ollama AI agents with their roles"""
        return {
            # TECHNICAL ANALYSIS SPECIALISTS
            "nemotron-mini:4b": ("TECHNICAL_ANALYST", "Chart patterns and technical indicators"),
            "phi4-reasoning:plus": ("QUANTITATIVE_ANALYST", "Mathematical models and statistical analysis"),
            
            # FUNDAMENTAL & MACRO ANALYSIS
            "marco-o1:7b": ("MACRO_ANALYST", "Global economics and market trends"),
            "magistral:24b": ("FUNDAMENTAL_ANALYST", "Asset valuation and intrinsic analysis"),
            
            # STRATEGY & RISK MANAGEMENT
            "command-r:35b": ("STRATEGY_EXPERT", "Trading strategy optimization"),
            "cogito:32b": ("RISK_MANAGER", "Risk assessment and portfolio management"),
            
            # MARKET STRUCTURE & SENTIMENT
            "gemma3:27b": ("SENTIMENT_ANALYST", "Market psychology and crowd behavior"),
            "mistral-small:24b": ("MARKET_MAKER", "Liquidity and market microstructure"),
            
            # SPECIALIZED ANALYSIS
            "falcon3:10b": ("TECHNICAL_ANALYST", "Advanced chart pattern recognition"),
            "granite3.3:8b": ("QUANTITATIVE_ANALYST", "Statistical modeling and backtesting"),
            "qwen3:32b": ("STRATEGY_EXPERT", "Adaptive strategy selection"),
            "deepseek-r1:32b": ("FUNDAMENTAL_ANALYST", "Deep fundamental research")
        }
    
    async def initialize_all_agents(self) -> bool:
        """Initialize ALL Ollama AI agents"""
        try:
            self.logger.info("🚀 INITIALIZING ULTIMATE OLLAMA AI TRADING SYSTEM")
            self.logger.info("=" * 80)
            self.logger.info("🤖 LOADING ALL REAL OLLAMA AI MODELS")
            self.logger.info("🚫 NO SIMULATED AGENTS - ONLY REAL AI")
            self.logger.info("⚡ MAXIMUM AI POWER ACTIVATED")
            self.logger.info("=" * 80)
            
            agent_definitions = self._define_ai_agents()
            
            # Initialize each AI agent
            for model_name, (role, specialty) in agent_definitions.items():
                self.logger.info(f"🤖 Initializing {model_name} as {role}...")
                
                agent = OllamaAIAgent(model_name, role, specialty)
                success = await agent.initialize()
                
                if success:
                    self.ai_agents[model_name] = agent
                    self.logger.info(f"✅ {model_name} ({role}) ready for trading")
                else:
                    self.logger.warning(f"⚠️ {model_name} failed to initialize")
            
            # Initialize other components
            self.logger.info("📈 Initializing market data feeds...")
            await self.market_manager.initialize_feed(DataProvider.COINBASE)
            
            self.logger.info("💾 Initializing databases...")
            await self.clickhouse_manager.initialize_schema()
            await self.redis_manager.test_connection()
            
            self.logger.info("🎲 Starting market simulation...")
            asyncio.create_task(self.market_simulator.start())
            
            self.logger.info("=" * 80)
            self.logger.info(f"✅ ULTIMATE OLLAMA SYSTEM INITIALIZED")
            self.logger.info(f"🤖 {len(self.ai_agents)} REAL AI AGENTS ACTIVE")
            self.logger.info(f"📊 {len(self.all_symbols)} SYMBOLS MONITORED")
            self.logger.info("🚀 READY FOR MAXIMUM AI TRADING")
            self.logger.info("=" * 80)
            
            return len(self.ai_agents) > 0

        except Exception as e:
            self.logger.error(f"❌ System initialization failed: {e}")
            return False

    async def start_ultimate_trading(self):
        """Start the ultimate Ollama AI trading system"""
        try:
            self.running = True

            self.logger.info("🚀 STARTING ULTIMATE OLLAMA AI TRADING")
            self.logger.info("=" * 80)
            self.logger.info("🤖 ALL REAL OLLAMA MODELS ACTIVE")
            self.logger.info("🧠 MAXIMUM AI INTELLIGENCE DEPLOYED")
            self.logger.info("📊 COMPREHENSIVE MARKET ANALYSIS")
            self.logger.info("💰 REAL-TIME TRADING DECISIONS")
            self.logger.info("=" * 80)

            # Start all system loops
            tasks = [
                asyncio.create_task(self._ultimate_ai_analysis_loop()),
                asyncio.create_task(self._market_data_collection_loop()),
                asyncio.create_task(self._consensus_decision_loop()),
                asyncio.create_task(self._system_monitoring_loop()),
                asyncio.create_task(self._performance_analytics_loop()),
                asyncio.create_task(self._ai_coordination_loop())
            ]

            # Wait for all tasks
            await asyncio.gather(*tasks, return_exceptions=True)

        except Exception as e:
            self.logger.error(f"❌ Ultimate trading system error: {e}")
        finally:
            await self.shutdown_all_agents()

    async def _ultimate_ai_analysis_loop(self):
        """Ultimate AI analysis using ALL Ollama models"""
        self.logger.info("🧠 Starting ULTIMATE AI analysis loop...")

        while self.running:
            try:
                for symbol in self.all_symbols:
                    # Get current market data
                    market_data = await self._get_market_data(symbol)

                    if market_data:
                        self.logger.info(f"🔥 DEPLOYING ALL AI AGENTS FOR {symbol}")

                        # Analyze with ALL AI agents simultaneously
                        analysis_tasks = []
                        for model_name, agent in self.ai_agents.items():
                            task = asyncio.create_task(
                                agent.analyze_market(symbol, market_data)
                            )
                            analysis_tasks.append((model_name, task))

                        # Collect all analyses
                        symbol_analyses = []
                        for model_name, task in analysis_tasks:
                            try:
                                analysis = await asyncio.wait_for(task, timeout=45.0)
                                if analysis:
                                    symbol_analyses.append(analysis)
                                    self.logger.info(f"✅ {model_name}: {analysis.recommendation} "
                                                   f"({analysis.confidence:.2f})")
                            except asyncio.TimeoutError:
                                self.logger.warning(f"⏰ {model_name} timed out for {symbol}")
                            except Exception as e:
                                self.logger.error(f"❌ {model_name} error: {e}")

                        # Store analyses
                        if symbol_analyses:
                            self.ai_analyses[symbol] = symbol_analyses

                            # Log comprehensive analysis
                            self.logger.info(f"🎯 COMPLETE AI ANALYSIS FOR {symbol}:")
                            for analysis in symbol_analyses:
                                self.logger.info(f"   {analysis.model_name}: {analysis.recommendation} "
                                               f"(confidence: {analysis.confidence:.2f}, "
                                               f"time: {analysis.processing_time:.2f}s)")

                            # Store in database
                            await self._store_comprehensive_analysis(symbol, symbol_analyses, market_data)

                # Wait before next analysis cycle
                await asyncio.sleep(120)  # Analyze every 2 minutes

            except Exception as e:
                self.logger.error(f"❌ Ultimate AI analysis error: {e}")
                await asyncio.sleep(60)

    async def _consensus_decision_loop(self):
        """Build consensus from all AI agents"""
        self.logger.info("🎯 Starting AI consensus decision loop...")

        while self.running:
            try:
                for symbol in self.all_symbols:
                    if symbol in self.ai_analyses and self.ai_analyses[symbol]:
                        analyses = self.ai_analyses[symbol]

                        # Build weighted consensus
                        consensus = await self._build_ai_consensus(symbol, analyses)

                        if consensus and consensus.confidence > 0.7:
                            # Make trading decision
                            decision = {
                                "symbol": symbol,
                                "action": consensus.recommendation,
                                "confidence": consensus.confidence,
                                "ai_agents": len(analyses),
                                "reasoning": consensus.reasoning,
                                "timestamp": datetime.now(),
                                "source": "ULTIMATE_OLLAMA_CONSENSUS"
                            }

                            self.consensus_decisions.append(decision)

                            self.logger.info("🚀 ULTIMATE AI CONSENSUS DECISION:")
                            self.logger.info(f"   Symbol: {symbol}")
                            self.logger.info(f"   Action: {consensus.recommendation}")
                            self.logger.info(f"   Confidence: {consensus.confidence:.2f}")
                            self.logger.info(f"   AI Agents: {len(analyses)}")
                            self.logger.info(f"   Reasoning: {consensus.reasoning[:100]}...")

                            # Store decision
                            await self._store_consensus_decision(decision)

                await asyncio.sleep(60)  # Check every minute

            except Exception as e:
                self.logger.error(f"❌ Consensus decision error: {e}")
                await asyncio.sleep(30)

    async def _build_ai_consensus(self, symbol: str, analyses: List[OllamaAnalysis]) -> Optional[OllamaAnalysis]:
        """Build weighted consensus from all AI analyses"""
        try:
            if not analyses:
                return None

            # Weight votes by confidence and model size
            model_weights = {
                "deepseek-r1:32b": 1.0,
                "command-r:35b": 1.0,
                "cogito:32b": 0.9,
                "qwen3:32b": 0.9,
                "gemma3:27b": 0.8,
                "magistral:24b": 0.8,
                "mistral-small:24b": 0.8,
                "phi4-reasoning:plus": 0.7,
                "marco-o1:7b": 0.6,
                "falcon3:10b": 0.5,
                "granite3.3:8b": 0.4,
                "nemotron-mini:4b": 0.3
            }

            # Calculate weighted votes
            buy_weight = 0
            sell_weight = 0
            hold_weight = 0
            total_weight = 0
            total_confidence = 0

            reasoning_parts = []

            for analysis in analyses:
                weight = model_weights.get(analysis.model_name, 0.5)
                confidence_weight = weight * analysis.confidence

                if analysis.recommendation == "BUY":
                    buy_weight += confidence_weight
                elif analysis.recommendation == "SELL":
                    sell_weight += confidence_weight
                else:
                    hold_weight += confidence_weight

                total_weight += confidence_weight
                total_confidence += analysis.confidence

                reasoning_parts.append(f"{analysis.model_name}({weight:.1f}): {analysis.recommendation}")

            # Determine consensus
            if buy_weight > sell_weight and buy_weight > hold_weight:
                consensus_rec = "BUY"
                consensus_confidence = buy_weight / total_weight if total_weight > 0 else 0
            elif sell_weight > buy_weight and sell_weight > hold_weight:
                consensus_rec = "SELL"
                consensus_confidence = sell_weight / total_weight if total_weight > 0 else 0
            else:
                consensus_rec = "HOLD"
                consensus_confidence = hold_weight / total_weight if total_weight > 0 else 0

            # Create consensus analysis
            consensus_reasoning = f"Weighted consensus from {len(analyses)} AI agents: " + "; ".join(reasoning_parts)

            consensus = OllamaAnalysis(
                model_name="ULTIMATE_CONSENSUS",
                symbol=symbol,
                analysis_text=f"Ultimate consensus from {len(analyses)} Ollama AI agents",
                recommendation=consensus_rec,
                confidence=consensus_confidence,
                reasoning=consensus_reasoning,
                timestamp=datetime.now(),
                processing_time=sum(a.processing_time for a in analyses)
            )

            self.logger.info(f"🎯 ULTIMATE CONSENSUS for {symbol}: {consensus_rec} "
                           f"(confidence: {consensus_confidence:.2f}, agents: {len(analyses)})")

            return consensus

        except Exception as e:
            self.logger.error(f"❌ Error building consensus: {e}")
            return None

    async def _get_market_data(self, symbol: str) -> Optional[Dict[str, Any]]:
        """Get market data (real or simulated)"""
        try:
            if symbol in self.real_symbols:
                # Get real market data
                market_data = await self.market_manager.get_real_market_data(
                    DataProvider.COINBASE, symbol
                )
                if market_data:
                    return {
                        "symbol": symbol,
                        "price": market_data.price,
                        "volume": market_data.volume_24h,
                        "bid": market_data.bid,
                        "ask": market_data.ask,
                        "source": "REAL_COINBASE"
                    }
            else:
                # Get simulated data
                if hasattr(self.market_simulator, 'prices') and symbol in self.market_simulator.prices:
                    price = self.market_simulator.prices[symbol]
                    return {
                        "symbol": symbol,
                        "price": price,
                        "volume": 1000000,
                        "bid": price * 0.999,
                        "ask": price * 1.001,
                        "source": "SIMULATED"
                    }

            return None

        except Exception as e:
            self.logger.error(f"❌ Error getting market data for {symbol}: {e}")
            return None

    async def _market_data_collection_loop(self):
        """Market data collection loop"""
        self.logger.info("📊 Starting market data collection...")

        while self.running:
            try:
                for symbol in self.all_symbols:
                    market_data = await self._get_market_data(symbol)
                    if market_data:
                        # Store in Redis
                        await self.redis_manager.store_market_data(
                            f"ultimate_market_{symbol}",
                            market_data,
                            300
                        )

                        # Store in ClickHouse
                        ohlcv_data = {
                            "symbol": symbol,
                            "timestamp": datetime.now(),
                            "open": market_data["price"],
                            "high": market_data["price"] * 1.001,
                            "low": market_data["price"] * 0.999,
                            "close": market_data["price"],
                            "volume": market_data["volume"],
                            "source": market_data["source"]
                        }

                        await self.clickhouse_manager.insert_ohlcv_batch([ohlcv_data])

                await asyncio.sleep(5)  # Update every 5 seconds

            except Exception as e:
                self.logger.error(f"❌ Market data collection error: {e}")
                await asyncio.sleep(10)

    async def _system_monitoring_loop(self):
        """Ultimate system monitoring"""
        self.logger.info("🔍 Starting ultimate system monitoring...")

        while self.running:
            try:
                runtime = datetime.now() - self.start_time

                # Calculate system metrics
                total_analyses = sum(len(analyses) for analyses in self.ai_analyses.values())
                active_agents = len(self.ai_agents)

                self.logger.info("=" * 80)
                self.logger.info("🚀 ULTIMATE OLLAMA AI TRADING SYSTEM STATUS")
                self.logger.info("=" * 80)
                self.logger.info(f"⏱️  Runtime: {runtime}")
                self.logger.info(f"🤖 Active AI Agents: {active_agents}")
                self.logger.info(f"📈 Symbols Analyzed: {len(self.ai_analyses)}")
                self.logger.info(f"🧠 Total AI Analyses: {total_analyses}")
                self.logger.info(f"💰 Consensus Decisions: {len(self.consensus_decisions)}")

                # Show agent performance
                self.logger.info("🤖 AI Agent Performance:")
                for model_name, agent in self.ai_agents.items():
                    stats = agent.get_performance_stats()
                    if stats["analyses"] > 0:
                        self.logger.info(f"   {model_name}: {stats['analyses']} analyses, "
                                       f"avg time: {stats['avg_processing_time']:.2f}s, "
                                       f"avg confidence: {stats['avg_confidence']:.2f}")

                # Show recent consensus decisions
                if self.consensus_decisions:
                    self.logger.info("🎯 Recent Consensus Decisions:")
                    for decision in self.consensus_decisions[-3:]:
                        self.logger.info(f"   {decision['symbol']}: {decision['action']} "
                                       f"({decision['confidence']:.2f}) - {decision['ai_agents']} agents")

                self.logger.info("=" * 80)

                await asyncio.sleep(90)  # Status every 90 seconds

            except Exception as e:
                self.logger.error(f"❌ System monitoring error: {e}")
                await asyncio.sleep(30)

    async def _performance_analytics_loop(self):
        """Performance analytics and optimization"""
        self.logger.info("📈 Starting performance analytics...")

        while self.running:
            try:
                # Analyze AI agent performance
                if self.ai_analyses:
                    for symbol, analyses in self.ai_analyses.items():
                        if analyses:
                            recent_analyses = [a for a in analyses
                                             if (datetime.now() - a.timestamp).total_seconds() < 3600]

                            if recent_analyses:
                                avg_confidence = sum(a.confidence for a in recent_analyses) / len(recent_analyses)
                                avg_time = sum(a.processing_time for a in recent_analyses) / len(recent_analyses)

                                self.logger.info(f"📊 {symbol} Performance (1h): "
                                               f"{len(recent_analyses)} analyses, "
                                               f"avg confidence: {avg_confidence:.2f}, "
                                               f"avg time: {avg_time:.2f}s")

                await asyncio.sleep(300)  # Analytics every 5 minutes

            except Exception as e:
                self.logger.error(f"❌ Performance analytics error: {e}")
                await asyncio.sleep(60)

    async def _ai_coordination_loop(self):
        """AI agent coordination and optimization"""
        self.logger.info("🤝 Starting AI coordination loop...")

        while self.running:
            try:
                # Check agent health and performance
                for model_name, agent in list(self.ai_agents.items()):
                    stats = agent.get_performance_stats()

                    # Log agent status
                    if stats["analyses"] > 0:
                        self.logger.info(f"🤖 {model_name} Status: "
                                       f"{stats['analyses']} analyses, "
                                       f"avg confidence: {stats['avg_confidence']:.2f}")

                await asyncio.sleep(180)  # Coordinate every 3 minutes

            except Exception as e:
                self.logger.error(f"❌ AI coordination error: {e}")
                await asyncio.sleep(60)

    async def _store_comprehensive_analysis(self, symbol: str, analyses: List[OllamaAnalysis], market_data: Dict[str, Any]):
        """Store comprehensive AI analysis in database"""
        try:
            for analysis in analyses:
                analysis_data = {
                    "analysis_id": f"ultimate_{analysis.symbol}_{analysis.model_name}_{int(time.time())}",
                    "symbol": analysis.symbol,
                    "timestamp": analysis.timestamp,
                    "model_type": analysis.model_name,
                    "prediction_value": 1.0 if analysis.recommendation == "BUY" else -1.0 if analysis.recommendation == "SELL" else 0.0,
                    "confidence": analysis.confidence,
                    "market_price": market_data["price"],
                    "data_source": market_data["source"],
                    "features_used": analysis.reasoning,
                    "model_version": "ollama_ultimate",
                    "processing_time": analysis.processing_time,
                    "recommendation": analysis.recommendation
                }

                await self.clickhouse_manager.insert_ai_analysis([analysis_data])

        except Exception as e:
            self.logger.error(f"❌ Error storing comprehensive analysis: {e}")

    async def _store_consensus_decision(self, decision: Dict[str, Any]):
        """Store consensus trading decision"""
        try:
            trade_data = {
                "trade_id": f"ultimate_consensus_{decision['symbol']}_{int(time.time())}",
                "symbol": decision["symbol"],
                "side": decision["action"],
                "quantity": 1000.0,
                "price": 0.0,
                "timestamp": decision["timestamp"],
                "order_type": "ULTIMATE_CONSENSUS",
                "ai_confidence": decision["confidence"],
                "ai_model": f"CONSENSUS_{decision['ai_agents']}_AGENTS",
                "portfolio_id": "ultimate_ollama_system",
                "execution_type": "AI_CONSENSUS"
            }

            await self.clickhouse_manager.insert_trade_batch([trade_data])

        except Exception as e:
            self.logger.error(f"❌ Error storing consensus decision: {e}")

    async def shutdown_all_agents(self):
        """Graceful shutdown of all AI agents"""
        try:
            self.logger.info("🛑 SHUTTING DOWN ULTIMATE OLLAMA SYSTEM")

            self.running = False

            # Close all AI agents
            for model_name, agent in self.ai_agents.items():
                self.logger.info(f"🛑 Shutting down {model_name}...")
                await agent.close()

            # Close market data
            await self.market_manager.close_all_feeds()

            runtime = datetime.now() - self.start_time
            total_analyses = sum(len(analyses) for analyses in self.ai_analyses.values())

            self.logger.info("=" * 80)
            self.logger.info("📊 ULTIMATE OLLAMA SYSTEM FINAL REPORT")
            self.logger.info("=" * 80)
            self.logger.info(f"⏱️  Total Runtime: {runtime}")
            self.logger.info(f"🤖 AI Agents Used: {len(self.ai_agents)}")
            self.logger.info(f"🧠 Total AI Analyses: {total_analyses}")
            self.logger.info(f"💰 Consensus Decisions: {len(self.consensus_decisions)}")
            self.logger.info(f"📈 Symbols Analyzed: {len(self.ai_analyses)}")

            # Final agent performance
            self.logger.info("🏆 Final AI Agent Performance:")
            for model_name, agent in self.ai_agents.items():
                stats = agent.get_performance_stats()
                if stats["analyses"] > 0:
                    self.logger.info(f"   {model_name}: {stats['analyses']} analyses, "
                                   f"total time: {stats['total_time']:.2f}s, "
                                   f"avg confidence: {stats['avg_confidence']:.2f}")

            self.logger.info("✅ ULTIMATE OLLAMA SYSTEM SHUTDOWN COMPLETE")

        except Exception as e:
            self.logger.error(f"❌ Error during shutdown: {e}")


async def main():
    """Main entry point for Ultimate Ollama Trading System"""
    try:
        # Create ultimate system
        ultimate_system = UltimateOllamaTrading()

        # Initialize all AI agents
        success = await ultimate_system.initialize_all_agents()

        if success:
            # Start ultimate trading
            await ultimate_system.start_ultimate_trading()
        else:
            logger.error("❌ Ultimate system initialization failed")

    except KeyboardInterrupt:
        logger.info("🛑 Ultimate system shutdown requested by user")
    except Exception as e:
        logger.error(f"❌ Ultimate system error: {e}")


if __name__ == "__main__":
    asyncio.run(main())
