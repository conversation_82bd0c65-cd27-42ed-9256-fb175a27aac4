#!/usr/bin/env python3
"""
Analysis Synthesizer
Advanced system for synthesizing multiple analysis types into coherent insights.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime, timedelta
from collections import defaultdict
import statistics

logger = logging.getLogger(__name__)

class AnalysisType(Enum):
    """Types of analysis that can be synthesized."""
    TECHNICAL = "technical"
    FUNDAMENTAL = "fundamental"
    SENTIMENT = "sentiment"
    NEWS = "news"
    VOLUME = "volume"
    PATTERN = "pattern"
    FIBONACCI = "fibonacci"
    WAVE = "wave"
    RISK = "risk"
    CORRELATION = "correlation"

class ConfidenceLevel(Enum):
    """Confidence levels for synthesized analysis."""
    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"

class MarketBias(Enum):
    """Overall market bias from synthesized analysis."""
    STRONGLY_BEARISH = "strongly_bearish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"
    BULLISH = "bullish"
    STRONGLY_BULLISH = "strongly_bullish"

@dataclass
class AnalysisInput:
    """Input analysis data structure."""
    analysis_type: AnalysisType
    symbol: str
    timeframe: str
    timestamp: datetime
    confidence: float
    bias: str  # 'bullish', 'bearish', 'neutral'
    strength: float
    key_points: List[str]
    supporting_data: Dict[str, Any]
    weight: float = 1.0

@dataclass
class SynthesizedInsight:
    """Synthesized analysis insight."""
    insight_id: str
    symbol: str
    timeframe: str
    overall_bias: MarketBias
    confidence: ConfidenceLevel
    strength: float
    consensus_score: float
    key_drivers: List[str]
    conflicting_signals: List[str]
    risk_factors: List[str]
    opportunities: List[str]
    price_targets: Dict[str, float]
    time_horizon: str
    supporting_analyses: List[AnalysisType]
    synthesis_timestamp: datetime
    metadata: Dict[str, Any] = None

@dataclass
class ConflictAnalysis:
    """Analysis of conflicting signals."""
    conflict_type: str
    conflicting_analyses: List[AnalysisType]
    severity: str  # 'low', 'medium', 'high'
    resolution_suggestion: str
    impact_on_confidence: float

class AnalysisSynthesizer:
    """Advanced analysis synthesis system."""
    
    def __init__(self):
        self.analysis_weights = {
            AnalysisType.TECHNICAL: 1.0,
            AnalysisType.FUNDAMENTAL: 1.2,
            AnalysisType.SENTIMENT: 0.8,
            AnalysisType.NEWS: 0.9,
            AnalysisType.VOLUME: 1.1,
            AnalysisType.PATTERN: 1.0,
            AnalysisType.FIBONACCI: 0.9,
            AnalysisType.WAVE: 0.8,
            AnalysisType.RISK: 1.3,
            AnalysisType.CORRELATION: 0.7
        }
        
        self.timeframe_weights = {
            '1m': 0.3,
            '5m': 0.5,
            '15m': 0.7,
            '1h': 1.0,
            '4h': 1.2,
            '1d': 1.5,
            '1w': 1.3,
            '1M': 1.1
        }
        
        self.conflict_threshold = 0.3
        self.consensus_threshold = 0.7
        
    def synthesize_analyses(self, analyses: List[AnalysisInput], 
                          symbol: str, timeframe: str) -> SynthesizedInsight:
        """Synthesize multiple analyses into a coherent insight."""
        try:
            if not analyses:
                logger.warning("No analyses provided for synthesis")
                return self._create_empty_insight(symbol, timeframe)
            
            # Filter and weight analyses
            weighted_analyses = self._apply_weights(analyses)
            
            # Calculate overall bias
            overall_bias = self._calculate_overall_bias(weighted_analyses)
            
            # Calculate confidence
            confidence = self._calculate_confidence(weighted_analyses)
            
            # Calculate consensus score
            consensus_score = self._calculate_consensus_score(weighted_analyses)
            
            # Identify conflicts
            conflicts = self._identify_conflicts(weighted_analyses)
            
            # Extract key drivers
            key_drivers = self._extract_key_drivers(weighted_analyses)
            
            # Identify risk factors and opportunities
            risk_factors = self._identify_risk_factors(weighted_analyses, conflicts)
            opportunities = self._identify_opportunities(weighted_analyses)
            
            # Calculate price targets
            price_targets = self._calculate_price_targets(weighted_analyses, overall_bias)
            
            # Determine time horizon
            time_horizon = self._determine_time_horizon(weighted_analyses)
            
            # Create synthesized insight
            insight = SynthesizedInsight(
                insight_id=f"synthesis_{symbol}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                symbol=symbol,
                timeframe=timeframe,
                overall_bias=overall_bias,
                confidence=confidence,
                strength=self._calculate_strength(weighted_analyses),
                consensus_score=consensus_score,
                key_drivers=key_drivers,
                conflicting_signals=[c.conflict_type for c in conflicts],
                risk_factors=risk_factors,
                opportunities=opportunities,
                price_targets=price_targets,
                time_horizon=time_horizon,
                supporting_analyses=[a.analysis_type for a in weighted_analyses],
                synthesis_timestamp=datetime.now(),
                metadata={
                    'total_analyses': len(analyses),
                    'conflicts_detected': len(conflicts),
                    'analysis_distribution': self._get_analysis_distribution(analyses)
                }
            )
            
            return insight
            
        except Exception as e:
            logger.error(f"Error synthesizing analyses: {e}")
            return self._create_empty_insight(symbol, timeframe)
    
    def _apply_weights(self, analyses: List[AnalysisInput]) -> List[AnalysisInput]:
        """Apply weights to analyses based on type and timeframe."""
        weighted_analyses = []
        
        try:
            for analysis in analyses:
                # Get base weight for analysis type
                type_weight = self.analysis_weights.get(analysis.analysis_type, 1.0)
                
                # Get timeframe weight
                timeframe_weight = self.timeframe_weights.get(analysis.timeframe, 1.0)
                
                # Calculate final weight
                final_weight = analysis.weight * type_weight * timeframe_weight
                
                # Create weighted analysis
                weighted_analysis = AnalysisInput(
                    analysis_type=analysis.analysis_type,
                    symbol=analysis.symbol,
                    timeframe=analysis.timeframe,
                    timestamp=analysis.timestamp,
                    confidence=analysis.confidence,
                    bias=analysis.bias,
                    strength=analysis.strength,
                    key_points=analysis.key_points,
                    supporting_data=analysis.supporting_data,
                    weight=final_weight
                )
                
                weighted_analyses.append(weighted_analysis)
        
        except Exception as e:
            logger.error(f"Error applying weights: {e}")
            return analyses
        
        return weighted_analyses
    
    def _calculate_overall_bias(self, analyses: List[AnalysisInput]) -> MarketBias:
        """Calculate overall market bias from weighted analyses."""
        try:
            if not analyses:
                return MarketBias.NEUTRAL
            
            # Calculate weighted bias score
            bias_score = 0.0
            total_weight = 0.0
            
            for analysis in analyses:
                # Convert bias to numeric score
                if analysis.bias.lower() == 'bullish':
                    numeric_bias = analysis.strength
                elif analysis.bias.lower() == 'bearish':
                    numeric_bias = -analysis.strength
                else:
                    numeric_bias = 0.0
                
                # Weight by confidence and analysis weight
                weighted_bias = numeric_bias * analysis.confidence * analysis.weight
                bias_score += weighted_bias
                total_weight += analysis.confidence * analysis.weight
            
            # Normalize bias score
            if total_weight > 0:
                normalized_bias = bias_score / total_weight
            else:
                normalized_bias = 0.0
            
            # Convert to MarketBias enum
            if normalized_bias >= 0.7:
                return MarketBias.STRONGLY_BULLISH
            elif normalized_bias >= 0.3:
                return MarketBias.BULLISH
            elif normalized_bias <= -0.7:
                return MarketBias.STRONGLY_BEARISH
            elif normalized_bias <= -0.3:
                return MarketBias.BEARISH
            else:
                return MarketBias.NEUTRAL
                
        except Exception as e:
            logger.error(f"Error calculating overall bias: {e}")
            return MarketBias.NEUTRAL
    
    def _calculate_confidence(self, analyses: List[AnalysisInput]) -> ConfidenceLevel:
        """Calculate overall confidence level."""
        try:
            if not analyses:
                return ConfidenceLevel.VERY_LOW
            
            # Calculate weighted average confidence
            total_confidence = 0.0
            total_weight = 0.0
            
            for analysis in analyses:
                weighted_confidence = analysis.confidence * analysis.weight
                total_confidence += weighted_confidence
                total_weight += analysis.weight
            
            if total_weight > 0:
                avg_confidence = total_confidence / total_weight
            else:
                avg_confidence = 0.0
            
            # Adjust for number of analyses (more analyses = higher confidence)
            analysis_count_factor = min(1.2, 1.0 + (len(analyses) - 1) * 0.05)
            adjusted_confidence = avg_confidence * analysis_count_factor
            
            # Convert to ConfidenceLevel enum
            if adjusted_confidence >= 0.9:
                return ConfidenceLevel.VERY_HIGH
            elif adjusted_confidence >= 0.7:
                return ConfidenceLevel.HIGH
            elif adjusted_confidence >= 0.5:
                return ConfidenceLevel.MODERATE
            elif adjusted_confidence >= 0.3:
                return ConfidenceLevel.LOW
            else:
                return ConfidenceLevel.VERY_LOW
                
        except Exception as e:
            logger.error(f"Error calculating confidence: {e}")
            return ConfidenceLevel.VERY_LOW
    
    def _calculate_consensus_score(self, analyses: List[AnalysisInput]) -> float:
        """Calculate consensus score among analyses."""
        try:
            if len(analyses) < 2:
                return 1.0
            
            # Group analyses by bias
            bias_groups = defaultdict(list)
            for analysis in analyses:
                bias_groups[analysis.bias.lower()].append(analysis)
            
            # Calculate weighted consensus
            max_group_weight = 0.0
            total_weight = 0.0
            
            for bias, group_analyses in bias_groups.items():
                group_weight = sum(a.weight * a.confidence for a in group_analyses)
                max_group_weight = max(max_group_weight, group_weight)
                total_weight += group_weight
            
            if total_weight > 0:
                consensus_score = max_group_weight / total_weight
            else:
                consensus_score = 0.0
            
            return min(1.0, consensus_score)
            
        except Exception as e:
            logger.error(f"Error calculating consensus score: {e}")
            return 0.0
    
    def _identify_conflicts(self, analyses: List[AnalysisInput]) -> List[ConflictAnalysis]:
        """Identify conflicts between analyses."""
        conflicts = []
        
        try:
            # Group analyses by type
            type_groups = defaultdict(list)
            for analysis in analyses:
                type_groups[analysis.analysis_type].append(analysis)
            
            # Check for conflicts within each type
            for analysis_type, group_analyses in type_groups.items():
                if len(group_analyses) > 1:
                    conflict = self._check_intra_type_conflict(analysis_type, group_analyses)
                    if conflict:
                        conflicts.append(conflict)
            
            # Check for conflicts between types
            inter_type_conflicts = self._check_inter_type_conflicts(analyses)
            conflicts.extend(inter_type_conflicts)
            
        except Exception as e:
            logger.error(f"Error identifying conflicts: {e}")
        
        return conflicts
    
    def _check_intra_type_conflict(self, analysis_type: AnalysisType, 
                                  analyses: List[AnalysisInput]) -> Optional[ConflictAnalysis]:
        """Check for conflicts within the same analysis type."""
        try:
            if len(analyses) < 2:
                return None
            
            # Check bias consistency
            biases = [a.bias.lower() for a in analyses]
            unique_biases = set(biases)
            
            if len(unique_biases) > 1 and 'neutral' not in unique_biases:
                # Conflicting biases detected
                severity = "high" if len(unique_biases) > 2 else "medium"
                
                return ConflictAnalysis(
                    conflict_type=f"Intra-type conflict in {analysis_type.value}",
                    conflicting_analyses=[analysis_type],
                    severity=severity,
                    resolution_suggestion="Review analysis parameters and data sources",
                    impact_on_confidence=-0.2
                )
            
            return None
            
        except Exception as e:
            logger.error(f"Error checking intra-type conflict: {e}")
            return None
    
    def _check_inter_type_conflicts(self, analyses: List[AnalysisInput]) -> List[ConflictAnalysis]:
        """Check for conflicts between different analysis types."""
        conflicts = []
        
        try:
            # Group by bias
            bias_groups = defaultdict(list)
            for analysis in analyses:
                bias_groups[analysis.bias.lower()].append(analysis)
            
            # Check if we have strong conflicting signals
            if ('bullish' in bias_groups and 'bearish' in bias_groups):
                bullish_strength = sum(a.strength * a.confidence for a in bias_groups['bullish'])
                bearish_strength = sum(a.strength * a.confidence for a in bias_groups['bearish'])
                
                # If both sides are strong, it's a conflict
                if bullish_strength > 0.5 and bearish_strength > 0.5:
                    conflict = ConflictAnalysis(
                        conflict_type="Strong bullish vs bearish signals",
                        conflicting_analyses=[a.analysis_type for a in analyses],
                        severity="high",
                        resolution_suggestion="Wait for clearer signals or reduce position size",
                        impact_on_confidence=-0.3
                    )
                    conflicts.append(conflict)
            
        except Exception as e:
            logger.error(f"Error checking inter-type conflicts: {e}")
        
        return conflicts
    
    def _extract_key_drivers(self, analyses: List[AnalysisInput]) -> List[str]:
        """Extract key drivers from analyses."""
        key_drivers = []
        
        try:
            # Collect all key points with weights
            weighted_points = []
            
            for analysis in analyses:
                for point in analysis.key_points:
                    weighted_points.append({
                        'point': point,
                        'weight': analysis.weight * analysis.confidence,
                        'type': analysis.analysis_type.value
                    })
            
            # Sort by weight and take top drivers
            weighted_points.sort(key=lambda x: x['weight'], reverse=True)
            
            # Extract unique key drivers
            seen_points = set()
            for item in weighted_points[:10]:  # Top 10
                if item['point'] not in seen_points:
                    key_drivers.append(f"[{item['type']}] {item['point']}")
                    seen_points.add(item['point'])
            
        except Exception as e:
            logger.error(f"Error extracting key drivers: {e}")
        
        return key_drivers[:5]  # Return top 5
    
    def _identify_risk_factors(self, analyses: List[AnalysisInput], 
                             conflicts: List[ConflictAnalysis]) -> List[str]:
        """Identify risk factors from analyses and conflicts."""
        risk_factors = []
        
        try:
            # Add conflict-based risks
            for conflict in conflicts:
                if conflict.severity in ['high', 'medium']:
                    risk_factors.append(f"Signal conflict: {conflict.conflict_type}")
            
            # Extract risk-related key points
            for analysis in analyses:
                for point in analysis.key_points:
                    if any(risk_word in point.lower() for risk_word in 
                          ['risk', 'danger', 'warning', 'concern', 'volatility', 'uncertainty']):
                        risk_factors.append(point)
            
            # Add general risks based on analysis types
            analysis_types = [a.analysis_type for a in analyses]
            
            if AnalysisType.SENTIMENT in analysis_types:
                risk_factors.append("Sentiment-driven volatility possible")
            
            if AnalysisType.NEWS in analysis_types:
                risk_factors.append("News-related price gaps possible")
            
        except Exception as e:
            logger.error(f"Error identifying risk factors: {e}")
        
        return list(set(risk_factors))[:5]  # Return unique top 5
    
    def _identify_opportunities(self, analyses: List[AnalysisInput]) -> List[str]:
        """Identify opportunities from analyses."""
        opportunities = []
        
        try:
            # Extract opportunity-related key points
            for analysis in analyses:
                for point in analysis.key_points:
                    if any(opp_word in point.lower() for opp_word in 
                          ['opportunity', 'breakout', 'support', 'target', 'bullish', 'strong']):
                        opportunities.append(point)
            
            # Add type-specific opportunities
            analysis_types = [a.analysis_type for a in analyses]
            
            if AnalysisType.PATTERN in analysis_types:
                opportunities.append("Pattern-based trading opportunities")
            
            if AnalysisType.FIBONACCI in analysis_types:
                opportunities.append("Fibonacci level trading opportunities")
            
            if AnalysisType.VOLUME in analysis_types:
                opportunities.append("Volume-confirmed moves")
            
        except Exception as e:
            logger.error(f"Error identifying opportunities: {e}")
        
        return list(set(opportunities))[:5]  # Return unique top 5
    
    def _calculate_price_targets(self, analyses: List[AnalysisInput], 
                               overall_bias: MarketBias) -> Dict[str, float]:
        """Calculate price targets based on analyses."""
        targets = {}
        
        try:
            # Extract target-related data from supporting_data
            target_values = []
            
            for analysis in analyses:
                supporting_data = analysis.supporting_data or {}
                
                # Look for various target fields
                for key in ['target', 'price_target', 'resistance', 'support', 'projection']:
                    if key in supporting_data:
                        value = supporting_data[key]
                        if isinstance(value, (int, float)):
                            target_values.append(value)
                        elif isinstance(value, list):
                            target_values.extend([v for v in value if isinstance(v, (int, float))])
            
            if target_values:
                # Calculate statistical targets
                targets['mean_target'] = statistics.mean(target_values)
                targets['median_target'] = statistics.median(target_values)
                
                if len(target_values) > 1:
                    targets['conservative_target'] = min(target_values)
                    targets['aggressive_target'] = max(target_values)
            
            # Add bias-based targets if we have a current price
            current_prices = []
            for analysis in analyses:
                if 'current_price' in (analysis.supporting_data or {}):
                    current_prices.append(analysis.supporting_data['current_price'])
            
            if current_prices:
                current_price = statistics.mean(current_prices)
                
                if overall_bias == MarketBias.STRONGLY_BULLISH:
                    targets['bias_target'] = current_price * 1.1
                elif overall_bias == MarketBias.BULLISH:
                    targets['bias_target'] = current_price * 1.05
                elif overall_bias == MarketBias.STRONGLY_BEARISH:
                    targets['bias_target'] = current_price * 0.9
                elif overall_bias == MarketBias.BEARISH:
                    targets['bias_target'] = current_price * 0.95
            
        except Exception as e:
            logger.error(f"Error calculating price targets: {e}")
        
        return targets
    
    def _determine_time_horizon(self, analyses: List[AnalysisInput]) -> str:
        """Determine appropriate time horizon for the synthesis."""
        try:
            # Analyze timeframes of input analyses
            timeframes = [a.timeframe for a in analyses]
            
            # Convert timeframes to minutes for comparison
            timeframe_minutes = []
            for tf in timeframes:
                if tf.endswith('m'):
                    timeframe_minutes.append(int(tf[:-1]))
                elif tf.endswith('h'):
                    timeframe_minutes.append(int(tf[:-1]) * 60)
                elif tf.endswith('d'):
                    timeframe_minutes.append(int(tf[:-1]) * 1440)
                elif tf.endswith('w'):
                    timeframe_minutes.append(int(tf[:-1]) * 10080)
                elif tf.endswith('M'):
                    timeframe_minutes.append(int(tf[:-1]) * 43200)
            
            if timeframe_minutes:
                avg_minutes = statistics.mean(timeframe_minutes)
                
                if avg_minutes <= 60:
                    return "short_term"  # Up to 1 hour
                elif avg_minutes <= 1440:
                    return "medium_term"  # Up to 1 day
                else:
                    return "long_term"  # More than 1 day
            
            return "medium_term"  # Default
            
        except Exception as e:
            logger.error(f"Error determining time horizon: {e}")
            return "medium_term"
    
    def _calculate_strength(self, analyses: List[AnalysisInput]) -> float:
        """Calculate overall strength of the synthesized analysis."""
        try:
            if not analyses:
                return 0.0
            
            # Calculate weighted average strength
            total_strength = 0.0
            total_weight = 0.0
            
            for analysis in analyses:
                weighted_strength = analysis.strength * analysis.confidence * analysis.weight
                total_strength += weighted_strength
                total_weight += analysis.confidence * analysis.weight
            
            if total_weight > 0:
                return min(1.0, total_strength / total_weight)
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating strength: {e}")
            return 0.0
    
    def _get_analysis_distribution(self, analyses: List[AnalysisInput]) -> Dict[str, int]:
        """Get distribution of analysis types."""
        distribution = defaultdict(int)
        
        try:
            for analysis in analyses:
                distribution[analysis.analysis_type.value] += 1
        
        except Exception as e:
            logger.error(f"Error getting analysis distribution: {e}")
        
        return dict(distribution)
    
    def _create_empty_insight(self, symbol: str, timeframe: str) -> SynthesizedInsight:
        """Create an empty insight when synthesis fails."""
        return SynthesizedInsight(
            insight_id=f"empty_{symbol}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            symbol=symbol,
            timeframe=timeframe,
            overall_bias=MarketBias.NEUTRAL,
            confidence=ConfidenceLevel.VERY_LOW,
            strength=0.0,
            consensus_score=0.0,
            key_drivers=[],
            conflicting_signals=[],
            risk_factors=["Insufficient analysis data"],
            opportunities=[],
            price_targets={},
            time_horizon="unknown",
            supporting_analyses=[],
            synthesis_timestamp=datetime.now(),
            metadata={'error': 'No valid analyses provided'}
        )
    
    def create_analysis_input(self, analysis_type: str, symbol: str, timeframe: str,
                            confidence: float, bias: str, strength: float,
                            key_points: List[str], supporting_data: Dict[str, Any] = None) -> AnalysisInput:
        """Helper method to create AnalysisInput objects."""
        try:
            # Convert string to enum
            analysis_type_enum = AnalysisType(analysis_type.lower())
            
            return AnalysisInput(
                analysis_type=analysis_type_enum,
                symbol=symbol,
                timeframe=timeframe,
                timestamp=datetime.now(),
                confidence=confidence,
                bias=bias,
                strength=strength,
                key_points=key_points,
                supporting_data=supporting_data or {}
            )
            
        except ValueError:
            logger.error(f"Invalid analysis type: {analysis_type}")
            # Default to technical analysis
            return AnalysisInput(
                analysis_type=AnalysisType.TECHNICAL,
                symbol=symbol,
                timeframe=timeframe,
                timestamp=datetime.now(),
                confidence=confidence,
                bias=bias,
                strength=strength,
                key_points=key_points,
                supporting_data=supporting_data or {}
            )
    
    def get_synthesis_summary(self, insight: SynthesizedInsight) -> Dict[str, Any]:
        """Get a summary of the synthesized insight."""
        try:
            return {
                'symbol': insight.symbol,
                'timeframe': insight.timeframe,
                'overall_bias': insight.overall_bias.value,
                'confidence': insight.confidence.value,
                'strength': insight.strength,
                'consensus_score': insight.consensus_score,
                'key_drivers_count': len(insight.key_drivers),
                'conflicts_count': len(insight.conflicting_signals),
                'risk_factors_count': len(insight.risk_factors),
                'opportunities_count': len(insight.opportunities),
                'price_targets_count': len(insight.price_targets),
                'time_horizon': insight.time_horizon,
                'supporting_analyses_count': len(insight.supporting_analyses),
                'synthesis_timestamp': insight.synthesis_timestamp.isoformat()
            }
            
        except Exception as e:
            logger.error(f"Error creating synthesis summary: {e}")
            return {}