#!/usr/bin/env python3
"""
Decision Framework
Advanced decision-making framework for trading and analysis decisions.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime, timed<PERSON>ta
from collections import defaultdict
import statistics

logger = logging.getLogger(__name__)

class DecisionType(Enum):
    """Types of decisions that can be made."""
    TRADE_ENTRY = "trade_entry"
    TRADE_EXIT = "trade_exit"
    POSITION_SIZE = "position_size"
    RISK_MANAGEMENT = "risk_management"
    PORTFOLIO_ALLOCATION = "portfolio_allocation"
    MARKET_TIMING = "market_timing"
    STRATEGY_SELECTION = "strategy_selection"
    ALERT_GENERATION = "alert_generation"

class DecisionConfidence(Enum):
    """Confidence levels for decisions."""
    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"

class DecisionUrgency(Enum):
    """Urgency levels for decisions."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class RiskLevel(Enum):
    """Risk levels for decisions."""
    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"

@dataclass
class DecisionCriteria:
    """Criteria for making decisions."""
    name: str
    weight: float
    threshold: float
    current_value: float
    is_met: bool
    importance: str  # 'critical', 'important', 'nice_to_have'
    description: str

@dataclass
class DecisionInput:
    """Input data for decision making."""
    input_id: str
    source: str
    data_type: str
    value: Any
    confidence: float
    timestamp: datetime
    weight: float = 1.0
    metadata: Dict[str, Any] = None

@dataclass
class DecisionOption:
    """A possible decision option."""
    option_id: str
    name: str
    description: str
    expected_outcome: str
    probability_success: float
    potential_reward: float
    potential_risk: float
    risk_reward_ratio: float
    required_resources: Dict[str, Any]
    constraints: List[str]
    pros: List[str]
    cons: List[str]
    score: float = 0.0

@dataclass
class DecisionResult:
    """Result of a decision-making process."""
    decision_id: str
    decision_type: DecisionType
    symbol: str
    recommended_option: DecisionOption
    alternative_options: List[DecisionOption]
    confidence: DecisionConfidence
    urgency: DecisionUrgency
    risk_level: RiskLevel
    reasoning: List[str]
    supporting_criteria: List[DecisionCriteria]
    conflicting_criteria: List[DecisionCriteria]
    risk_factors: List[str]
    mitigation_strategies: List[str]
    expected_timeline: str
    review_triggers: List[str]
    decision_timestamp: datetime
    metadata: Dict[str, Any] = None

class DecisionFramework:
    """Advanced decision-making framework."""
    
    def __init__(self):
        self.decision_weights = {
            DecisionType.TRADE_ENTRY: {
                'technical_analysis': 0.3,
                'fundamental_analysis': 0.2,
                'risk_management': 0.25,
                'market_conditions': 0.15,
                'sentiment': 0.1
            },
            DecisionType.TRADE_EXIT: {
                'profit_target': 0.25,
                'stop_loss': 0.3,
                'technical_signals': 0.2,
                'market_conditions': 0.15,
                'time_decay': 0.1
            },
            DecisionType.POSITION_SIZE: {
                'risk_tolerance': 0.4,
                'account_size': 0.2,
                'volatility': 0.2,
                'correlation': 0.1,
                'confidence': 0.1
            },
            DecisionType.RISK_MANAGEMENT: {
                'portfolio_risk': 0.3,
                'individual_risk': 0.25,
                'correlation_risk': 0.2,
                'market_risk': 0.15,
                'liquidity_risk': 0.1
            }
        }
        
        self.confidence_thresholds = {
            DecisionConfidence.VERY_HIGH: 0.9,
            DecisionConfidence.HIGH: 0.75,
            DecisionConfidence.MODERATE: 0.6,
            DecisionConfidence.LOW: 0.4,
            DecisionConfidence.VERY_LOW: 0.0
        }
        
        self.risk_thresholds = {
            RiskLevel.VERY_LOW: 0.1,
            RiskLevel.LOW: 0.25,
            RiskLevel.MODERATE: 0.5,
            RiskLevel.HIGH: 0.75,
            RiskLevel.VERY_HIGH: 1.0
        }
    
    def make_decision(self, decision_type: DecisionType, symbol: str,
                     inputs: List[DecisionInput], options: List[DecisionOption],
                     criteria: List[DecisionCriteria] = None) -> DecisionResult:
        """Make a decision based on inputs, options, and criteria."""
        try:
            if not options:
                logger.warning("No decision options provided")
                return self._create_empty_decision(decision_type, symbol)
            
            # Evaluate each option
            evaluated_options = self._evaluate_options(decision_type, options, inputs, criteria)
            
            # Select the best option
            best_option = max(evaluated_options, key=lambda x: x.score)
            alternative_options = [opt for opt in evaluated_options if opt.option_id != best_option.option_id]
            
            # Calculate confidence
            confidence = self._calculate_decision_confidence(best_option, evaluated_options, inputs)
            
            # Determine urgency
            urgency = self._determine_urgency(decision_type, inputs, best_option)
            
            # Assess risk level
            risk_level = self._assess_risk_level(best_option, inputs)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(best_option, inputs, criteria)
            
            # Identify supporting and conflicting criteria
            supporting_criteria, conflicting_criteria = self._analyze_criteria(criteria or [])
            
            # Identify risk factors and mitigation strategies
            risk_factors = self._identify_risk_factors(best_option, inputs)
            mitigation_strategies = self._generate_mitigation_strategies(risk_factors, best_option)
            
            # Determine expected timeline
            expected_timeline = self._determine_timeline(decision_type, best_option)
            
            # Set review triggers
            review_triggers = self._set_review_triggers(decision_type, best_option, inputs)
            
            # Create decision result
            decision_result = DecisionResult(
                decision_id=f"decision_{symbol}_{decision_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                decision_type=decision_type,
                symbol=symbol,
                recommended_option=best_option,
                alternative_options=alternative_options,
                confidence=confidence,
                urgency=urgency,
                risk_level=risk_level,
                reasoning=reasoning,
                supporting_criteria=supporting_criteria,
                conflicting_criteria=conflicting_criteria,
                risk_factors=risk_factors,
                mitigation_strategies=mitigation_strategies,
                expected_timeline=expected_timeline,
                review_triggers=review_triggers,
                decision_timestamp=datetime.now(),
                metadata={
                    'total_options': len(options),
                    'total_inputs': len(inputs),
                    'criteria_count': len(criteria or []),
                    'best_option_score': best_option.score
                }
            )
            
            return decision_result
            
        except Exception as e:
            logger.error(f"Error making decision: {e}")
            return self._create_empty_decision(decision_type, symbol)
    
    def _evaluate_options(self, decision_type: DecisionType, options: List[DecisionOption],
                         inputs: List[DecisionInput], criteria: List[DecisionCriteria]) -> List[DecisionOption]:
        """Evaluate all decision options."""
        evaluated_options = []
        
        try:
            weights = self.decision_weights.get(decision_type, {})
            
            for option in options:
                # Calculate base score
                base_score = self._calculate_base_score(option)
                
                # Apply input-based scoring
                input_score = self._calculate_input_score(option, inputs, weights)
                
                # Apply criteria-based scoring
                criteria_score = self._calculate_criteria_score(option, criteria or [])
                
                # Calculate risk-adjusted score
                risk_adjustment = self._calculate_risk_adjustment(option)
                
                # Combine scores
                final_score = (base_score * 0.3 + input_score * 0.4 + 
                             criteria_score * 0.2 + risk_adjustment * 0.1)
                
                # Create evaluated option
                evaluated_option = DecisionOption(
                    option_id=option.option_id,
                    name=option.name,
                    description=option.description,
                    expected_outcome=option.expected_outcome,
                    probability_success=option.probability_success,
                    potential_reward=option.potential_reward,
                    potential_risk=option.potential_risk,
                    risk_reward_ratio=option.risk_reward_ratio,
                    required_resources=option.required_resources,
                    constraints=option.constraints,
                    pros=option.pros,
                    cons=option.cons,
                    score=final_score
                )
                
                evaluated_options.append(evaluated_option)
        
        except Exception as e:
            logger.error(f"Error evaluating options: {e}")
            return options
        
        return evaluated_options
    
    def _calculate_base_score(self, option: DecisionOption) -> float:
        """Calculate base score for an option."""
        try:
            # Combine probability of success, risk-reward ratio, and other factors
            prob_score = option.probability_success
            rr_score = min(1.0, option.risk_reward_ratio / 3.0)  # Normalize to 0-1
            
            # Penalize high risk
            risk_penalty = max(0.0, 1.0 - option.potential_risk)
            
            base_score = (prob_score * 0.4 + rr_score * 0.4 + risk_penalty * 0.2)
            
            return min(1.0, max(0.0, base_score))
            
        except Exception as e:
            logger.error(f"Error calculating base score: {e}")
            return 0.5
    
    def _calculate_input_score(self, option: DecisionOption, inputs: List[DecisionInput],
                             weights: Dict[str, float]) -> float:
        """Calculate score based on decision inputs."""
        try:
            if not inputs:
                return 0.5
            
            total_score = 0.0
            total_weight = 0.0
            
            for input_data in inputs:
                # Get weight for this input type
                input_weight = weights.get(input_data.data_type, 0.1)
                
                # Calculate input score based on value and confidence
                if isinstance(input_data.value, (int, float)):
                    # Normalize numeric values
                    normalized_value = min(1.0, max(0.0, input_data.value))
                    input_score = normalized_value * input_data.confidence
                elif isinstance(input_data.value, bool):
                    input_score = 1.0 if input_data.value else 0.0
                elif isinstance(input_data.value, str):
                    # Simple string-based scoring
                    positive_words = ['bullish', 'positive', 'strong', 'good', 'buy']
                    negative_words = ['bearish', 'negative', 'weak', 'bad', 'sell']
                    
                    if any(word in input_data.value.lower() for word in positive_words):
                        input_score = 0.8 * input_data.confidence
                    elif any(word in input_data.value.lower() for word in negative_words):
                        input_score = 0.2 * input_data.confidence
                    else:
                        input_score = 0.5 * input_data.confidence
                else:
                    input_score = 0.5 * input_data.confidence
                
                # Weight the score
                weighted_score = input_score * input_weight * input_data.weight
                total_score += weighted_score
                total_weight += input_weight * input_data.weight
            
            if total_weight > 0:
                return total_score / total_weight
            else:
                return 0.5
                
        except Exception as e:
            logger.error(f"Error calculating input score: {e}")
            return 0.5
    
    def _calculate_criteria_score(self, option: DecisionOption, criteria: List[DecisionCriteria]) -> float:
        """Calculate score based on decision criteria."""
        try:
            if not criteria:
                return 0.5
            
            total_score = 0.0
            total_weight = 0.0
            
            for criterion in criteria:
                # Score based on whether criterion is met
                criterion_score = 1.0 if criterion.is_met else 0.0
                
                # Adjust for importance
                importance_multiplier = {
                    'critical': 1.5,
                    'important': 1.0,
                    'nice_to_have': 0.5
                }.get(criterion.importance, 1.0)
                
                weighted_score = criterion_score * criterion.weight * importance_multiplier
                total_score += weighted_score
                total_weight += criterion.weight * importance_multiplier
            
            if total_weight > 0:
                return total_score / total_weight
            else:
                return 0.5
                
        except Exception as e:
            logger.error(f"Error calculating criteria score: {e}")
            return 0.5
    
    def _calculate_risk_adjustment(self, option: DecisionOption) -> float:
        """Calculate risk adjustment factor."""
        try:
            # Higher risk = lower adjustment
            risk_factor = 1.0 - option.potential_risk
            
            # Bonus for good risk-reward ratio
            rr_bonus = min(0.2, option.risk_reward_ratio / 10.0)
            
            return min(1.0, max(0.0, risk_factor + rr_bonus))
            
        except Exception as e:
            logger.error(f"Error calculating risk adjustment: {e}")
            return 0.5
    
    def _calculate_decision_confidence(self, best_option: DecisionOption,
                                     all_options: List[DecisionOption],
                                     inputs: List[DecisionInput]) -> DecisionConfidence:
        """Calculate confidence in the decision."""
        try:
            # Base confidence from option score
            base_confidence = best_option.score
            
            # Adjust for option separation (how much better is the best option)
            if len(all_options) > 1:
                scores = [opt.score for opt in all_options]
                scores.sort(reverse=True)
                separation = scores[0] - scores[1] if len(scores) > 1 else 0.0
                separation_bonus = min(0.2, separation)
            else:
                separation_bonus = 0.0
            
            # Adjust for input confidence
            if inputs:
                avg_input_confidence = statistics.mean([inp.confidence for inp in inputs])
                input_adjustment = (avg_input_confidence - 0.5) * 0.2
            else:
                input_adjustment = 0.0
            
            # Calculate final confidence
            final_confidence = base_confidence + separation_bonus + input_adjustment
            final_confidence = min(1.0, max(0.0, final_confidence))
            
            # Convert to enum
            for conf_level, threshold in sorted(self.confidence_thresholds.items(), 
                                              key=lambda x: x[1], reverse=True):
                if final_confidence >= threshold:
                    return conf_level
            
            return DecisionConfidence.VERY_LOW
            
        except Exception as e:
            logger.error(f"Error calculating decision confidence: {e}")
            return DecisionConfidence.LOW
    
    def _determine_urgency(self, decision_type: DecisionType, inputs: List[DecisionInput],
                          option: DecisionOption) -> DecisionUrgency:
        """Determine urgency of the decision."""
        try:
            # Base urgency by decision type
            type_urgency = {
                DecisionType.TRADE_ENTRY: DecisionUrgency.HIGH,
                DecisionType.TRADE_EXIT: DecisionUrgency.CRITICAL,
                DecisionType.POSITION_SIZE: DecisionUrgency.MEDIUM,
                DecisionType.RISK_MANAGEMENT: DecisionUrgency.HIGH,
                DecisionType.PORTFOLIO_ALLOCATION: DecisionUrgency.LOW,
                DecisionType.MARKET_TIMING: DecisionUrgency.HIGH,
                DecisionType.STRATEGY_SELECTION: DecisionUrgency.MEDIUM,
                DecisionType.ALERT_GENERATION: DecisionUrgency.MEDIUM
            }.get(decision_type, DecisionUrgency.MEDIUM)
            
            # Adjust based on inputs
            urgency_indicators = 0
            for input_data in inputs:
                if 'urgent' in str(input_data.value).lower():
                    urgency_indicators += 1
                elif 'critical' in str(input_data.value).lower():
                    urgency_indicators += 2
                elif 'immediate' in str(input_data.value).lower():
                    urgency_indicators += 2
            
            # Adjust urgency based on indicators
            if urgency_indicators >= 2:
                return DecisionUrgency.CRITICAL
            elif urgency_indicators >= 1:
                if type_urgency in [DecisionUrgency.LOW, DecisionUrgency.MEDIUM]:
                    return DecisionUrgency.HIGH
            
            return type_urgency
            
        except Exception as e:
            logger.error(f"Error determining urgency: {e}")
            return DecisionUrgency.MEDIUM
    
    def _assess_risk_level(self, option: DecisionOption, inputs: List[DecisionInput]) -> RiskLevel:
        """Assess risk level of the decision."""
        try:
            # Base risk from option
            base_risk = option.potential_risk
            
            # Adjust for input-based risk factors
            risk_inputs = [inp for inp in inputs if 'risk' in inp.data_type.lower()]
            if risk_inputs:
                avg_risk_input = statistics.mean([inp.value if isinstance(inp.value, (int, float)) else 0.5 
                                                 for inp in risk_inputs])
                risk_adjustment = (avg_risk_input - 0.5) * 0.3
            else:
                risk_adjustment = 0.0
            
            # Calculate final risk
            final_risk = base_risk + risk_adjustment
            final_risk = min(1.0, max(0.0, final_risk))
            
            # Convert to enum
            for risk_level, threshold in sorted(self.risk_thresholds.items(), 
                                              key=lambda x: x[1], reverse=True):
                if final_risk >= threshold:
                    return risk_level
            
            return RiskLevel.VERY_LOW
            
        except Exception as e:
            logger.error(f"Error assessing risk level: {e}")
            return RiskLevel.MODERATE
    
    def _generate_reasoning(self, option: DecisionOption, inputs: List[DecisionInput],
                          criteria: List[DecisionCriteria]) -> List[str]:
        """Generate reasoning for the decision."""
        reasoning = []
        
        try:
            # Add option-based reasoning
            reasoning.append(f"Selected '{option.name}' with score {option.score:.2f}")
            reasoning.append(f"Expected outcome: {option.expected_outcome}")
            reasoning.append(f"Success probability: {option.probability_success:.1%}")
            reasoning.append(f"Risk-reward ratio: {option.risk_reward_ratio:.2f}")
            
            # Add top pros
            if option.pros:
                reasoning.append(f"Key advantages: {', '.join(option.pros[:3])}")
            
            # Add input-based reasoning
            high_confidence_inputs = [inp for inp in inputs if inp.confidence > 0.7]
            if high_confidence_inputs:
                reasoning.append(f"Supported by {len(high_confidence_inputs)} high-confidence inputs")
            
            # Add criteria-based reasoning
            if criteria:
                met_criteria = [c for c in criteria if c.is_met]
                reasoning.append(f"Meets {len(met_criteria)}/{len(criteria)} decision criteria")
            
        except Exception as e:
            logger.error(f"Error generating reasoning: {e}")
        
        return reasoning[:5]  # Return top 5 reasons
    
    def _analyze_criteria(self, criteria: List[DecisionCriteria]) -> Tuple[List[DecisionCriteria], List[DecisionCriteria]]:
        """Analyze supporting and conflicting criteria."""
        supporting = []
        conflicting = []
        
        try:
            for criterion in criteria:
                if criterion.is_met:
                    supporting.append(criterion)
                else:
                    conflicting.append(criterion)
        
        except Exception as e:
            logger.error(f"Error analyzing criteria: {e}")
        
        return supporting, conflicting
    
    def _identify_risk_factors(self, option: DecisionOption, inputs: List[DecisionInput]) -> List[str]:
        """Identify risk factors for the decision."""
        risk_factors = []
        
        try:
            # Add option-specific risks
            if option.potential_risk > 0.7:
                risk_factors.append("High potential risk identified")
            
            if option.cons:
                risk_factors.extend([f"Risk: {con}" for con in option.cons[:3]])
            
            # Add input-based risks
            risk_inputs = [inp for inp in inputs if 'risk' in inp.data_type.lower() or 
                          'volatility' in inp.data_type.lower()]
            
            for risk_input in risk_inputs:
                if isinstance(risk_input.value, (int, float)) and risk_input.value > 0.6:
                    risk_factors.append(f"Elevated {risk_input.data_type}: {risk_input.value:.2f}")
            
            # Add general risks
            if option.risk_reward_ratio < 1.0:
                risk_factors.append("Unfavorable risk-reward ratio")
            
            if option.probability_success < 0.5:
                risk_factors.append("Low probability of success")
        
        except Exception as e:
            logger.error(f"Error identifying risk factors: {e}")
        
        return risk_factors[:5]  # Return top 5 risks
    
    def _generate_mitigation_strategies(self, risk_factors: List[str], option: DecisionOption) -> List[str]:
        """Generate mitigation strategies for identified risks."""
        strategies = []
        
        try:
            # Generic mitigation strategies
            if risk_factors:
                strategies.append("Implement strict stop-loss levels")
                strategies.append("Monitor position closely")
                strategies.append("Consider reduced position size")
            
            # Specific strategies based on risk factors
            for risk in risk_factors:
                if 'volatility' in risk.lower():
                    strategies.append("Use wider stop-losses for volatility")
                elif 'risk-reward' in risk.lower():
                    strategies.append("Reassess entry and exit points")
                elif 'probability' in risk.lower():
                    strategies.append("Wait for better setup confirmation")
            
            # Option-specific strategies
            if option.potential_risk > 0.5:
                strategies.append("Consider hedging strategies")
        
        except Exception as e:
            logger.error(f"Error generating mitigation strategies: {e}")
        
        return list(set(strategies))[:5]  # Return unique top 5 strategies
    
    def _determine_timeline(self, decision_type: DecisionType, option: DecisionOption) -> str:
        """Determine expected timeline for the decision."""
        try:
            # Base timeline by decision type
            type_timelines = {
                DecisionType.TRADE_ENTRY: "immediate",
                DecisionType.TRADE_EXIT: "immediate",
                DecisionType.POSITION_SIZE: "within_hour",
                DecisionType.RISK_MANAGEMENT: "within_day",
                DecisionType.PORTFOLIO_ALLOCATION: "within_week",
                DecisionType.MARKET_TIMING: "within_day",
                DecisionType.STRATEGY_SELECTION: "within_week",
                DecisionType.ALERT_GENERATION: "immediate"
            }
            
            return type_timelines.get(decision_type, "within_day")
            
        except Exception as e:
            logger.error(f"Error determining timeline: {e}")
            return "within_day"
    
    def _set_review_triggers(self, decision_type: DecisionType, option: DecisionOption,
                           inputs: List[DecisionInput]) -> List[str]:
        """Set triggers for reviewing the decision."""
        triggers = []
        
        try:
            # Common triggers
            triggers.append("Significant price movement (>5%)")
            triggers.append("Change in market conditions")
            triggers.append("New conflicting signals")
            
            # Decision-type specific triggers
            if decision_type in [DecisionType.TRADE_ENTRY, DecisionType.TRADE_EXIT]:
                triggers.append("Stop-loss or take-profit hit")
                triggers.append("Technical pattern invalidation")
            
            elif decision_type == DecisionType.RISK_MANAGEMENT:
                triggers.append("Portfolio risk exceeds limits")
                triggers.append("Correlation changes significantly")
            
            # Option-specific triggers
            if option.potential_risk > 0.7:
                triggers.append("Risk metrics deteriorate further")
        
        except Exception as e:
            logger.error(f"Error setting review triggers: {e}")
        
        return triggers[:5]  # Return top 5 triggers
    
    def _create_empty_decision(self, decision_type: DecisionType, symbol: str) -> DecisionResult:
        """Create an empty decision when decision making fails."""
        empty_option = DecisionOption(
            option_id="empty_option",
            name="No Action",
            description="No suitable decision option available",
            expected_outcome="Maintain current position",
            probability_success=0.5,
            potential_reward=0.0,
            potential_risk=0.0,
            risk_reward_ratio=0.0,
            required_resources={},
            constraints=[],
            pros=[],
            cons=["Insufficient data for decision"],
            score=0.0
        )
        
        return DecisionResult(
            decision_id=f"empty_{symbol}_{decision_type.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            decision_type=decision_type,
            symbol=symbol,
            recommended_option=empty_option,
            alternative_options=[],
            confidence=DecisionConfidence.VERY_LOW,
            urgency=DecisionUrgency.LOW,
            risk_level=RiskLevel.MODERATE,
            reasoning=["Insufficient data for informed decision"],
            supporting_criteria=[],
            conflicting_criteria=[],
            risk_factors=["Lack of sufficient analysis data"],
            mitigation_strategies=["Gather more data before proceeding"],
            expected_timeline="pending",
            review_triggers=["New data becomes available"],
            decision_timestamp=datetime.now(),
            metadata={'error': 'No valid decision options provided'}
        )
    
    def create_decision_input(self, input_id: str, source: str, data_type: str,
                            value: Any, confidence: float) -> DecisionInput:
        """Helper method to create DecisionInput objects."""
        return DecisionInput(
            input_id=input_id,
            source=source,
            data_type=data_type,
            value=value,
            confidence=confidence,
            timestamp=datetime.now()
        )
    
    def create_decision_option(self, option_id: str, name: str, description: str,
                             expected_outcome: str, probability_success: float,
                             potential_reward: float, potential_risk: float,
                             pros: List[str] = None, cons: List[str] = None) -> DecisionOption:
        """Helper method to create DecisionOption objects."""
        risk_reward_ratio = potential_reward / max(potential_risk, 0.01)
        
        return DecisionOption(
            option_id=option_id,
            name=name,
            description=description,
            expected_outcome=expected_outcome,
            probability_success=probability_success,
            potential_reward=potential_reward,
            potential_risk=potential_risk,
            risk_reward_ratio=risk_reward_ratio,
            required_resources={},
            constraints=[],
            pros=pros or [],
            cons=cons or []
        )
    
    def create_decision_criteria(self, name: str, weight: float, threshold: float,
                               current_value: float, importance: str = "important",
                               description: str = "") -> DecisionCriteria:
        """Helper method to create DecisionCriteria objects."""
        is_met = current_value >= threshold
        
        return DecisionCriteria(
            name=name,
            weight=weight,
            threshold=threshold,
            current_value=current_value,
            is_met=is_met,
            importance=importance,
            description=description
        )