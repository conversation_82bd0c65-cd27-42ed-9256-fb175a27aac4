#!/usr/bin/env python3
"""
Event Detector
Detects and classifies market events from news and data.
"""

import re
from typing import Dict, List, Tuple, Optional, Set
from enum import Enum
from dataclasses import dataclass
from datetime import datetime
import logging

logger = logging.getLogger(__name__)

class EventType(Enum):
    """Types of market events."""
    EARNINGS = "earnings"
    MERGER_ACQUISITION = "merger_acquisition"
    REGULATORY = "regulatory"
    ECONOMIC_DATA = "economic_data"
    CENTRAL_BANK = "central_bank"
    GEOPOLITICAL = "geopolitical"
    CORPORATE_ACTION = "corporate_action"
    MARKET_STRUCTURE = "market_structure"
    TECHNOLOGY = "technology"
    COMMODITY = "commodity"
    CURRENCY = "currency"
    CRYPTO = "crypto"
    IPO = "ipo"
    BANKRUPTCY = "bankruptcy"
    ANALYST_RATING = "analyst_rating"
    PRODUCT_LAUNCH = "product_launch"
    PARTNERSHIP = "partnership"
    LEGAL = "legal"
    ENVIRONMENTAL = "environmental"
    OTHER = "other"

class EventSeverity(Enum):
    """Severity levels for market events."""
    LOW = 1
    MEDIUM = 2
    HIGH = 3
    CRITICAL = 4

@dataclass
class DetectedEvent:
    """A detected market event."""
    event_type: EventType
    severity: EventSeverity
    confidence: float  # 0.0 to 1.0
    title: str
    description: str
    entities: List[str]  # Companies, currencies, etc.
    keywords: List[str]
    timestamp: datetime
    source: str
    market_impact_score: float  # 0.0 to 1.0

class EventDetector:
    """Detects and classifies market events from text."""
    
    def __init__(self):
        # Event type patterns
        self.event_patterns = {
            EventType.EARNINGS: {
                'keywords': {'earnings', 'quarterly', 'revenue', 'profit', 'eps', 'guidance', 'forecast'},
                'patterns': [r'\bearnings\b', r'\bq[1-4]\b', r'\beps\b', r'\brevenue\b']
            },
            EventType.MERGER_ACQUISITION: {
                'keywords': {'merger', 'acquisition', 'buyout', 'takeover', 'acquire', 'merge'},
                'patterns': [r'\bacquir\w+', r'\bmerger?\b', r'\btakeover\b', r'\bbuyout\b']
            },
            EventType.REGULATORY: {
                'keywords': {'regulation', 'regulatory', 'sec', 'fda', 'approval', 'compliance', 'investigation'},
                'patterns': [r'\bsec\b', r'\bfda\b', r'\bregulat\w+', r'\bapproval\b']
            },
            EventType.ECONOMIC_DATA: {
                'keywords': {'gdp', 'inflation', 'unemployment', 'cpi', 'ppi', 'jobs', 'employment'},
                'patterns': [r'\bgdp\b', r'\binflation\b', r'\bunemployment\b', r'\bcpi\b']
            },
            EventType.CENTRAL_BANK: {
                'keywords': {'fed', 'federal reserve', 'interest rate', 'monetary policy', 'fomc', 'ecb', 'boe'},
                'patterns': [r'\bfed\b', r'\bfomc\b', r'\binterest rate\b', r'\bmonetary\b']
            },
            EventType.GEOPOLITICAL: {
                'keywords': {'war', 'conflict', 'sanctions', 'trade war', 'tariff', 'election', 'political'},
                'patterns': [r'\bwar\b', r'\bsanctions\b', r'\btariff\b', r'\belection\b']
            },
            EventType.CORPORATE_ACTION: {
                'keywords': {'dividend', 'split', 'spinoff', 'buyback', 'repurchase', 'restructuring'},
                'patterns': [r'\bdividend\b', r'\bsplit\b', r'\bbuyback\b', r'\brepurchase\b']
            },
            EventType.IPO: {
                'keywords': {'ipo', 'initial public offering', 'public offering', 'listing', 'debut'},
                'patterns': [r'\bipo\b', r'\bpublic offering\b', r'\blisting\b', r'\bdebut\b']
            },
            EventType.BANKRUPTCY: {
                'keywords': {'bankruptcy', 'chapter 11', 'insolvency', 'liquidation', 'restructuring'},
                'patterns': [r'\bbankruptcy\b', r'\bchapter 11\b', r'\binsolven\w+', r'\bliquidation\b']
            },
            EventType.ANALYST_RATING: {
                'keywords': {'upgrade', 'downgrade', 'rating', 'target price', 'analyst', 'recommendation'},
                'patterns': [r'\bupgrade\b', r'\bdowngrade\b', r'\btarget price\b', r'\banalyst\b']
            },
            EventType.CRYPTO: {
                'keywords': {'bitcoin', 'ethereum', 'crypto', 'blockchain', 'defi', 'nft', 'mining'},
                'patterns': [r'\bbitcoin\b', r'\bethereum\b', r'\bcrypto\b', r'\bblockchain\b']
            }
        }
        
        # Severity indicators
        self.severity_keywords = {
            EventSeverity.CRITICAL: {'crash', 'collapse', 'crisis', 'emergency', 'halt', 'suspend'},
            EventSeverity.HIGH: {'surge', 'plunge', 'soar', 'tumble', 'spike', 'major', 'significant'},
            EventSeverity.MEDIUM: {'rise', 'fall', 'increase', 'decrease', 'moderate', 'notable'},
            EventSeverity.LOW: {'slight', 'minor', 'small', 'modest', 'gradual'}
        }
        
        # Market impact indicators
        self.impact_keywords = {
            'high': {'market', 'global', 'sector', 'industry', 'widespread', 'systemic'},
            'medium': {'company', 'stock', 'share', 'price', 'trading', 'volume'},
            'low': {'local', 'specific', 'individual', 'isolated'}
        }
        
        # Entity extraction patterns
        self.entity_patterns = {
            'company': r'\b[A-Z][a-zA-Z&\s]+(?:Inc|Corp|Ltd|LLC|Co)\b',
            'ticker': r'\b[A-Z]{1,5}\b(?=\s|$|[^a-zA-Z])',
            'currency': r'\b(?:USD|EUR|GBP|JPY|CHF|CAD|AUD|NZD|BTC|ETH)\b',
            'percentage': r'\b\d+(?:\.\d+)?%\b',
            'money': r'\$\d+(?:,\d{3})*(?:\.\d{2})?(?:\s?(?:million|billion|trillion))?\b'
        }
    
    def detect_events(self, text: str, source: str = "unknown") -> List[DetectedEvent]:
        """Detect events from given text."""
        try:
            events = []
            text_lower = text.lower()
            
            # Check each event type
            for event_type, config in self.event_patterns.items():
                confidence = self._calculate_event_confidence(text_lower, config)
                
                if confidence > 0.3:  # Threshold for event detection
                    severity = self._determine_severity(text_lower)
                    entities = self._extract_entities(text)
                    keywords = self._extract_matching_keywords(text_lower, config['keywords'])
                    market_impact = self._calculate_market_impact(text_lower)
                    
                    event = DetectedEvent(
                        event_type=event_type,
                        severity=severity,
                        confidence=confidence,
                        title=self._generate_title(text, event_type),
                        description=text[:500] + "..." if len(text) > 500 else text,
                        entities=entities,
                        keywords=keywords,
                        timestamp=datetime.now(),
                        source=source,
                        market_impact_score=market_impact
                    )
                    events.append(event)
            
            # Sort by confidence and return top events
            events.sort(key=lambda x: x.confidence, reverse=True)
            return events[:5]  # Return top 5 events
            
        except Exception as e:
            logger.error(f"Error detecting events: {e}")
            return []
    
    def _calculate_event_confidence(self, text: str, config: Dict) -> float:
        """Calculate confidence score for event type."""
        keyword_score = 0.0
        pattern_score = 0.0
        
        # Check keywords
        words = set(text.split())
        matching_keywords = words.intersection(config['keywords'])
        keyword_score = len(matching_keywords) / len(config['keywords'])
        
        # Check patterns
        pattern_matches = 0
        for pattern in config['patterns']:
            if re.search(pattern, text, re.IGNORECASE):
                pattern_matches += 1
        pattern_score = pattern_matches / len(config['patterns'])
        
        # Combined score
        confidence = (keyword_score * 0.6 + pattern_score * 0.4)
        return min(1.0, confidence)
    
    def _determine_severity(self, text: str) -> EventSeverity:
        """Determine event severity based on text content."""
        words = set(text.split())
        
        for severity, keywords in self.severity_keywords.items():
            if words.intersection(keywords):
                return severity
        
        return EventSeverity.MEDIUM  # Default severity
    
    def _extract_entities(self, text: str) -> List[str]:
        """Extract entities (companies, tickers, etc.) from text."""
        entities = []
        
        for entity_type, pattern in self.entity_patterns.items():
            matches = re.findall(pattern, text)
            entities.extend(matches)
        
        return list(set(entities))  # Remove duplicates
    
    def _extract_matching_keywords(self, text: str, keywords: Set[str]) -> List[str]:
        """Extract keywords that match the event type."""
        words = set(text.split())
        return list(words.intersection(keywords))
    
    def _calculate_market_impact(self, text: str) -> float:
        """Calculate potential market impact score."""
        words = set(text.split())
        impact_score = 0.0
        
        for impact_level, keywords in self.impact_keywords.items():
            matches = len(words.intersection(keywords))
            if impact_level == 'high':
                impact_score += matches * 0.8
            elif impact_level == 'medium':
                impact_score += matches * 0.5
            else:  # low
                impact_score += matches * 0.2
        
        return min(1.0, impact_score / 10)  # Normalize to 0-1
    
    def _generate_title(self, text: str, event_type: EventType) -> str:
        """Generate a title for the detected event."""
        # Take first sentence or first 100 characters
        sentences = text.split('. ')
        if sentences:
            title = sentences[0]
        else:
            title = text[:100]
        
        # Clean up title
        title = title.strip()
        if not title.endswith('.'):
            title += '...'
        
        return title
    
    def filter_events_by_type(self, events: List[DetectedEvent], event_types: List[EventType]) -> List[DetectedEvent]:
        """Filter events by specific types."""
        return [event for event in events if event.event_type in event_types]
    
    def filter_events_by_severity(self, events: List[DetectedEvent], min_severity: EventSeverity) -> List[DetectedEvent]:
        """Filter events by minimum severity."""
        return [event for event in events if event.severity.value >= min_severity.value]
    
    def filter_events_by_confidence(self, events: List[DetectedEvent], min_confidence: float) -> List[DetectedEvent]:
        """Filter events by minimum confidence."""
        return [event for event in events if event.confidence >= min_confidence]
    
    def get_event_summary(self, events: List[DetectedEvent]) -> Dict:
        """Get summary statistics for detected events."""
        if not events:
            return {
                'total_events': 0,
                'by_type': {},
                'by_severity': {},
                'avg_confidence': 0.0,
                'avg_market_impact': 0.0
            }
        
        # Count by type
        by_type = {}
        for event in events:
            event_type = event.event_type.value
            by_type[event_type] = by_type.get(event_type, 0) + 1
        
        # Count by severity
        by_severity = {}
        for event in events:
            severity = event.severity.name
            by_severity[severity] = by_severity.get(severity, 0) + 1
        
        # Calculate averages
        avg_confidence = sum(event.confidence for event in events) / len(events)
        avg_market_impact = sum(event.market_impact_score for event in events) / len(events)
        
        return {
            'total_events': len(events),
            'by_type': by_type,
            'by_severity': by_severity,
            'avg_confidence': round(avg_confidence, 3),
            'avg_market_impact': round(avg_market_impact, 3)
        }