"""Execution algorithms for the Noryon V2 trading system"""

import asyncio
import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Callable, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import logging
from abc import ABC, abstractmethod
import math

from src.core.models import TradeOrder, ExecutionReport
from src.utils.order_management import OrderManagementSystem, OrderSide, OrderType

logger = logging.getLogger(__name__)


class AlgorithmType(Enum):
    """Execution algorithm types"""
    TWAP = "twap"  # Time Weighted Average Price
    VWAP = "vwap"  # Volume Weighted Average Price
    POV = "pov"    # Percentage of Volume
    IS = "is"      # Implementation Shortfall
    ICEBERG = "iceberg"
    SNIPER = "sniper"
    GUERRILLA = "guerrilla"
    STEALTH = "stealth"


class ExecutionStyle(Enum):
    """Execution style preferences"""
    AGGRESSIVE = "aggressive"
    PASSIVE = "passive"
    BALANCED = "balanced"
    OPPORTUNISTIC = "opportunistic"


class MarketCondition(Enum):
    """Market condition assessment"""
    NORMAL = "normal"
    VOLATILE = "volatile"
    TRENDING = "trending"
    RANGING = "ranging"
    LOW_LIQUIDITY = "low_liquidity"
    HIGH_LIQUIDITY = "high_liquidity"


@dataclass
class ExecutionParameters:
    """Execution algorithm parameters"""
    algorithm_type: AlgorithmType
    total_quantity: float
    target_duration: timedelta
    participation_rate: float = 0.1  # For POV algorithm
    price_limit: Optional[float] = None
    urgency: float = 0.5  # 0 = patient, 1 = urgent
    risk_aversion: float = 0.5  # 0 = risk seeking, 1 = risk averse
    slice_size: Optional[float] = None
    min_slice_size: float = 100.0
    max_slice_size: Optional[float] = None
    randomization: float = 0.1  # Amount of randomization to add
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    metadata: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ExecutionSlice:
    """Individual execution slice"""
    slice_id: str
    quantity: float
    target_time: datetime
    price_limit: Optional[float]
    order_type: OrderType
    priority: int = 0
    executed: bool = False
    order_id: Optional[str] = None
    execution_time: Optional[datetime] = None
    execution_price: Optional[float] = None
    slippage: Optional[float] = None


@dataclass
class ExecutionProgress:
    """Execution progress tracking"""
    algorithm_id: str
    symbol: str
    total_quantity: float
    executed_quantity: float
    remaining_quantity: float
    average_price: float
    total_slippage: float
    total_cost: float
    start_time: datetime
    current_time: datetime
    estimated_completion: datetime
    progress_percent: float
    slices_completed: int
    slices_remaining: int
    market_impact: float
    execution_quality: float


class ExecutionAlgorithm(ABC):
    """Abstract base class for execution algorithms"""
    
    def __init__(self, symbol: str, side: OrderSide, params: ExecutionParameters):
        self.symbol = symbol
        self.side = side
        self.params = params
        self.algorithm_id = f"{params.algorithm_type.value}_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.slices = []
        self.executed_slices = []
        self.start_time = datetime.now()
        self.is_running = False
        self.is_completed = False
        
    @abstractmethod
    def generate_slices(self, market_data: Dict[str, Any]) -> List[ExecutionSlice]:
        """Generate execution slices based on algorithm logic"""
        pass
    
    @abstractmethod
    def adjust_execution(self, market_data: Dict[str, Any], progress: ExecutionProgress) -> List[ExecutionSlice]:
        """Adjust execution based on market conditions and progress"""
        pass
    
    def get_progress(self) -> ExecutionProgress:
        """Get current execution progress"""
        executed_qty = sum(slice.quantity for slice in self.executed_slices if slice.executed)
        remaining_qty = self.params.total_quantity - executed_qty

        if executed_qty > 0:
            # Only include slices with valid execution prices
            valid_executions = [slice for slice in self.executed_slices if slice.executed and slice.execution_price is not None]
            if valid_executions:
                avg_price = sum(slice.execution_price * slice.quantity for slice in valid_executions) / sum(slice.quantity for slice in valid_executions)
                total_cost = sum(slice.execution_price * slice.quantity for slice in valid_executions)
            else:
                avg_price = 0.0
                total_cost = 0.0
        else:
            avg_price = 0.0
            total_cost = 0.0

        progress_pct = (executed_qty / self.params.total_quantity) * 100 if self.params.total_quantity > 0 else 0
        
        return ExecutionProgress(
            algorithm_id=self.algorithm_id,
            symbol=self.symbol,
            total_quantity=self.params.total_quantity,
            executed_quantity=executed_qty,
            remaining_quantity=remaining_qty,
            average_price=avg_price,
            total_slippage=0.0,  # Calculate based on benchmark
            total_cost=total_cost,
            start_time=self.start_time,
            current_time=datetime.now(),
            estimated_completion=self.start_time + self.params.target_duration,
            progress_percent=progress_pct,
            slices_completed=len(self.executed_slices),
            slices_remaining=len(self.slices) - len(self.executed_slices),
            market_impact=0.0,  # Calculate based on price movement
            execution_quality=0.95  # Default quality score
        )


class TWAPAlgorithm(ExecutionAlgorithm):
    """Time Weighted Average Price algorithm with advanced volatility handling"""

    def __init__(self, symbol: str, side: OrderSide, params: ExecutionParameters):
        super().__init__(symbol, side, params)
        self.volatility_threshold = 0.02  # 2% volatility threshold
        self.price_history = []
        self.volume_history = []

    def generate_slices(self, market_data: Dict[str, Any]) -> List[ExecutionSlice]:
        """Generate TWAP slices with advanced time-weighted calculation logic"""
        try:
            duration_minutes = self.params.target_duration.total_seconds() / 60
            current_volatility = market_data.get('volatility', 0.15)
            current_price = market_data.get('current_price', 100.0)

            # Adjust slice interval based on volatility and urgency
            if current_volatility > self.volatility_threshold:
                # Higher volatility = smaller, more frequent slices
                base_interval = max(1, int(duration_minutes / 20))  # More slices
            elif duration_minutes <= 30:
                base_interval = 2
            elif duration_minutes <= 120:
                base_interval = 5
            else:
                base_interval = 15

            # Apply urgency factor
            urgency_multiplier = 1 - (self.params.urgency * 0.5)  # Higher urgency = smaller intervals
            slice_interval_minutes = max(1, int(base_interval * urgency_multiplier))

            num_slices = max(1, int(duration_minutes / slice_interval_minutes))

            # Calculate time-weighted slice sizes
            slices = []
            current_time = self.params.start_time or datetime.now()

            # Generate time weights (higher weight during high-volume periods)
            time_weights = self._calculate_time_weights(num_slices, market_data)

            for i in range(num_slices):
                # Calculate slice size based on time weight
                time_weight = time_weights[i]
                base_slice_size = (self.params.total_quantity * time_weight) / sum(time_weights)

                # Add controlled randomization
                randomization_factor = 1 + (np.random.uniform(-1, 1) * self.params.randomization)
                slice_size = base_slice_size * randomization_factor

                # Apply volatility adjustment
                if current_volatility > self.volatility_threshold:
                    # Reduce slice size in volatile conditions
                    volatility_factor = max(0.5, 1 - (current_volatility - self.volatility_threshold) * 2)
                    slice_size *= volatility_factor

                # Ensure slice size is within bounds
                slice_size = max(self.params.min_slice_size, slice_size)
                if self.params.max_slice_size:
                    slice_size = min(self.params.max_slice_size, slice_size)

                # Adjust last slice to match total quantity exactly
                if i == num_slices - 1:
                    executed_so_far = sum(s.quantity for s in slices)
                    slice_size = max(0, self.params.total_quantity - executed_so_far)

                if slice_size > 0:
                    # Calculate target price with TWAP logic
                    target_price = self._calculate_twap_price(current_price, i, num_slices, market_data)

                    slice = ExecutionSlice(
                        slice_id=f"{self.algorithm_id}_slice_{i+1}",
                        quantity=slice_size,
                        target_time=current_time + timedelta(minutes=i * slice_interval_minutes),
                        price_limit=target_price if self.params.price_limit else None,
                        order_type=OrderType.LIMIT if target_price else OrderType.MARKET,
                        priority=i
                    )
                    slices.append(slice)

            self.slices = slices
            logger.info(f"Generated {len(slices)} TWAP slices with volatility adjustment (σ={current_volatility:.3f})")
            return slices

        except Exception as e:
            logger.error(f"Error generating TWAP slices: {e}")
            return []

    def _calculate_time_weights(self, num_slices: int, market_data: Dict[str, Any]) -> List[float]:
        """Calculate time-based weights for slice distribution"""
        try:
            # Get expected volume profile or use default
            volume_profile = market_data.get('volume_profile', [1.0] * num_slices)

            # Extend or truncate profile to match number of slices
            if len(volume_profile) != num_slices:
                # Interpolate volume profile to match slice count
                profile_array = np.array(volume_profile)
                indices = np.linspace(0, len(profile_array) - 1, num_slices)
                time_weights = np.interp(indices, range(len(profile_array)), profile_array)
            else:
                time_weights = volume_profile

            # Normalize weights
            total_weight = sum(time_weights)
            if total_weight > 0:
                time_weights = [w / total_weight for w in time_weights]
            else:
                time_weights = [1.0 / num_slices] * num_slices

            return time_weights

        except Exception as e:
            logger.error(f"Error calculating time weights: {e}")
            return [1.0 / num_slices] * num_slices

    def _calculate_twap_price(self, current_price: float, slice_index: int,
                             total_slices: int, market_data: Dict[str, Any]) -> Optional[float]:
        """Calculate time-weighted average price target for slice"""
        try:
            if not self.params.price_limit:
                return None

            # Get bid-ask spread
            bid = market_data.get('bid', current_price * 0.999)
            ask = market_data.get('ask', current_price * 1.001)
            spread = ask - bid

            # Calculate TWAP target based on slice position and market conditions
            progress_ratio = slice_index / max(1, total_slices - 1)

            # For buy orders, start closer to bid and move toward mid
            # For sell orders, start closer to ask and move toward mid
            mid_price = (bid + ask) / 2

            if self.side == OrderSide.BUY:
                # Start at bid + small buffer, move toward mid
                target_price = bid + (spread * 0.1) + (progress_ratio * spread * 0.4)
            else:  # SELL
                # Start at ask - small buffer, move toward mid
                target_price = ask - (spread * 0.1) - (progress_ratio * spread * 0.4)

            # Apply price limit constraint
            if self.params.price_limit:
                if self.side == OrderSide.BUY:
                    target_price = min(target_price, self.params.price_limit)
                else:
                    target_price = max(target_price, self.params.price_limit)

            return round(target_price, 4)

        except Exception as e:
            logger.error(f"Error calculating TWAP price: {e}")
            return current_price
    
    def adjust_execution(self, market_data: Dict[str, Any], progress: ExecutionProgress) -> List[ExecutionSlice]:
        """Adjust TWAP execution based on progress and market volatility scenarios"""
        try:
            current_time = datetime.now()
            time_elapsed = (current_time - self.start_time).total_seconds()
            expected_progress = time_elapsed / self.params.target_duration.total_seconds()
            actual_progress = progress.progress_percent / 100

            current_price = market_data.get('current_price', 100.0)
            current_volatility = market_data.get('volatility', 0.15)

            # Update price history for volatility tracking
            self.price_history.append(current_price)
            if len(self.price_history) > 20:  # Keep last 20 prices
                self.price_history.pop(0)

            # Calculate recent volatility
            if len(self.price_history) >= 2:
                recent_returns = [
                    abs(self.price_history[i] - self.price_history[i-1]) / self.price_history[i-1]
                    for i in range(1, len(self.price_history))
                ]
                recent_volatility = np.std(recent_returns) if recent_returns else 0
            else:
                recent_volatility = current_volatility

            # Adjust execution based on multiple factors
            adjustments_made = 0

            for slice in self.slices:
                if not slice.executed and slice.target_time > current_time:

                    # 1. Schedule adjustment - if behind schedule
                    if actual_progress < expected_progress * 0.8:
                        # Increase urgency - switch to market orders
                        if slice.order_type == OrderType.LIMIT:
                            slice.order_type = OrderType.MARKET
                            adjustments_made += 1
                            logger.info(f"Slice {slice.slice_id}: Switched to MARKET order (behind schedule)")

                    # 2. Volatility adjustment - if market becomes volatile
                    elif recent_volatility > self.volatility_threshold * 1.5:
                        # Reduce slice sizes and delay execution slightly
                        if slice.quantity > self.params.min_slice_size * 1.5:
                            original_qty = slice.quantity
                            slice.quantity *= 0.8  # Reduce by 20%
                            slice.target_time += timedelta(minutes=2)  # Delay slightly
                            adjustments_made += 1
                            logger.info(f"Slice {slice.slice_id}: Reduced size {original_qty:.2f} -> {slice.quantity:.2f} (high volatility)")

                    # 3. Price improvement opportunity
                    elif slice.price_limit and current_price:
                        if self.side == OrderSide.BUY and current_price < slice.price_limit * 0.995:
                            # Good buying opportunity - increase slice size slightly
                            slice.quantity *= 1.1
                            adjustments_made += 1
                            logger.info(f"Slice {slice.slice_id}: Increased size for price opportunity")
                        elif self.side == OrderSide.SELL and current_price > slice.price_limit * 1.005:
                            # Good selling opportunity - increase slice size slightly
                            slice.quantity *= 1.1
                            adjustments_made += 1
                            logger.info(f"Slice {slice.slice_id}: Increased size for price opportunity")

                    # 4. Update price limits based on current market
                    if slice.price_limit and current_price:
                        new_price = self._calculate_twap_price(
                            current_price,
                            slice.priority,
                            len(self.slices),
                            market_data
                        )
                        if new_price and abs(new_price - slice.price_limit) / slice.price_limit > 0.005:  # 0.5% change
                            slice.price_limit = new_price
                            adjustments_made += 1

            # Rebalance remaining quantities if needed
            if adjustments_made > 0:
                self._rebalance_remaining_quantities()
                logger.info(f"TWAP execution adjusted: {adjustments_made} slices modified")

            return self.slices

        except Exception as e:
            logger.error(f"Error adjusting TWAP execution: {e}")
            return self.slices

    def _rebalance_remaining_quantities(self):
        """Rebalance quantities across remaining slices to maintain total"""
        try:
            executed_qty = sum(slice.quantity for slice in self.slices if slice.executed)
            remaining_slices = [slice for slice in self.slices if not slice.executed]

            if remaining_slices:
                remaining_target = self.params.total_quantity - executed_qty
                current_remaining = sum(slice.quantity for slice in remaining_slices)

                if current_remaining > 0 and abs(remaining_target - current_remaining) > 0.01:
                    # Proportionally adjust remaining slices
                    adjustment_factor = remaining_target / current_remaining
                    for slice in remaining_slices:
                        slice.quantity *= adjustment_factor
                        slice.quantity = max(self.params.min_slice_size, slice.quantity)

                    logger.info(f"Rebalanced {len(remaining_slices)} remaining slices (factor: {adjustment_factor:.3f})")

        except Exception as e:
            logger.error(f"Error rebalancing quantities: {e}")


class VWAPAlgorithm(ExecutionAlgorithm):
    """Volume Weighted Average Price algorithm with liquidity detection"""

    def __init__(self, symbol: str, side: OrderSide, params: ExecutionParameters):
        super().__init__(symbol, side, params)
        self.volume_history = []
        self.vwap_history = []
        self.liquidity_threshold = 0.7  # Minimum liquidity score
        self.participation_limit = 0.15  # Max 15% of volume

    def generate_slices(self, market_data: Dict[str, Any]) -> List[ExecutionSlice]:
        """Generate VWAP slices with volume-weighted calculation and liquidity detection"""
        try:
            # Get enhanced volume profile with liquidity analysis
            volume_profile = self._get_enhanced_volume_profile(market_data)
            liquidity_score = market_data.get('liquidity_score', 0.8)
            expected_volume = market_data.get('expected_volume', 500000)
            current_price = market_data.get('current_price', 100.0)

            duration_minutes = self.params.target_duration.total_seconds() / 60

            # Adjust slice interval based on liquidity
            if liquidity_score < self.liquidity_threshold:
                slice_interval_minutes = 10  # Longer intervals for low liquidity
            else:
                slice_interval_minutes = 5  # Standard 5-minute intervals

            num_slices = max(1, int(duration_minutes / slice_interval_minutes))

            # Calculate maximum participation per slice
            max_slice_volume = expected_volume * self.participation_limit / num_slices

            slices = []
            current_time = self.params.start_time or datetime.now()
            cumulative_vwap = 0
            cumulative_volume = 0

            for i in range(num_slices):
                # Get volume weight for this time period
                time_bucket = i % len(volume_profile)
                volume_data = volume_profile[time_bucket]
                volume_weight = volume_data['weight']
                expected_price = volume_data.get('expected_price', current_price)

                # Calculate base slice size from volume weight
                base_slice_size = self.params.total_quantity * volume_weight

                # Apply liquidity constraints
                if liquidity_score < self.liquidity_threshold:
                    # Reduce slice size in low liquidity conditions
                    liquidity_factor = max(0.3, liquidity_score / self.liquidity_threshold)
                    base_slice_size *= liquidity_factor

                # Ensure we don't exceed participation limits
                volume_constraint = max_slice_volume / max(expected_price, 1.0)  # Convert to shares
                base_slice_size = min(base_slice_size, volume_constraint)

                # Add controlled randomization
                randomization_factor = 1 + (np.random.uniform(-1, 1) * self.params.randomization * 0.5)
                slice_size = base_slice_size * randomization_factor

                # Apply bounds
                slice_size = max(self.params.min_slice_size, slice_size)
                if self.params.max_slice_size:
                    slice_size = min(self.params.max_slice_size, slice_size)

                if slice_size > 0:
                    # Calculate VWAP-based price target
                    vwap_price = self._calculate_vwap_price(
                        expected_price, cumulative_vwap, cumulative_volume,
                        slice_size, market_data
                    )

                    slice = ExecutionSlice(
                        slice_id=f"{self.algorithm_id}_slice_{i+1}",
                        quantity=slice_size,
                        target_time=current_time + timedelta(minutes=i * slice_interval_minutes),
                        price_limit=vwap_price if self.params.price_limit else None,
                        order_type=OrderType.LIMIT if vwap_price else OrderType.MARKET,
                        priority=i
                    )
                    slices.append(slice)

                    # Update cumulative VWAP calculation
                    cumulative_volume += slice_size
                    cumulative_vwap = ((cumulative_vwap * (cumulative_volume - slice_size)) +
                                     (expected_price * slice_size)) / cumulative_volume

            # Normalize slice sizes to match total quantity exactly
            total_slice_qty = sum(s.quantity for s in slices)
            if total_slice_qty > 0:
                scale_factor = self.params.total_quantity / total_slice_qty
                for slice in slices:
                    slice.quantity *= scale_factor

            self.slices = slices
            logger.info(f"Generated {len(slices)} VWAP slices (liquidity: {liquidity_score:.2f}, participation: {self.participation_limit:.1%})")
            return slices

        except Exception as e:
            logger.error(f"Error generating VWAP slices: {e}")
            return []
    
    def _get_enhanced_volume_profile(self, market_data: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Get enhanced volume profile with price and liquidity data"""
        try:
            current_price = market_data.get('current_price', 100.0)
            volatility = market_data.get('volatility', 0.15)

            # Enhanced intraday volume profile with price expectations
            base_profile = [
                {'weight': 0.15, 'volume_factor': 1.8, 'liquidity': 0.9},  # 9:30-10:00 - High opening volume
                {'weight': 0.12, 'volume_factor': 1.4, 'liquidity': 0.85}, # 10:00-10:30
                {'weight': 0.08, 'volume_factor': 1.0, 'liquidity': 0.8},  # 10:30-11:00
                {'weight': 0.06, 'volume_factor': 0.8, 'liquidity': 0.75}, # 11:00-11:30
                {'weight': 0.05, 'volume_factor': 0.6, 'liquidity': 0.7},  # 11:30-12:00 - Lunch lull
                {'weight': 0.04, 'volume_factor': 0.5, 'liquidity': 0.65}, # 12:00-12:30
                {'weight': 0.04, 'volume_factor': 0.5, 'liquidity': 0.65}, # 12:30-13:00
                {'weight': 0.05, 'volume_factor': 0.6, 'liquidity': 0.7},  # 13:00-13:30
                {'weight': 0.06, 'volume_factor': 0.8, 'liquidity': 0.75}, # 13:30-14:00
                {'weight': 0.08, 'volume_factor': 1.0, 'liquidity': 0.8},  # 14:00-14:30
                {'weight': 0.10, 'volume_factor': 1.2, 'liquidity': 0.85}, # 14:30-15:00
                {'weight': 0.12, 'volume_factor': 1.4, 'liquidity': 0.9},  # 15:00-15:30
                {'weight': 0.15, 'volume_factor': 1.8, 'liquidity': 0.95}  # 15:30-16:00 - High closing volume
            ]

            # Add expected prices based on current price and volatility
            enhanced_profile = []
            for i, period in enumerate(base_profile):
                # Simulate price movement throughout the day
                time_factor = (i - 6) / 6  # -1 to +1 range centered on midday
                price_drift = current_price * volatility * time_factor * 0.1  # Small drift
                expected_price = current_price + price_drift + np.random.normal(0, current_price * volatility * 0.05)

                enhanced_period = {
                    'weight': period['weight'],
                    'volume_factor': period['volume_factor'],
                    'liquidity': period['liquidity'],
                    'expected_price': max(0.01, expected_price)  # Ensure positive price
                }
                enhanced_profile.append(enhanced_period)

            return enhanced_profile

        except Exception as e:
            logger.error(f"Error creating enhanced volume profile: {e}")
            # Return simple fallback profile
            return [{'weight': 1.0/13, 'volume_factor': 1.0, 'liquidity': 0.8, 'expected_price': 100.0}] * 13

    def _calculate_vwap_price(self, expected_price: float, cumulative_vwap: float,
                             cumulative_volume: float, slice_size: float,
                             market_data: Dict[str, Any]) -> Optional[float]:
        """Calculate volume-weighted average price target for slice"""
        try:
            if not self.params.price_limit:
                return None

            current_price = market_data.get('current_price', expected_price)
            bid = market_data.get('bid', current_price * 0.999)
            ask = market_data.get('ask', current_price * 1.001)

            # Calculate target VWAP for this slice
            if cumulative_volume > 0:
                # Use existing VWAP as reference
                target_vwap = cumulative_vwap
            else:
                # First slice - use expected price
                target_vwap = expected_price

            # Adjust target based on side and market conditions
            spread = ask - bid
            mid_price = (bid + ask) / 2

            if self.side == OrderSide.BUY:
                # For buys, target slightly below VWAP but above bid
                vwap_offset = min(spread * 0.3, target_vwap * 0.001)  # 0.1% or 30% of spread
                target_price = target_vwap - vwap_offset
                target_price = max(target_price, bid + spread * 0.1)  # Don't go too low
            else:  # SELL
                # For sells, target slightly above VWAP but below ask
                vwap_offset = min(spread * 0.3, target_vwap * 0.001)
                target_price = target_vwap + vwap_offset
                target_price = min(target_price, ask - spread * 0.1)  # Don't go too high

            # Apply price limit constraints
            if self.params.price_limit:
                if self.side == OrderSide.BUY:
                    target_price = min(target_price, self.params.price_limit)
                else:
                    target_price = max(target_price, self.params.price_limit)

            return round(target_price, 4)

        except Exception as e:
            logger.error(f"Error calculating VWAP price: {e}")
            return expected_price
    
    def adjust_execution(self, market_data: Dict[str, Any], progress: ExecutionProgress) -> List[ExecutionSlice]:
        """Adjust VWAP execution based on real-time volume and liquidity detection"""
        try:
            current_time = datetime.now()
            current_volume = market_data.get('current_volume', 0)
            expected_volume = market_data.get('expected_volume', 500000)
            current_price = market_data.get('current_price', 100.0)
            liquidity_score = market_data.get('liquidity_score', 0.8)

            # Update volume history
            self.volume_history.append(current_volume)
            if len(self.volume_history) > 10:  # Keep last 10 volume readings
                self.volume_history.pop(0)

            # Calculate volume metrics
            volume_ratio = current_volume / expected_volume if expected_volume > 0 else 1.0
            avg_recent_volume = np.mean(self.volume_history) if self.volume_history else current_volume
            volume_trend = (current_volume - avg_recent_volume) / avg_recent_volume if avg_recent_volume > 0 else 0

            # Calculate current VWAP
            executed_slices = [s for s in self.slices if s.executed and s.execution_price]
            if executed_slices:
                total_value = sum(s.execution_price * s.quantity for s in executed_slices)
                total_volume = sum(s.quantity for s in executed_slices)
                current_vwap = total_value / total_volume if total_volume > 0 else current_price
                self.vwap_history.append(current_vwap)
            else:
                current_vwap = current_price

            # Keep VWAP history manageable
            if len(self.vwap_history) > 20:
                self.vwap_history.pop(0)

            adjustments_made = 0

            for slice in self.slices:
                if not slice.executed and slice.target_time > current_time:

                    # 1. Volume-based adjustments
                    if volume_ratio > 1.3:  # Significantly higher volume than expected
                        # Increase slice size to take advantage of liquidity
                        if liquidity_score > self.liquidity_threshold:
                            original_qty = slice.quantity
                            slice.quantity *= min(1.2, 1 + (volume_ratio - 1) * 0.5)
                            adjustments_made += 1
                            logger.info(f"Slice {slice.slice_id}: Increased size {original_qty:.2f} -> {slice.quantity:.2f} (high volume)")

                    elif volume_ratio < 0.6:  # Much lower volume than expected
                        # Reduce slice size and potentially delay
                        original_qty = slice.quantity
                        slice.quantity *= max(0.7, volume_ratio + 0.1)
                        slice.target_time += timedelta(minutes=2)  # Small delay
                        adjustments_made += 1
                        logger.info(f"Slice {slice.slice_id}: Reduced size {original_qty:.2f} -> {slice.quantity:.2f} (low volume)")

                    # 2. Liquidity-based adjustments
                    if liquidity_score < self.liquidity_threshold:
                        # Low liquidity - be more conservative
                        if slice.quantity > self.params.min_slice_size * 2:
                            slice.quantity *= 0.8
                            slice.target_time += timedelta(minutes=3)  # Longer delay
                            adjustments_made += 1
                            logger.info(f"Slice {slice.slice_id}: Reduced for low liquidity (score: {liquidity_score:.2f})")

                    # 3. VWAP performance adjustment
                    if current_vwap and current_price:
                        vwap_deviation = (current_price - current_vwap) / current_vwap

                        if abs(vwap_deviation) > 0.005:  # 0.5% deviation from VWAP
                            if self.side == OrderSide.BUY and vwap_deviation < -0.005:
                                # Price below VWAP - good buying opportunity
                                slice.quantity *= 1.1
                                adjustments_made += 1
                                logger.info(f"Slice {slice.slice_id}: Increased for favorable VWAP deviation ({vwap_deviation:.3f})")
                            elif self.side == OrderSide.SELL and vwap_deviation > 0.005:
                                # Price above VWAP - good selling opportunity
                                slice.quantity *= 1.1
                                adjustments_made += 1
                                logger.info(f"Slice {slice.slice_id}: Increased for favorable VWAP deviation ({vwap_deviation:.3f})")

                    # 4. Update price targets based on current VWAP
                    if slice.price_limit and current_vwap:
                        new_vwap_price = self._calculate_vwap_price(
                            current_price, current_vwap, progress.executed_quantity,
                            slice.quantity, market_data
                        )
                        if new_vwap_price and abs(new_vwap_price - slice.price_limit) / slice.price_limit > 0.003:
                            slice.price_limit = new_vwap_price
                            adjustments_made += 1

                    # 5. Participation rate enforcement
                    if expected_volume > 0:
                        max_participation_qty = expected_volume * self.participation_limit / len([s for s in self.slices if not s.executed])
                        if slice.quantity > max_participation_qty:
                            slice.quantity = max_participation_qty
                            adjustments_made += 1
                            logger.info(f"Slice {slice.slice_id}: Capped at participation limit")

            # Rebalance if significant adjustments were made
            if adjustments_made > 2:
                self._rebalance_vwap_quantities()
                logger.info(f"VWAP execution adjusted: {adjustments_made} slices modified (vol_ratio: {volume_ratio:.2f}, liq: {liquidity_score:.2f})")

            return self.slices

        except Exception as e:
            logger.error(f"Error adjusting VWAP execution: {e}")
            return self.slices

    def _rebalance_vwap_quantities(self):
        """Rebalance quantities while maintaining VWAP objectives"""
        try:
            executed_qty = sum(slice.quantity for slice in self.slices if slice.executed)
            remaining_slices = [slice for slice in self.slices if not slice.executed]

            if remaining_slices:
                remaining_target = self.params.total_quantity - executed_qty
                current_remaining = sum(slice.quantity for slice in remaining_slices)

                if current_remaining > 0 and abs(remaining_target - current_remaining) > 0.01:
                    # Proportionally adjust while respecting volume constraints
                    adjustment_factor = remaining_target / current_remaining

                    for slice in remaining_slices:
                        slice.quantity *= adjustment_factor
                        slice.quantity = max(self.params.min_slice_size, slice.quantity)

                        # Ensure we don't violate participation limits
                        if hasattr(self, 'participation_limit'):
                            max_qty = self.params.total_quantity * self.participation_limit / len(remaining_slices)
                            slice.quantity = min(slice.quantity, max_qty)

                    logger.info(f"Rebalanced {len(remaining_slices)} VWAP slices (factor: {adjustment_factor:.3f})")

        except Exception as e:
            logger.error(f"Error rebalancing VWAP quantities: {e}")


class POVAlgorithm(ExecutionAlgorithm):
    """Percentage of Volume algorithm"""
    
    def generate_slices(self, market_data: Dict[str, Any]) -> List[ExecutionSlice]:
        """Generate POV slices based on participation rate"""
        try:
            expected_volume = market_data.get('expected_volume', 1000000)
            participation_volume = expected_volume * self.params.participation_rate
            
            duration_minutes = self.params.target_duration.total_seconds() / 60
            slice_interval_minutes = 2  # More frequent slices for POV
            num_slices = max(1, int(duration_minutes / slice_interval_minutes))
            
            base_slice_size = participation_volume / num_slices
            
            slices = []
            current_time = self.params.start_time or datetime.now()
            
            for i in range(num_slices):
                # Add randomization
                randomization_factor = 1 + (np.random.uniform(-1, 1) * self.params.randomization)
                slice_size = base_slice_size * randomization_factor
                
                # Ensure we don't exceed total quantity
                executed_so_far = sum(s.quantity for s in slices)
                remaining_qty = self.params.total_quantity - executed_so_far
                slice_size = min(slice_size, remaining_qty)
                
                if slice_size > self.params.min_slice_size and remaining_qty > 0:
                    slice = ExecutionSlice(
                        slice_id=f"{self.algorithm_id}_slice_{i+1}",
                        quantity=slice_size,
                        target_time=current_time + timedelta(minutes=i * slice_interval_minutes),
                        price_limit=self.params.price_limit,
                        order_type=OrderType.LIMIT if self.params.price_limit else OrderType.MARKET,
                        priority=i
                    )
                    slices.append(slice)
                
                if executed_so_far + slice_size >= self.params.total_quantity:
                    break
            
            self.slices = slices
            return slices
            
        except Exception as e:
            logger.error(f"Error generating POV slices: {e}")
            return []
    
    def adjust_execution(self, market_data: Dict[str, Any], progress: ExecutionProgress) -> List[ExecutionSlice]:
        """Adjust POV execution based on real-time volume"""
        try:
            current_volume = market_data.get('current_volume', 0)
            target_participation = current_volume * self.params.participation_rate
            
            # Adjust next slice size based on current volume
            for slice in self.slices:
                if not slice.executed and slice.target_time <= datetime.now() + timedelta(minutes=5):
                    slice.quantity = min(target_participation, 
                                       self.params.total_quantity - progress.executed_quantity)
            
            return self.slices
            
        except Exception as e:
            logger.error(f"Error adjusting POV execution: {e}")
            return self.slices


class IcebergAlgorithm(ExecutionAlgorithm):
    """Iceberg algorithm - shows only small portions of large orders"""
    
    def generate_slices(self, market_data: Dict[str, Any]) -> List[ExecutionSlice]:
        """Generate iceberg slices with hidden quantity"""
        try:
            # Determine visible slice size (tip of iceberg)
            avg_trade_size = market_data.get('avg_trade_size', 1000)
            visible_size = min(avg_trade_size * 2, self.params.total_quantity * 0.1)
            visible_size = max(self.params.min_slice_size, visible_size)
            
            num_slices = math.ceil(self.params.total_quantity / visible_size)
            
            slices = []
            current_time = self.params.start_time or datetime.now()
            
            for i in range(num_slices):
                remaining_qty = self.params.total_quantity - sum(s.quantity for s in slices)
                slice_size = min(visible_size, remaining_qty)
                
                if slice_size > 0:
                    slice = ExecutionSlice(
                        slice_id=f"{self.algorithm_id}_slice_{i+1}",
                        quantity=slice_size,
                        target_time=current_time + timedelta(seconds=i * 30),  # Quick succession
                        price_limit=self.params.price_limit,
                        order_type=OrderType.LIMIT,  # Always use limit orders for stealth
                        priority=i
                    )
                    slices.append(slice)
            
            self.slices = slices
            return slices
            
        except Exception as e:
            logger.error(f"Error generating Iceberg slices: {e}")
            return []
    
    def adjust_execution(self, market_data: Dict[str, Any], progress: ExecutionProgress) -> List[ExecutionSlice]:
        """Adjust iceberg execution to maintain stealth"""
        try:
            # Monitor market impact and adjust slice timing
            market_impact = market_data.get('market_impact', 0.0)
            
            if market_impact > 0.001:  # 10 bps impact threshold
                # Slow down execution
                for slice in self.slices:
                    if not slice.executed and slice.target_time > datetime.now():
                        slice.target_time += timedelta(minutes=1)
            
            return self.slices
            
        except Exception as e:
            logger.error(f"Error adjusting Iceberg execution: {e}")
            return self.slices


class ExecutionAlgorithms:
    """Main execution algorithms manager"""
    
    def __init__(self, order_management_system: OrderManagementSystem):
        self.oms = order_management_system
        self.active_algorithms = {}
        self.completed_algorithms = {}
        self.market_data_cache = {}
        
    def create_algorithm(self, algorithm_type: AlgorithmType, symbol: str, 
                        side: OrderSide, params: ExecutionParameters) -> ExecutionAlgorithm:
        """Create an execution algorithm instance"""
        try:
            if algorithm_type == AlgorithmType.TWAP:
                return TWAPAlgorithm(symbol, side, params)
            elif algorithm_type == AlgorithmType.VWAP:
                return VWAPAlgorithm(symbol, side, params)
            elif algorithm_type == AlgorithmType.POV:
                return POVAlgorithm(symbol, side, params)
            elif algorithm_type == AlgorithmType.ICEBERG:
                return IcebergAlgorithm(symbol, side, params)
            else:
                raise ValueError(f"Unsupported algorithm type: {algorithm_type}")
                
        except Exception as e:
            logger.error(f"Error creating algorithm: {e}")
            raise
    
    async def start_algorithm(self, algorithm: ExecutionAlgorithm) -> bool:
        """Start executing an algorithm"""
        try:
            # Get market data
            market_data = await self._get_market_data(algorithm.symbol)
            
            # Generate initial slices
            slices = algorithm.generate_slices(market_data)
            if not slices:
                logger.error(f"No slices generated for algorithm {algorithm.algorithm_id}")
                return False
            
            # Start algorithm
            algorithm.is_running = True
            self.active_algorithms[algorithm.algorithm_id] = algorithm
            
            # Start execution loop
            asyncio.create_task(self._execution_loop(algorithm))
            
            logger.info(f"Started algorithm {algorithm.algorithm_id} with {len(slices)} slices")
            return True
            
        except Exception as e:
            logger.error(f"Error starting algorithm: {e}")
            return False
    
    async def stop_algorithm(self, algorithm_id: str) -> bool:
        """Stop an executing algorithm"""
        try:
            if algorithm_id in self.active_algorithms:
                algorithm = self.active_algorithms[algorithm_id]
                algorithm.is_running = False
                
                # Cancel any pending orders
                for slice in algorithm.slices:
                    if slice.order_id and not slice.executed:
                        await self.oms.cancel_order(slice.order_id)
                
                # Move to completed
                self.completed_algorithms[algorithm_id] = self.active_algorithms.pop(algorithm_id)
                
                logger.info(f"Stopped algorithm {algorithm_id}")
                return True
            
            return False
            
        except Exception as e:
            logger.error(f"Error stopping algorithm: {e}")
            return False
    
    async def _execution_loop(self, algorithm: ExecutionAlgorithm):
        """Main execution loop for an algorithm"""
        try:
            while algorithm.is_running and not algorithm.is_completed:
                # Get current market data
                market_data = await self._get_market_data(algorithm.symbol)
                
                # Get current progress
                progress = algorithm.get_progress()
                
                # Adjust execution if needed
                algorithm.adjust_execution(market_data, progress)
                
                # Execute ready slices
                await self._execute_ready_slices(algorithm)
                
                # Check if algorithm is completed
                if progress.remaining_quantity <= 0:
                    algorithm.is_completed = True
                    algorithm.is_running = False
                    self.completed_algorithms[algorithm.algorithm_id] = self.active_algorithms.pop(algorithm.algorithm_id)
                    logger.info(f"Algorithm {algorithm.algorithm_id} completed")
                    break
                
                # Wait before next iteration
                await asyncio.sleep(5)  # 5-second intervals
                
        except Exception as e:
            logger.error(f"Error in execution loop for {algorithm.algorithm_id}: {e}")
            algorithm.is_running = False
    
    async def _execute_ready_slices(self, algorithm: ExecutionAlgorithm):
        """Execute slices that are ready"""
        try:
            current_time = datetime.now()
            
            for slice in algorithm.slices:
                if (not slice.executed and 
                    slice.target_time <= current_time and 
                    not slice.order_id):
                    
                    # Create order for slice
                    if slice.order_type == OrderType.MARKET:
                        order = self.oms.create_market_order(
                            algorithm.symbol, 
                            algorithm.side, 
                            slice.quantity
                        )
                    else:
                        order = self.oms.create_limit_order(
                            algorithm.symbol,
                            algorithm.side,
                            slice.quantity,
                            slice.price_limit or 100.0  # Default price
                        )
                    
                    # Submit order
                    success, validation_result = await self.oms.submit_order(order)
                    
                    if success:
                        slice.order_id = order.order_id
                        slice.execution_time = datetime.now()
                        slice.executed = True
                        # Set a simulated execution price for testing
                        slice.execution_price = getattr(order, 'price', 100.0) if hasattr(order, 'price') else 100.0
                        algorithm.executed_slices.append(slice)

                        logger.info(f"Executed slice {slice.slice_id} with order {order.order_id}")
                    else:
                        logger.warning(f"Failed to execute slice {slice.slice_id}: {validation_result.messages}")
                        
        except Exception as e:
            logger.error(f"Error executing slices: {e}")
    
    async def _get_market_data(self, symbol: str) -> Dict[str, Any]:
        """Get market data for a symbol"""
        try:
            # Simulate market data (in practice, would fetch from market data provider)
            current_time = datetime.now()
            
            # Check cache
            cache_key = f"{symbol}_{current_time.strftime('%Y%m%d_%H%M')}"
            if cache_key in self.market_data_cache:
                return self.market_data_cache[cache_key]
            
            # Generate simulated market data
            market_data = {
                'symbol': symbol,
                'current_price': 100.0 + np.random.normal(0, 1),
                'bid': 99.95,
                'ask': 100.05,
                'volume': np.random.randint(100000, 1000000),
                'expected_volume': 500000,
                'current_volume': np.random.randint(50000, 150000),
                'avg_trade_size': np.random.randint(500, 2000),
                'volatility': np.random.uniform(0.15, 0.35),
                'market_impact': np.random.uniform(0.0001, 0.002),
                'liquidity_score': np.random.uniform(0.7, 1.0),
                'timestamp': current_time
            }
            
            # Cache for 1 minute
            self.market_data_cache[cache_key] = market_data
            
            return market_data
            
        except Exception as e:
            logger.error(f"Error getting market data: {e}")
            return {}
    
    def get_algorithm_progress(self, algorithm_id: str) -> Optional[ExecutionProgress]:
        """Get progress for a specific algorithm"""
        algorithm = self.active_algorithms.get(algorithm_id) or self.completed_algorithms.get(algorithm_id)
        if algorithm:
            return algorithm.get_progress()
        return None
    
    def get_active_algorithms(self) -> List[str]:
        """Get list of active algorithm IDs"""
        return list(self.active_algorithms.keys())
    
    def get_algorithm_summary(self) -> Dict[str, Any]:
        """Get summary of all algorithms"""
        return {
            'active_count': len(self.active_algorithms),
            'completed_count': len(self.completed_algorithms),
            'active_algorithms': list(self.active_algorithms.keys()),
            'completed_algorithms': list(self.completed_algorithms.keys())
        }
    
    def recommend_algorithm(self, symbol: str, quantity: float, 
                          market_condition: MarketCondition,
                          execution_style: ExecutionStyle) -> Tuple[AlgorithmType, ExecutionParameters]:
        """Recommend an algorithm based on market conditions and preferences"""
        try:
            # Default parameters
            params = ExecutionParameters(
                algorithm_type=AlgorithmType.TWAP,
                total_quantity=quantity,
                target_duration=timedelta(hours=1)
            )
            
            # Adjust based on market condition
            if market_condition == MarketCondition.VOLATILE:
                # Use ICEBERG for volatile markets
                params.algorithm_type = AlgorithmType.ICEBERG
                params.target_duration = timedelta(hours=2)  # Slower execution
                params.randomization = 0.2  # More randomization
                
            elif market_condition == MarketCondition.LOW_LIQUIDITY:
                # Use TWAP with longer duration
                params.algorithm_type = AlgorithmType.TWAP
                params.target_duration = timedelta(hours=4)
                params.min_slice_size = quantity * 0.01  # Smaller slices
                
            elif market_condition == MarketCondition.HIGH_LIQUIDITY:
                # Use POV to take advantage of liquidity
                params.algorithm_type = AlgorithmType.POV
                params.participation_rate = 0.15
                params.target_duration = timedelta(minutes=30)
                
            else:  # NORMAL, TRENDING, RANGING
                # Use VWAP as default
                params.algorithm_type = AlgorithmType.VWAP
                params.target_duration = timedelta(hours=1)
            
            # Adjust based on execution style
            if execution_style == ExecutionStyle.AGGRESSIVE:
                params.urgency = 0.8
                params.participation_rate = min(0.25, params.participation_rate * 1.5)
                params.target_duration = timedelta(seconds=params.target_duration.total_seconds() * 0.5)
                
            elif execution_style == ExecutionStyle.PASSIVE:
                params.urgency = 0.2
                params.participation_rate = max(0.05, params.participation_rate * 0.7)
                params.target_duration = timedelta(seconds=params.target_duration.total_seconds() * 2)
                params.randomization = 0.15
                
            elif execution_style == ExecutionStyle.OPPORTUNISTIC:
                params.algorithm_type = AlgorithmType.ICEBERG
                params.urgency = 0.3
                params.randomization = 0.25
            
            return params.algorithm_type, params
            
        except Exception as e:
            logger.error(f"Error recommending algorithm: {e}")
            # Return safe default
            return AlgorithmType.TWAP, ExecutionParameters(
                algorithm_type=AlgorithmType.TWAP,
                total_quantity=quantity,
                target_duration=timedelta(hours=1)
            )
    
    def clear_completed_algorithms(self):
        """Clear completed algorithms from memory"""
        self.completed_algorithms.clear()
        logger.info("Cleared completed algorithms")
    
    def get_execution_statistics(self) -> Dict[str, Any]:
        """Get execution statistics across all algorithms"""
        try:
            total_algorithms = len(self.active_algorithms) + len(self.completed_algorithms)
            
            if total_algorithms == 0:
                return {'total_algorithms': 0}
            
            # Calculate aggregate statistics
            total_quantity = 0
            total_executed = 0
            total_slices = 0
            algorithm_types = {}
            
            all_algorithms = list(self.active_algorithms.values()) + list(self.completed_algorithms.values())
            
            for algorithm in all_algorithms:
                progress = algorithm.get_progress()
                total_quantity += progress.total_quantity
                total_executed += progress.executed_quantity
                total_slices += progress.slices_completed + progress.slices_remaining
                
                algo_type = algorithm.params.algorithm_type.value
                algorithm_types[algo_type] = algorithm_types.get(algo_type, 0) + 1
            
            execution_rate = (total_executed / total_quantity * 100) if total_quantity > 0 else 0
            
            return {
                'total_algorithms': total_algorithms,
                'active_algorithms': len(self.active_algorithms),
                'completed_algorithms': len(self.completed_algorithms),
                'total_quantity': total_quantity,
                'total_executed': total_executed,
                'execution_rate_percent': execution_rate,
                'total_slices': total_slices,
                'algorithm_types': algorithm_types
            }
            
        except Exception as e:
            logger.error(f"Error calculating execution statistics: {e}")
            return {'error': str(e)}