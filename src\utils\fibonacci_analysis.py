#!/usr/bin/env python3
"""
Fibonacci Analysis
Advanced Fibonacci retracement and extension analysis for technical analysis.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
import logging
from scipy import signal
from scipy.stats import linregress

logger = logging.getLogger(__name__)

class FibonacciType(Enum):
    """Types of Fibonacci analysis."""
    RETRACEMENT = "retracement"
    EXTENSION = "extension"
    PROJECTION = "projection"
    TIME_ZONE = "time_zone"
    FAN = "fan"
    ARC = "arc"

class FibonacciLevel(Enum):
    """Standard Fibonacci levels."""
    LEVEL_0 = 0.0
    LEVEL_236 = 0.236
    LEVEL_382 = 0.382
    LEVEL_500 = 0.5
    LEVEL_618 = 0.618
    LEVEL_786 = 0.786
    LEVEL_1000 = 1.0
    LEVEL_1272 = 1.272
    LEVEL_1618 = 1.618
    LEVEL_2618 = 2.618
    LEVEL_4236 = 4.236

class SupportResistanceStrength(Enum):
    """Strength of support/resistance levels."""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"

@dataclass
class FibonacciPoint:
    """Fibonacci analysis point."""
    price: float
    level: float
    level_name: str
    fib_type: FibonacciType
    strength: SupportResistanceStrength
    confidence: float
    touches: int = 0
    last_touch_index: int = -1

@dataclass
class FibonacciAnalysis:
    """Complete Fibonacci analysis result."""
    symbol: str
    timeframe: str
    swing_high: float
    swing_low: float
    swing_high_index: int
    swing_low_index: int
    direction: str  # 'bullish' or 'bearish'
    retracement_levels: List[FibonacciPoint]
    extension_levels: List[FibonacciPoint]
    projection_levels: List[FibonacciPoint]
    current_price: float
    nearest_support: Optional[FibonacciPoint]
    nearest_resistance: Optional[FibonacciPoint]
    confluence_zones: List[Dict[str, Any]]
    analysis_timestamp: pd.Timestamp

class FibonacciAnalyzer:
    """Advanced Fibonacci analysis system."""
    
    def __init__(self):
        self.standard_retracement_levels = [0.236, 0.382, 0.5, 0.618, 0.786]
        self.standard_extension_levels = [1.272, 1.618, 2.618, 4.236]
        self.standard_projection_levels = [0.618, 1.0, 1.272, 1.618]
        self.confluence_threshold = 0.005  # 0.5% price tolerance for confluence
        self.min_swing_size = 0.02  # Minimum 2% swing size
        
    def analyze_fibonacci_levels(self, data: pd.DataFrame, symbol: str = None, 
                               timeframe: str = None) -> Dict[str, Any]:
        """Perform comprehensive Fibonacci analysis."""
        try:
            if data.empty or len(data) < 20:
                logger.warning(f"Insufficient data for Fibonacci analysis: {len(data)} rows")
                return {}
            
            # Find significant swing points
            swing_points = self._find_swing_points(data)
            
            if len(swing_points) < 2:
                logger.warning("Insufficient swing points for Fibonacci analysis")
                return {}
            
            # Analyze multiple swing combinations
            analyses = []
            
            # Analyze recent major swings
            for i in range(len(swing_points) - 1):
                for j in range(i + 1, len(swing_points)):
                    swing_low_data = swing_points[i]
                    swing_high_data = swing_points[j]
                    
                    # Ensure we have a significant swing
                    price_diff = abs(swing_high_data['price'] - swing_low_data['price'])
                    avg_price = (swing_high_data['price'] + swing_low_data['price']) / 2
                    
                    if price_diff / avg_price >= self.min_swing_size:
                        analysis = self._analyze_swing_pair(
                            data, swing_low_data, swing_high_data, symbol, timeframe
                        )
                        if analysis:
                            analyses.append(analysis)
            
            # Find confluence zones
            confluence_zones = self._find_confluence_zones(analyses)
            
            # Get current market position
            current_price = data['close'].iloc[-1]
            market_position = self._analyze_market_position(analyses, current_price)
            
            return {
                'analyses': analyses,
                'confluence_zones': confluence_zones,
                'market_position': market_position,
                'key_levels': self._extract_key_levels(analyses),
                'trading_opportunities': self._identify_trading_opportunities(analyses, current_price)
            }
            
        except Exception as e:
            logger.error(f"Error in Fibonacci analysis: {e}")
            return {}
    
    def _find_swing_points(self, data: pd.DataFrame) -> List[Dict[str, Any]]:
        """Find significant swing highs and lows."""
        swing_points = []
        
        try:
            high = data['high'].values
            low = data['low'].values
            close = data['close'].values
            
            # Find peaks and troughs with minimum distance
            min_distance = max(5, len(data) // 20)  # Adaptive distance
            
            peaks = signal.find_peaks(high, distance=min_distance, prominence=np.std(high) * 0.5)[0]
            troughs = signal.find_peaks(-low, distance=min_distance, prominence=np.std(low) * 0.5)[0]
            
            # Add swing highs
            for peak in peaks:
                swing_points.append({
                    'index': peak,
                    'price': high[peak],
                    'type': 'high',
                    'timestamp': data.index[peak] if hasattr(data.index, '__getitem__') else peak
                })
            
            # Add swing lows
            for trough in troughs:
                swing_points.append({
                    'index': trough,
                    'price': low[trough],
                    'type': 'low',
                    'timestamp': data.index[trough] if hasattr(data.index, '__getitem__') else trough
                })
            
            # Sort by index
            swing_points.sort(key=lambda x: x['index'])
            
            # Filter for significance
            significant_swings = self._filter_significant_swings(swing_points, close)
            
        except Exception as e:
            logger.error(f"Error finding swing points: {e}")
            return []
        
        return significant_swings
    
    def _filter_significant_swings(self, swing_points: List[Dict], close: np.ndarray) -> List[Dict]:
        """Filter out insignificant swing points."""
        if len(swing_points) < 2:
            return swing_points
        
        significant = []
        avg_price = np.mean(close)
        min_swing_amount = avg_price * self.min_swing_size
        
        for i, swing in enumerate(swing_points):
            is_significant = False
            
            # Check against previous swings
            for j in range(max(0, i-3), i):
                if abs(swing['price'] - swing_points[j]['price']) >= min_swing_amount:
                    is_significant = True
                    break
            
            # Check against following swings
            for j in range(i+1, min(len(swing_points), i+4)):
                if abs(swing['price'] - swing_points[j]['price']) >= min_swing_amount:
                    is_significant = True
                    break
            
            if is_significant or len(significant) == 0:
                significant.append(swing)
        
        return significant
    
    def _analyze_swing_pair(self, data: pd.DataFrame, swing_low: Dict, swing_high: Dict,
                           symbol: str, timeframe: str) -> Optional[FibonacciAnalysis]:
        """Analyze Fibonacci levels for a swing pair."""
        try:
            # Determine direction
            if swing_low['index'] < swing_high['index']:
                direction = 'bullish'
                start_price = swing_low['price']
                end_price = swing_high['price']
                start_index = swing_low['index']
                end_index = swing_high['index']
            else:
                direction = 'bearish'
                start_price = swing_high['price']
                end_price = swing_low['price']
                start_index = swing_high['index']
                end_index = swing_low['index']
            
            # Calculate Fibonacci levels
            retracement_levels = self._calculate_retracement_levels(
                start_price, end_price, direction, data, start_index, end_index
            )
            
            extension_levels = self._calculate_extension_levels(
                start_price, end_price, direction, data, start_index, end_index
            )
            
            projection_levels = self._calculate_projection_levels(
                start_price, end_price, direction, data, start_index, end_index
            )
            
            # Find nearest support and resistance
            current_price = data['close'].iloc[-1]
            nearest_support, nearest_resistance = self._find_nearest_levels(
                retracement_levels + extension_levels, current_price
            )
            
            # Create analysis object
            analysis = FibonacciAnalysis(
                symbol=symbol or 'Unknown',
                timeframe=timeframe or 'Unknown',
                swing_high=swing_high['price'],
                swing_low=swing_low['price'],
                swing_high_index=swing_high['index'],
                swing_low_index=swing_low['index'],
                direction=direction,
                retracement_levels=retracement_levels,
                extension_levels=extension_levels,
                projection_levels=projection_levels,
                current_price=current_price,
                nearest_support=nearest_support,
                nearest_resistance=nearest_resistance,
                confluence_zones=[],
                analysis_timestamp=pd.Timestamp.now()
            )
            
            return analysis
            
        except Exception as e:
            logger.error(f"Error analyzing swing pair: {e}")
            return None
    
    def _calculate_retracement_levels(self, start_price: float, end_price: float,
                                    direction: str, data: pd.DataFrame,
                                    start_index: int, end_index: int) -> List[FibonacciPoint]:
        """Calculate Fibonacci retracement levels."""
        levels = []
        
        try:
            price_range = end_price - start_price
            
            for level in self.standard_retracement_levels:
                if direction == 'bullish':
                    fib_price = end_price - (price_range * level)
                else:
                    fib_price = start_price + (price_range * level)
                
                # Calculate strength and touches
                strength, touches, last_touch = self._calculate_level_strength(
                    fib_price, data, max(start_index, end_index)
                )
                
                fib_point = FibonacciPoint(
                    price=fib_price,
                    level=level,
                    level_name=f"{level:.1%}",
                    fib_type=FibonacciType.RETRACEMENT,
                    strength=strength,
                    confidence=self._calculate_level_confidence(level, touches),
                    touches=touches,
                    last_touch_index=last_touch
                )
                
                levels.append(fib_point)
        
        except Exception as e:
            logger.error(f"Error calculating retracement levels: {e}")
        
        return levels
    
    def _calculate_extension_levels(self, start_price: float, end_price: float,
                                  direction: str, data: pd.DataFrame,
                                  start_index: int, end_index: int) -> List[FibonacciPoint]:
        """Calculate Fibonacci extension levels."""
        levels = []
        
        try:
            price_range = abs(end_price - start_price)
            
            for level in self.standard_extension_levels:
                if direction == 'bullish':
                    fib_price = end_price + (price_range * (level - 1))
                else:
                    fib_price = end_price - (price_range * (level - 1))
                
                # Calculate strength and touches
                strength, touches, last_touch = self._calculate_level_strength(
                    fib_price, data, max(start_index, end_index)
                )
                
                fib_point = FibonacciPoint(
                    price=fib_price,
                    level=level,
                    level_name=f"{level:.1%}",
                    fib_type=FibonacciType.EXTENSION,
                    strength=strength,
                    confidence=self._calculate_level_confidence(level, touches),
                    touches=touches,
                    last_touch_index=last_touch
                )
                
                levels.append(fib_point)
        
        except Exception as e:
            logger.error(f"Error calculating extension levels: {e}")
        
        return levels
    
    def _calculate_projection_levels(self, start_price: float, end_price: float,
                                   direction: str, data: pd.DataFrame,
                                   start_index: int, end_index: int) -> List[FibonacciPoint]:
        """Calculate Fibonacci projection levels."""
        levels = []
        
        try:
            price_range = abs(end_price - start_price)
            
            for level in self.standard_projection_levels:
                if direction == 'bullish':
                    fib_price = start_price + (price_range * level)
                else:
                    fib_price = start_price - (price_range * level)
                
                # Calculate strength and touches
                strength, touches, last_touch = self._calculate_level_strength(
                    fib_price, data, max(start_index, end_index)
                )
                
                fib_point = FibonacciPoint(
                    price=fib_price,
                    level=level,
                    level_name=f"{level:.1%}",
                    fib_type=FibonacciType.PROJECTION,
                    strength=strength,
                    confidence=self._calculate_level_confidence(level, touches),
                    touches=touches,
                    last_touch_index=last_touch
                )
                
                levels.append(fib_point)
        
        except Exception as e:
            logger.error(f"Error calculating projection levels: {e}")
        
        return levels
    
    def _calculate_level_strength(self, level_price: float, data: pd.DataFrame,
                                start_index: int) -> Tuple[SupportResistanceStrength, int, int]:
        """Calculate the strength of a Fibonacci level based on price action."""
        try:
            high = data['high'].values
            low = data['low'].values
            close = data['close'].values
            
            # Look for touches after the swing formation
            touches = 0
            last_touch_index = -1
            tolerance = level_price * 0.01  # 1% tolerance
            
            for i in range(start_index + 1, len(data)):
                # Check if price touched the level
                if (low[i] <= level_price + tolerance and 
                    high[i] >= level_price - tolerance):
                    touches += 1
                    last_touch_index = i
                
                # Check for strong bounces
                if (abs(low[i] - level_price) <= tolerance and 
                    close[i] > level_price + tolerance):
                    touches += 0.5  # Partial credit for bounces
                
                if (abs(high[i] - level_price) <= tolerance and 
                    close[i] < level_price - tolerance):
                    touches += 0.5  # Partial credit for rejections
            
            # Determine strength based on touches
            if touches >= 3:
                strength = SupportResistanceStrength.VERY_STRONG
            elif touches >= 2:
                strength = SupportResistanceStrength.STRONG
            elif touches >= 1:
                strength = SupportResistanceStrength.MODERATE
            else:
                strength = SupportResistanceStrength.WEAK
            
            return strength, int(touches), last_touch_index
            
        except Exception as e:
            logger.error(f"Error calculating level strength: {e}")
            return SupportResistanceStrength.WEAK, 0, -1
    
    def _calculate_level_confidence(self, fib_level: float, touches: int) -> float:
        """Calculate confidence score for a Fibonacci level."""
        try:
            # Base confidence based on Fibonacci level importance
            base_confidence = {
                0.236: 0.6,
                0.382: 0.8,
                0.5: 0.7,
                0.618: 0.9,
                0.786: 0.7,
                1.272: 0.8,
                1.618: 0.9,
                2.618: 0.7
            }.get(fib_level, 0.5)
            
            # Adjust based on touches
            touch_bonus = min(0.3, touches * 0.1)
            
            total_confidence = min(1.0, base_confidence + touch_bonus)
            
            return total_confidence
            
        except Exception as e:
            logger.error(f"Error calculating level confidence: {e}")
            return 0.5
    
    def _find_nearest_levels(self, all_levels: List[FibonacciPoint], 
                           current_price: float) -> Tuple[Optional[FibonacciPoint], Optional[FibonacciPoint]]:
        """Find nearest support and resistance levels."""
        try:
            support_levels = [level for level in all_levels if level.price < current_price]
            resistance_levels = [level for level in all_levels if level.price > current_price]
            
            nearest_support = None
            nearest_resistance = None
            
            if support_levels:
                nearest_support = max(support_levels, key=lambda x: x.price)
            
            if resistance_levels:
                nearest_resistance = min(resistance_levels, key=lambda x: x.price)
            
            return nearest_support, nearest_resistance
            
        except Exception as e:
            logger.error(f"Error finding nearest levels: {e}")
            return None, None
    
    def _find_confluence_zones(self, analyses: List[FibonacciAnalysis]) -> List[Dict[str, Any]]:
        """Find confluence zones where multiple Fibonacci levels cluster."""
        confluence_zones = []
        
        try:
            if len(analyses) < 2:
                return confluence_zones
            
            # Collect all levels from all analyses
            all_levels = []
            for analysis in analyses:
                all_levels.extend(analysis.retracement_levels)
                all_levels.extend(analysis.extension_levels)
                all_levels.extend(analysis.projection_levels)
            
            # Group levels by price proximity
            price_groups = {}
            
            for level in all_levels:
                # Find existing group or create new one
                group_found = False
                for group_price in price_groups.keys():
                    if abs(level.price - group_price) / group_price <= self.confluence_threshold:
                        price_groups[group_price].append(level)
                        group_found = True
                        break
                
                if not group_found:
                    price_groups[level.price] = [level]
            
            # Create confluence zones for groups with multiple levels
            for group_price, levels in price_groups.items():
                if len(levels) >= 2:  # At least 2 levels for confluence
                    avg_price = np.mean([level.price for level in levels])
                    total_confidence = np.mean([level.confidence for level in levels])
                    
                    # Calculate strength based on number of confluences
                    if len(levels) >= 4:
                        strength = SupportResistanceStrength.VERY_STRONG
                    elif len(levels) >= 3:
                        strength = SupportResistanceStrength.STRONG
                    else:
                        strength = SupportResistanceStrength.MODERATE
                    
                    confluence_zone = {
                        'price': avg_price,
                        'level_count': len(levels),
                        'strength': strength,
                        'confidence': total_confidence,
                        'levels': levels,
                        'price_range': {
                            'min': min(level.price for level in levels),
                            'max': max(level.price for level in levels)
                        }
                    }
                    
                    confluence_zones.append(confluence_zone)
            
            # Sort by confidence
            confluence_zones.sort(key=lambda x: x['confidence'], reverse=True)
            
        except Exception as e:
            logger.error(f"Error finding confluence zones: {e}")
        
        return confluence_zones
    
    def _analyze_market_position(self, analyses: List[FibonacciAnalysis], 
                               current_price: float) -> Dict[str, Any]:
        """Analyze current market position relative to Fibonacci levels."""
        try:
            if not analyses:
                return {}
            
            # Find the most recent analysis
            recent_analysis = max(analyses, key=lambda x: x.analysis_timestamp)
            
            # Determine position within Fibonacci levels
            position_info = {
                'current_price': current_price,
                'trend_direction': recent_analysis.direction,
                'nearest_support': recent_analysis.nearest_support.price if recent_analysis.nearest_support else None,
                'nearest_resistance': recent_analysis.nearest_resistance.price if recent_analysis.nearest_resistance else None,
                'position_strength': self._calculate_position_strength(recent_analysis, current_price)
            }
            
            # Calculate retracement percentage
            if recent_analysis.direction == 'bullish':
                total_range = recent_analysis.swing_high - recent_analysis.swing_low
                current_retracement = (recent_analysis.swing_high - current_price) / total_range
            else:
                total_range = recent_analysis.swing_low - recent_analysis.swing_high
                current_retracement = (current_price - recent_analysis.swing_high) / total_range
            
            position_info['retracement_percentage'] = current_retracement
            position_info['retracement_level'] = self._identify_current_fib_level(current_retracement)
            
            return position_info
            
        except Exception as e:
            logger.error(f"Error analyzing market position: {e}")
            return {}
    
    def _calculate_position_strength(self, analysis: FibonacciAnalysis, current_price: float) -> str:
        """Calculate the strength of current market position."""
        try:
            # Check proximity to strong Fibonacci levels
            strong_levels = [
                level for level in analysis.retracement_levels + analysis.extension_levels
                if level.strength in [SupportResistanceStrength.STRONG, SupportResistanceStrength.VERY_STRONG]
            ]
            
            min_distance = float('inf')
            for level in strong_levels:
                distance = abs(current_price - level.price) / current_price
                min_distance = min(min_distance, distance)
            
            if min_distance < 0.01:  # Within 1%
                return "very_strong"
            elif min_distance < 0.02:  # Within 2%
                return "strong"
            elif min_distance < 0.05:  # Within 5%
                return "moderate"
            else:
                return "weak"
                
        except Exception as e:
            logger.error(f"Error calculating position strength: {e}")
            return "unknown"
    
    def _identify_current_fib_level(self, retracement_percentage: float) -> str:
        """Identify which Fibonacci level the current retracement is closest to."""
        try:
            fib_levels = {
                0.236: "23.6%",
                0.382: "38.2%",
                0.5: "50.0%",
                0.618: "61.8%",
                0.786: "78.6%"
            }
            
            closest_level = min(fib_levels.keys(), key=lambda x: abs(x - abs(retracement_percentage)))
            
            return fib_levels[closest_level]
            
        except Exception as e:
            logger.error(f"Error identifying current Fibonacci level: {e}")
            return "unknown"
    
    def _extract_key_levels(self, analyses: List[FibonacciAnalysis]) -> List[Dict[str, Any]]:
        """Extract key Fibonacci levels across all analyses."""
        key_levels = []
        
        try:
            for analysis in analyses:
                # Add high-confidence retracement levels
                for level in analysis.retracement_levels:
                    if level.confidence > 0.7:
                        key_levels.append({
                            'price': level.price,
                            'type': 'retracement',
                            'level': level.level_name,
                            'confidence': level.confidence,
                            'strength': level.strength.value,
                            'touches': level.touches
                        })
                
                # Add high-confidence extension levels
                for level in analysis.extension_levels:
                    if level.confidence > 0.7:
                        key_levels.append({
                            'price': level.price,
                            'type': 'extension',
                            'level': level.level_name,
                            'confidence': level.confidence,
                            'strength': level.strength.value,
                            'touches': level.touches
                        })
            
            # Remove duplicates and sort by confidence
            unique_levels = []
            for level in key_levels:
                is_duplicate = False
                for existing in unique_levels:
                    if abs(level['price'] - existing['price']) / level['price'] < 0.01:
                        is_duplicate = True
                        break
                if not is_duplicate:
                    unique_levels.append(level)
            
            unique_levels.sort(key=lambda x: x['confidence'], reverse=True)
            
        except Exception as e:
            logger.error(f"Error extracting key levels: {e}")
        
        return unique_levels[:10]  # Return top 10 levels
    
    def _identify_trading_opportunities(self, analyses: List[FibonacciAnalysis], 
                                      current_price: float) -> List[Dict[str, Any]]:
        """Identify potential trading opportunities based on Fibonacci analysis."""
        opportunities = []
        
        try:
            for analysis in analyses:
                # Look for bounce opportunities at strong retracement levels
                for level in analysis.retracement_levels:
                    if (level.strength in [SupportResistanceStrength.STRONG, SupportResistanceStrength.VERY_STRONG] and
                        abs(current_price - level.price) / current_price < 0.02):  # Within 2%
                        
                        opportunity = {
                            'type': 'fibonacci_bounce',
                            'direction': 'long' if analysis.direction == 'bullish' and current_price <= level.price else 'short',
                            'entry_price': level.price,
                            'confidence': level.confidence,
                            'stop_loss': self._calculate_stop_loss(analysis, level),
                            'targets': self._calculate_targets(analysis, level),
                            'risk_reward_ratio': self._calculate_risk_reward(analysis, level)
                        }
                        
                        opportunities.append(opportunity)
                
                # Look for breakout opportunities at extension levels
                for level in analysis.extension_levels:
                    if (level.confidence > 0.8 and
                        abs(current_price - level.price) / current_price < 0.01):  # Within 1%
                        
                        opportunity = {
                            'type': 'fibonacci_breakout',
                            'direction': 'long' if analysis.direction == 'bullish' else 'short',
                            'entry_price': level.price,
                            'confidence': level.confidence * 0.8,  # Lower confidence for breakouts
                            'stop_loss': self._calculate_stop_loss(analysis, level),
                            'targets': self._calculate_targets(analysis, level),
                            'risk_reward_ratio': self._calculate_risk_reward(analysis, level)
                        }
                        
                        opportunities.append(opportunity)
            
            # Sort by confidence
            opportunities.sort(key=lambda x: x['confidence'], reverse=True)
            
        except Exception as e:
            logger.error(f"Error identifying trading opportunities: {e}")
        
        return opportunities[:5]  # Return top 5 opportunities
    
    def _calculate_stop_loss(self, analysis: FibonacciAnalysis, level: FibonacciPoint) -> float:
        """Calculate stop loss for a Fibonacci-based trade."""
        try:
            # Simple stop loss calculation
            if analysis.direction == 'bullish':
                return level.price * 0.98  # 2% below entry
            else:
                return level.price * 1.02  # 2% above entry
                
        except Exception as e:
            logger.error(f"Error calculating stop loss: {e}")
            return level.price
    
    def _calculate_targets(self, analysis: FibonacciAnalysis, level: FibonacciPoint) -> List[float]:
        """Calculate profit targets for a Fibonacci-based trade."""
        targets = []
        
        try:
            if analysis.direction == 'bullish':
                # Target next Fibonacci extension levels
                targets = [level.price * 1.02, level.price * 1.05, level.price * 1.08]
            else:
                # Target next Fibonacci extension levels
                targets = [level.price * 0.98, level.price * 0.95, level.price * 0.92]
                
        except Exception as e:
            logger.error(f"Error calculating targets: {e}")
        
        return targets
    
    def _calculate_risk_reward(self, analysis: FibonacciAnalysis, level: FibonacciPoint) -> float:
        """Calculate risk-reward ratio for a Fibonacci-based trade."""
        try:
            stop_loss = self._calculate_stop_loss(analysis, level)
            targets = self._calculate_targets(analysis, level)
            
            if targets:
                risk = abs(level.price - stop_loss)
                reward = abs(targets[0] - level.price)
                
                if risk > 0:
                    return reward / risk
            
            return 1.0
            
        except Exception as e:
            logger.error(f"Error calculating risk-reward ratio: {e}")
            return 1.0
    
    def get_fibonacci_summary(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Get summary of Fibonacci analysis."""
        try:
            analyses = analysis_result.get('analyses', [])
            confluence_zones = analysis_result.get('confluence_zones', [])
            key_levels = analysis_result.get('key_levels', [])
            opportunities = analysis_result.get('trading_opportunities', [])
            
            return {
                'total_analyses': len(analyses),
                'confluence_zones': len(confluence_zones),
                'key_levels_count': len(key_levels),
                'trading_opportunities': len(opportunities),
                'strongest_confluence': confluence_zones[0] if confluence_zones else None,
                'best_opportunity': opportunities[0] if opportunities else None,
                'market_bias': self._determine_market_bias(analyses)
            }
            
        except Exception as e:
            logger.error(f"Error creating Fibonacci summary: {e}")
            return {}
    
    def _determine_market_bias(self, analyses: List[FibonacciAnalysis]) -> str:
        """Determine overall market bias from Fibonacci analyses."""
        try:
            if not analyses:
                return "neutral"
            
            bullish_count = sum(1 for analysis in analyses if analysis.direction == 'bullish')
            bearish_count = len(analyses) - bullish_count
            
            if bullish_count > bearish_count * 1.5:
                return "bullish"
            elif bearish_count > bullish_count * 1.5:
                return "bearish"
            else:
                return "neutral"
                
        except Exception as e:
            logger.error(f"Error determining market bias: {e}")
            return "neutral"