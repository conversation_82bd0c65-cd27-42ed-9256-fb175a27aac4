#!/usr/bin/env python3
"""
Market Impact Predictor
Predicts market impact of news events and market data.
"""

import numpy as np
from typing import Dict, List, Tuple, Optional, Union
from enum import Enum
from dataclasses import dataclass
from datetime import datetime, timedelta
import logging

logger = logging.getLogger(__name__)

class ImpactDirection(Enum):
    """Direction of market impact."""
    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"
    MIXED = "mixed"

class ImpactMagnitude(Enum):
    """Magnitude of market impact."""
    MINIMAL = 1
    LOW = 2
    MODERATE = 3
    HIGH = 4
    EXTREME = 5

class ImpactTimeframe(Enum):
    """Expected timeframe for impact."""
    IMMEDIATE = "immediate"  # 0-1 hours
    SHORT_TERM = "short_term"  # 1-24 hours
    MEDIUM_TERM = "medium_term"  # 1-7 days
    LONG_TERM = "long_term"  # 1+ weeks

@dataclass
class MarketImpactPrediction:
    """Prediction of market impact."""
    direction: ImpactDirection
    magnitude: ImpactMagnitude
    timeframe: ImpactTimeframe
    confidence: float  # 0.0 to 1.0
    affected_sectors: List[str]
    affected_assets: List[str]
    price_impact_estimate: float  # Expected % change
    volatility_impact: float  # Expected volatility increase
    volume_impact: float  # Expected volume change multiplier
    reasoning: str
    risk_factors: List[str]
    timestamp: datetime

class MarketImpactPredictor:
    """Predicts market impact of events and news."""
    
    def __init__(self):
        # Impact keywords and their weights
        self.bullish_keywords = {
            'strong': 0.7, 'beat': 0.8, 'exceed': 0.8, 'growth': 0.6, 'profit': 0.7,
            'rally': 0.9, 'surge': 0.9, 'boom': 0.8, 'breakout': 0.8, 'upgrade': 0.7,
            'buy': 0.6, 'outperform': 0.7, 'positive': 0.5, 'optimistic': 0.6,
            'recovery': 0.7, 'expansion': 0.6, 'bullish': 0.8, 'momentum': 0.6
        }
        
        self.bearish_keywords = {
            'weak': 0.7, 'miss': 0.8, 'decline': 0.7, 'loss': 0.7, 'crash': 0.9,
            'plunge': 0.9, 'recession': 0.9, 'breakdown': 0.8, 'downgrade': 0.7,
            'sell': 0.6, 'underperform': 0.7, 'negative': 0.5, 'pessimistic': 0.6,
            'crisis': 0.9, 'bearish': 0.8, 'volatility': 0.5, 'uncertainty': 0.6
        }
        
        # Sector impact mappings
        self.sector_keywords = {
            'technology': ['tech', 'software', 'ai', 'cloud', 'semiconductor', 'digital'],
            'finance': ['bank', 'financial', 'credit', 'loan', 'insurance', 'fintech'],
            'healthcare': ['pharma', 'biotech', 'medical', 'drug', 'health', 'clinical'],
            'energy': ['oil', 'gas', 'renewable', 'solar', 'wind', 'energy', 'petroleum'],
            'retail': ['retail', 'consumer', 'shopping', 'e-commerce', 'store'],
            'automotive': ['auto', 'car', 'vehicle', 'electric vehicle', 'ev', 'tesla'],
            'real_estate': ['real estate', 'property', 'housing', 'reit', 'construction'],
            'commodities': ['gold', 'silver', 'copper', 'commodity', 'metal', 'agriculture']
        }
        
        # Event type impact profiles
        self.event_impact_profiles = {
            'earnings': {
                'base_magnitude': ImpactMagnitude.MODERATE,
                'timeframe': ImpactTimeframe.IMMEDIATE,
                'volatility_multiplier': 1.5
            },
            'merger_acquisition': {
                'base_magnitude': ImpactMagnitude.HIGH,
                'timeframe': ImpactTimeframe.IMMEDIATE,
                'volatility_multiplier': 2.0
            },
            'regulatory': {
                'base_magnitude': ImpactMagnitude.MODERATE,
                'timeframe': ImpactTimeframe.SHORT_TERM,
                'volatility_multiplier': 1.3
            },
            'economic_data': {
                'base_magnitude': ImpactMagnitude.MODERATE,
                'timeframe': ImpactTimeframe.IMMEDIATE,
                'volatility_multiplier': 1.4
            },
            'central_bank': {
                'base_magnitude': ImpactMagnitude.HIGH,
                'timeframe': ImpactTimeframe.IMMEDIATE,
                'volatility_multiplier': 2.5
            },
            'geopolitical': {
                'base_magnitude': ImpactMagnitude.HIGH,
                'timeframe': ImpactTimeframe.SHORT_TERM,
                'volatility_multiplier': 2.0
            }
        }
    
    def predict_impact(self, text: str, event_type: str = None, 
                      entities: List[str] = None) -> MarketImpactPrediction:
        """Predict market impact from text and context."""
        try:
            text_lower = text.lower()
            
            # Analyze sentiment direction
            direction = self._analyze_direction(text_lower)
            
            # Determine magnitude
            magnitude = self._determine_magnitude(text_lower, event_type)
            
            # Estimate timeframe
            timeframe = self._estimate_timeframe(text_lower, event_type)
            
            # Calculate confidence
            confidence = self._calculate_confidence(text_lower, direction, magnitude)
            
            # Identify affected sectors
            affected_sectors = self._identify_affected_sectors(text_lower)
            
            # Extract affected assets
            affected_assets = self._extract_affected_assets(text_lower, entities or [])
            
            # Estimate price impact
            price_impact = self._estimate_price_impact(direction, magnitude, confidence)
            
            # Estimate volatility impact
            volatility_impact = self._estimate_volatility_impact(magnitude, event_type)
            
            # Estimate volume impact
            volume_impact = self._estimate_volume_impact(magnitude, direction)
            
            # Generate reasoning
            reasoning = self._generate_reasoning(direction, magnitude, timeframe, affected_sectors)
            
            # Identify risk factors
            risk_factors = self._identify_risk_factors(text_lower, magnitude)
            
            return MarketImpactPrediction(
                direction=direction,
                magnitude=magnitude,
                timeframe=timeframe,
                confidence=confidence,
                affected_sectors=affected_sectors,
                affected_assets=affected_assets,
                price_impact_estimate=price_impact,
                volatility_impact=volatility_impact,
                volume_impact=volume_impact,
                reasoning=reasoning,
                risk_factors=risk_factors,
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error predicting market impact: {e}")
            return self._create_neutral_prediction()
    
    def _analyze_direction(self, text: str) -> ImpactDirection:
        """Analyze the directional impact from text."""
        bullish_score = 0.0
        bearish_score = 0.0
        
        words = text.split()
        
        for word in words:
            if word in self.bullish_keywords:
                bullish_score += self.bullish_keywords[word]
            elif word in self.bearish_keywords:
                bearish_score += self.bearish_keywords[word]
        
        # Determine direction
        if abs(bullish_score - bearish_score) < 0.2:
            return ImpactDirection.NEUTRAL
        elif bullish_score > bearish_score * 1.5:
            return ImpactDirection.BULLISH
        elif bearish_score > bullish_score * 1.5:
            return ImpactDirection.BEARISH
        else:
            return ImpactDirection.MIXED
    
    def _determine_magnitude(self, text: str, event_type: str = None) -> ImpactMagnitude:
        """Determine the magnitude of impact."""
        # Base magnitude from event type
        base_magnitude = ImpactMagnitude.MODERATE
        if event_type and event_type in self.event_impact_profiles:
            base_magnitude = self.event_impact_profiles[event_type]['base_magnitude']
        
        # Intensity modifiers
        intensity_keywords = {
            'extreme': 2.0, 'massive': 1.8, 'huge': 1.6, 'major': 1.4, 'significant': 1.2,
            'moderate': 1.0, 'minor': 0.8, 'slight': 0.6, 'minimal': 0.4
        }
        
        intensity_multiplier = 1.0
        for keyword, multiplier in intensity_keywords.items():
            if keyword in text:
                intensity_multiplier = max(intensity_multiplier, multiplier)
        
        # Calculate final magnitude
        final_magnitude = int(base_magnitude.value * intensity_multiplier)
        final_magnitude = max(1, min(5, final_magnitude))
        
        return ImpactMagnitude(final_magnitude)
    
    def _estimate_timeframe(self, text: str, event_type: str = None) -> ImpactTimeframe:
        """Estimate the timeframe for impact."""
        # Default from event type
        if event_type and event_type in self.event_impact_profiles:
            default_timeframe = self.event_impact_profiles[event_type]['timeframe']
        else:
            default_timeframe = ImpactTimeframe.SHORT_TERM
        
        # Time-related keywords
        if any(word in text for word in ['immediate', 'instant', 'now', 'today']):
            return ImpactTimeframe.IMMEDIATE
        elif any(word in text for word in ['tomorrow', 'next week', 'short term']):
            return ImpactTimeframe.SHORT_TERM
        elif any(word in text for word in ['next month', 'medium term', 'quarterly']):
            return ImpactTimeframe.MEDIUM_TERM
        elif any(word in text for word in ['long term', 'annual', 'years']):
            return ImpactTimeframe.LONG_TERM
        
        return default_timeframe
    
    def _calculate_confidence(self, text: str, direction: ImpactDirection, 
                            magnitude: ImpactMagnitude) -> float:
        """Calculate confidence in the prediction."""
        confidence = 0.5  # Base confidence
        
        # Higher confidence for clear directional signals
        if direction in [ImpactDirection.BULLISH, ImpactDirection.BEARISH]:
            confidence += 0.2
        
        # Higher confidence for extreme magnitudes
        if magnitude in [ImpactMagnitude.HIGH, ImpactMagnitude.EXTREME]:
            confidence += 0.2
        
        # Confidence based on keyword density
        words = text.split()
        keyword_count = sum(1 for word in words if word in self.bullish_keywords or word in self.bearish_keywords)
        keyword_density = keyword_count / len(words) if words else 0
        confidence += min(0.3, keyword_density * 2)
        
        return min(1.0, confidence)
    
    def _identify_affected_sectors(self, text: str) -> List[str]:
        """Identify sectors likely to be affected."""
        affected_sectors = []
        
        for sector, keywords in self.sector_keywords.items():
            if any(keyword in text for keyword in keywords):
                affected_sectors.append(sector)
        
        return affected_sectors
    
    def _extract_affected_assets(self, text: str, entities: List[str]) -> List[str]:
        """Extract specific assets that might be affected."""
        affected_assets = []
        
        # Add entities that look like tickers or company names
        for entity in entities:
            if len(entity) <= 5 and entity.isupper():  # Likely ticker
                affected_assets.append(entity)
            elif any(suffix in entity.lower() for suffix in ['inc', 'corp', 'ltd', 'llc']):
                affected_assets.append(entity)
        
        # Common asset mentions
        asset_keywords = {
            'bitcoin': 'BTC', 'ethereum': 'ETH', 'gold': 'GOLD', 'oil': 'OIL',
            'dollar': 'USD', 'euro': 'EUR', 'yen': 'JPY', 'pound': 'GBP'
        }
        
        for keyword, asset in asset_keywords.items():
            if keyword in text:
                affected_assets.append(asset)
        
        return list(set(affected_assets))
    
    def _estimate_price_impact(self, direction: ImpactDirection, 
                             magnitude: ImpactMagnitude, confidence: float) -> float:
        """Estimate expected price impact as percentage."""
        base_impact = {
            ImpactMagnitude.MINIMAL: 0.5,
            ImpactMagnitude.LOW: 1.0,
            ImpactMagnitude.MODERATE: 2.0,
            ImpactMagnitude.HIGH: 4.0,
            ImpactMagnitude.EXTREME: 8.0
        }
        
        impact = base_impact[magnitude] * confidence
        
        if direction == ImpactDirection.BEARISH:
            impact = -impact
        elif direction in [ImpactDirection.NEUTRAL, ImpactDirection.MIXED]:
            impact = 0.0
        
        return round(impact, 2)
    
    def _estimate_volatility_impact(self, magnitude: ImpactMagnitude, event_type: str = None) -> float:
        """Estimate volatility impact multiplier."""
        base_volatility = {
            ImpactMagnitude.MINIMAL: 1.1,
            ImpactMagnitude.LOW: 1.2,
            ImpactMagnitude.MODERATE: 1.4,
            ImpactMagnitude.HIGH: 1.8,
            ImpactMagnitude.EXTREME: 2.5
        }
        
        volatility = base_volatility[magnitude]
        
        # Event type modifier
        if event_type and event_type in self.event_impact_profiles:
            event_multiplier = self.event_impact_profiles[event_type]['volatility_multiplier']
            volatility *= event_multiplier
        
        return round(volatility, 2)
    
    def _estimate_volume_impact(self, magnitude: ImpactMagnitude, direction: ImpactDirection) -> float:
        """Estimate volume impact multiplier."""
        base_volume = {
            ImpactMagnitude.MINIMAL: 1.2,
            ImpactMagnitude.LOW: 1.5,
            ImpactMagnitude.MODERATE: 2.0,
            ImpactMagnitude.HIGH: 3.0,
            ImpactMagnitude.EXTREME: 5.0
        }
        
        volume = base_volume[magnitude]
        
        # Higher volume for clear directional moves
        if direction in [ImpactDirection.BULLISH, ImpactDirection.BEARISH]:
            volume *= 1.2
        
        return round(volume, 2)
    
    def _generate_reasoning(self, direction: ImpactDirection, magnitude: ImpactMagnitude,
                          timeframe: ImpactTimeframe, sectors: List[str]) -> str:
        """Generate human-readable reasoning for the prediction."""
        reasoning_parts = []
        
        # Direction reasoning
        if direction == ImpactDirection.BULLISH:
            reasoning_parts.append("Positive market sentiment expected")
        elif direction == ImpactDirection.BEARISH:
            reasoning_parts.append("Negative market sentiment expected")
        elif direction == ImpactDirection.MIXED:
            reasoning_parts.append("Mixed market reactions anticipated")
        else:
            reasoning_parts.append("Neutral market impact expected")
        
        # Magnitude reasoning
        reasoning_parts.append(f"with {magnitude.name.lower()} intensity")
        
        # Timeframe reasoning
        reasoning_parts.append(f"over {timeframe.value.replace('_', ' ')} period")
        
        # Sector reasoning
        if sectors:
            reasoning_parts.append(f"primarily affecting {', '.join(sectors)} sectors")
        
        return ". ".join(reasoning_parts) + "."
    
    def _identify_risk_factors(self, text: str, magnitude: ImpactMagnitude) -> List[str]:
        """Identify potential risk factors."""
        risk_factors = []
        
        risk_keywords = {
            'uncertainty': 'Market uncertainty may amplify volatility',
            'volatility': 'High volatility expected',
            'liquidity': 'Liquidity concerns may affect execution',
            'regulation': 'Regulatory risks present',
            'geopolitical': 'Geopolitical tensions may escalate',
            'economic': 'Economic indicators may diverge from expectations'
        }
        
        for keyword, risk in risk_keywords.items():
            if keyword in text:
                risk_factors.append(risk)
        
        # Add magnitude-based risks
        if magnitude in [ImpactMagnitude.HIGH, ImpactMagnitude.EXTREME]:
            risk_factors.append('High impact event may trigger cascading effects')
        
        return risk_factors
    
    def _create_neutral_prediction(self) -> MarketImpactPrediction:
        """Create a neutral prediction for error cases."""
        return MarketImpactPrediction(
            direction=ImpactDirection.NEUTRAL,
            magnitude=ImpactMagnitude.MINIMAL,
            timeframe=ImpactTimeframe.SHORT_TERM,
            confidence=0.0,
            affected_sectors=[],
            affected_assets=[],
            price_impact_estimate=0.0,
            volatility_impact=1.0,
            volume_impact=1.0,
            reasoning="Unable to determine market impact",
            risk_factors=[],
            timestamp=datetime.now()
        )
    
    def predict_batch_impact(self, texts: List[str], event_types: List[str] = None) -> List[MarketImpactPrediction]:
        """Predict impact for multiple texts."""
        if event_types is None:
            event_types = [None] * len(texts)
        
        return [self.predict_impact(text, event_type) 
                for text, event_type in zip(texts, event_types)]
    
    def aggregate_predictions(self, predictions: List[MarketImpactPrediction]) -> MarketImpactPrediction:
        """Aggregate multiple predictions into a single overall prediction."""
        if not predictions:
            return self._create_neutral_prediction()
        
        # Weight predictions by confidence
        total_weight = sum(p.confidence for p in predictions)
        if total_weight == 0:
            return self._create_neutral_prediction()
        
        # Aggregate direction (weighted vote)
        direction_scores = {direction: 0 for direction in ImpactDirection}
        for pred in predictions:
            direction_scores[pred.direction] += pred.confidence
        
        aggregated_direction = max(direction_scores, key=direction_scores.get)
        
        # Aggregate magnitude (weighted average)
        avg_magnitude = sum(p.magnitude.value * p.confidence for p in predictions) / total_weight
        aggregated_magnitude = ImpactMagnitude(int(round(avg_magnitude)))
        
        # Aggregate other metrics
        avg_confidence = sum(p.confidence for p in predictions) / len(predictions)
        avg_price_impact = sum(p.price_impact_estimate * p.confidence for p in predictions) / total_weight
        avg_volatility = sum(p.volatility_impact * p.confidence for p in predictions) / total_weight
        avg_volume = sum(p.volume_impact * p.confidence for p in predictions) / total_weight
        
        # Combine sectors and assets
        all_sectors = set()
        all_assets = set()
        all_risks = set()
        
        for pred in predictions:
            all_sectors.update(pred.affected_sectors)
            all_assets.update(pred.affected_assets)
            all_risks.update(pred.risk_factors)
        
        return MarketImpactPrediction(
            direction=aggregated_direction,
            magnitude=aggregated_magnitude,
            timeframe=ImpactTimeframe.SHORT_TERM,  # Default for aggregated
            confidence=avg_confidence,
            affected_sectors=list(all_sectors),
            affected_assets=list(all_assets),
            price_impact_estimate=round(avg_price_impact, 2),
            volatility_impact=round(avg_volatility, 2),
            volume_impact=round(avg_volume, 2),
            reasoning=f"Aggregated prediction from {len(predictions)} sources",
            risk_factors=list(all_risks),
            timestamp=datetime.now()
        )