"""Market data provider and management for the Noryon V2 trading system"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional, Tuple, Any, Union
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import logging
import asyncio
from abc import ABC, abstractmethod

logger = logging.getLogger(__name__)


class DataSource(Enum):
    """Available data sources"""
    YAHOO_FINANCE = "yahoo_finance"
    ALPHA_VANTAGE = "alpha_vantage"
    POLYGON = "polygon"
    IEX_CLOUD = "iex_cloud"
    QUANDL = "quandl"
    BLOOMBERG = "bloomberg"
    REUTERS = "reuters"
    INTERNAL = "internal"


class DataType(Enum):
    """Types of market data"""
    PRICE = "price"
    VOLUME = "volume"
    FUNDAMENTAL = "fundamental"
    NEWS = "news"
    SENTIMENT = "sentiment"
    ECONOMIC = "economic"
    EARNINGS = "earnings"
    INSIDER = "insider"
    INSTITUTIONAL = "institutional"
    OPTIONS = "options"
    FUTURES = "futures"
    CRYPTO = "crypto"


class TimeFrame(Enum):
    """Time frames for data"""
    TICK = "tick"
    MINUTE_1 = "1m"
    MINUTE_5 = "5m"
    MINUTE_15 = "15m"
    MINUTE_30 = "30m"
    HOUR_1 = "1h"
    HOUR_4 = "4h"
    DAILY = "1d"
    WEEKLY = "1w"
    MONTHLY = "1M"


@dataclass
class MarketDataRequest:
    """Market data request structure"""
    symbol: str
    data_type: DataType
    timeframe: TimeFrame
    start_date: datetime
    end_date: datetime
    source: Optional[DataSource] = None
    fields: Optional[List[str]] = None
    metadata: Optional[Dict[str, Any]] = None


@dataclass
class MarketDataResponse:
    """Market data response structure"""
    symbol: str
    data_type: DataType
    timeframe: TimeFrame
    data: pd.DataFrame
    source: DataSource
    timestamp: datetime
    metadata: Dict[str, Any]
    success: bool
    error_message: Optional[str] = None


@dataclass
class PriceData:
    """Price data structure"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: int
    adjusted_close: Optional[float] = None
    dividend: Optional[float] = None
    split_ratio: Optional[float] = None


@dataclass
class FundamentalData:
    """Fundamental data structure"""
    symbol: str
    timestamp: datetime
    market_cap: Optional[float]
    pe_ratio: Optional[float]
    pb_ratio: Optional[float]
    dividend_yield: Optional[float]
    eps: Optional[float]
    revenue: Optional[float]
    net_income: Optional[float]
    debt_to_equity: Optional[float]
    roe: Optional[float]
    roa: Optional[float]
    metadata: Dict[str, Any]


class DataProvider(ABC):
    """Abstract base class for data providers"""
    
    @abstractmethod
    async def get_price_data(self, symbol: str, timeframe: TimeFrame,
                           start_date: datetime, end_date: datetime) -> MarketDataResponse:
        """Get price data for a symbol"""
        pass
    
    @abstractmethod
    async def get_fundamental_data(self, symbol: str) -> MarketDataResponse:
        """Get fundamental data for a symbol"""
        pass
    
    @abstractmethod
    async def get_real_time_price(self, symbol: str) -> Optional[PriceData]:
        """Get real-time price for a symbol"""
        pass


class YahooFinanceProvider(DataProvider):
    """Yahoo Finance data provider"""
    
    def __init__(self):
        self.source = DataSource.YAHOO_FINANCE
        
    async def get_price_data(self, symbol: str, timeframe: TimeFrame,
                           start_date: datetime, end_date: datetime) -> MarketDataResponse:
        """Get price data from Yahoo Finance"""
        try:
            # Simulate data retrieval (in practice, would use yfinance or similar)
            dates = pd.date_range(start=start_date, end=end_date, freq='D')
            
            # Generate synthetic data for demonstration
            np.random.seed(hash(symbol) % 2**32)
            base_price = 100.0
            returns = np.random.normal(0.001, 0.02, len(dates))
            prices = [base_price]
            
            for ret in returns[1:]:
                prices.append(prices[-1] * (1 + ret))
            
            data = pd.DataFrame({
                'open': [p * (1 + np.random.normal(0, 0.005)) for p in prices],
                'high': [p * (1 + abs(np.random.normal(0, 0.01))) for p in prices],
                'low': [p * (1 - abs(np.random.normal(0, 0.01))) for p in prices],
                'close': prices,
                'volume': np.random.randint(1000000, 10000000, len(dates)),
                'adjusted_close': prices
            }, index=dates)
            
            return MarketDataResponse(
                symbol=symbol,
                data_type=DataType.PRICE,
                timeframe=timeframe,
                data=data,
                source=self.source,
                timestamp=datetime.now(),
                metadata={'provider': 'yahoo_finance'},
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error fetching price data from Yahoo Finance: {e}")
            return MarketDataResponse(
                symbol=symbol,
                data_type=DataType.PRICE,
                timeframe=timeframe,
                data=pd.DataFrame(),
                source=self.source,
                timestamp=datetime.now(),
                metadata={},
                success=False,
                error_message=str(e)
            )
    
    async def get_fundamental_data(self, symbol: str) -> MarketDataResponse:
        """Get fundamental data from Yahoo Finance"""
        try:
            # Simulate fundamental data
            fundamental_data = pd.DataFrame({
                'market_cap': [np.random.uniform(1e9, 1e12)],
                'pe_ratio': [np.random.uniform(10, 30)],
                'pb_ratio': [np.random.uniform(1, 5)],
                'dividend_yield': [np.random.uniform(0, 0.05)],
                'eps': [np.random.uniform(1, 10)],
                'revenue': [np.random.uniform(1e9, 1e11)],
                'net_income': [np.random.uniform(1e8, 1e10)],
                'debt_to_equity': [np.random.uniform(0.1, 2.0)],
                'roe': [np.random.uniform(0.05, 0.25)],
                'roa': [np.random.uniform(0.02, 0.15)]
            }, index=[datetime.now()])
            
            return MarketDataResponse(
                symbol=symbol,
                data_type=DataType.FUNDAMENTAL,
                timeframe=TimeFrame.DAILY,
                data=fundamental_data,
                source=self.source,
                timestamp=datetime.now(),
                metadata={'provider': 'yahoo_finance'},
                success=True
            )
            
        except Exception as e:
            logger.error(f"Error fetching fundamental data from Yahoo Finance: {e}")
            return MarketDataResponse(
                symbol=symbol,
                data_type=DataType.FUNDAMENTAL,
                timeframe=TimeFrame.DAILY,
                data=pd.DataFrame(),
                source=self.source,
                timestamp=datetime.now(),
                metadata={},
                success=False,
                error_message=str(e)
            )
    
    async def get_real_time_price(self, symbol: str) -> Optional[PriceData]:
        """Get real-time price from Yahoo Finance"""
        try:
            # Simulate real-time price
            base_price = 100.0 + hash(symbol) % 100
            current_price = base_price * (1 + np.random.normal(0, 0.01))
            
            return PriceData(
                symbol=symbol,
                timestamp=datetime.now(),
                open=current_price * 0.99,
                high=current_price * 1.01,
                low=current_price * 0.98,
                close=current_price,
                volume=np.random.randint(100000, 1000000),
                adjusted_close=current_price
            )
            
        except Exception as e:
            logger.error(f"Error fetching real-time price from Yahoo Finance: {e}")
            return None


class MarketDataProvider:
    """Main market data provider with multiple sources"""
    
    def __init__(self, primary_source: DataSource = DataSource.YAHOO_FINANCE):
        self.primary_source = primary_source
        self.providers = {
            DataSource.YAHOO_FINANCE: YahooFinanceProvider()
        }
        self.cache = {}
        self.cache_ttl = timedelta(minutes=5)
        
    def add_provider(self, source: DataSource, provider: DataProvider):
        """Add a new data provider"""
        self.providers[source] = provider
    
    async def get_price_data(self, symbol: str, timeframe: TimeFrame = TimeFrame.DAILY,
                           start_date: Optional[datetime] = None,
                           end_date: Optional[datetime] = None,
                           source: Optional[DataSource] = None) -> MarketDataResponse:
        """Get price data for a symbol"""
        try:
            # Set default dates if not provided
            if end_date is None:
                end_date = datetime.now()
            if start_date is None:
                start_date = end_date - timedelta(days=365)
            
            # Use specified source or primary source
            data_source = source or self.primary_source
            
            # Check cache first
            cache_key = f"{symbol}_{timeframe.value}_{start_date}_{end_date}_{data_source.value}"
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # Get provider
            provider = self.providers.get(data_source)
            if not provider:
                raise ValueError(f"No provider available for source: {data_source}")
            
            # Fetch data
            response = await provider.get_price_data(symbol, timeframe, start_date, end_date)
            
            # Cache successful responses
            if response.success:
                self.cache[cache_key] = {
                    'data': response,
                    'timestamp': datetime.now()
                }
            
            return response
            
        except Exception as e:
            logger.error(f"Error getting price data: {e}")
            return MarketDataResponse(
                symbol=symbol,
                data_type=DataType.PRICE,
                timeframe=timeframe,
                data=pd.DataFrame(),
                source=data_source or self.primary_source,
                timestamp=datetime.now(),
                metadata={},
                success=False,
                error_message=str(e)
            )
    
    async def get_fundamental_data(self, symbol: str,
                                 source: Optional[DataSource] = None) -> MarketDataResponse:
        """Get fundamental data for a symbol"""
        try:
            data_source = source or self.primary_source
            
            # Check cache
            cache_key = f"{symbol}_fundamental_{data_source.value}"
            if self._is_cache_valid(cache_key):
                return self.cache[cache_key]['data']
            
            # Get provider
            provider = self.providers.get(data_source)
            if not provider:
                raise ValueError(f"No provider available for source: {data_source}")
            
            # Fetch data
            response = await provider.get_fundamental_data(symbol)
            
            # Cache successful responses
            if response.success:
                self.cache[cache_key] = {
                    'data': response,
                    'timestamp': datetime.now()
                }
            
            return response
            
        except Exception as e:
            logger.error(f"Error getting fundamental data: {e}")
            return MarketDataResponse(
                symbol=symbol,
                data_type=DataType.FUNDAMENTAL,
                timeframe=TimeFrame.DAILY,
                data=pd.DataFrame(),
                source=data_source or self.primary_source,
                timestamp=datetime.now(),
                metadata={},
                success=False,
                error_message=str(e)
            )
    
    async def get_real_time_price(self, symbol: str,
                                source: Optional[DataSource] = None) -> Optional[PriceData]:
        """Get real-time price for a symbol"""
        try:
            data_source = source or self.primary_source
            
            # Get provider
            provider = self.providers.get(data_source)
            if not provider:
                raise ValueError(f"No provider available for source: {data_source}")
            
            # Fetch real-time data
            return await provider.get_real_time_price(symbol)
            
        except Exception as e:
            logger.error(f"Error getting real-time price: {e}")
            return None
    
    async def get_multiple_symbols(self, symbols: List[str],
                                 timeframe: TimeFrame = TimeFrame.DAILY,
                                 start_date: Optional[datetime] = None,
                                 end_date: Optional[datetime] = None) -> Dict[str, MarketDataResponse]:
        """Get price data for multiple symbols"""
        tasks = []
        for symbol in symbols:
            task = self.get_price_data(symbol, timeframe, start_date, end_date)
            tasks.append(task)
        
        responses = await asyncio.gather(*tasks, return_exceptions=True)
        
        result = {}
        for symbol, response in zip(symbols, responses):
            if isinstance(response, Exception):
                logger.error(f"Error fetching data for {symbol}: {response}")
                result[symbol] = MarketDataResponse(
                    symbol=symbol,
                    data_type=DataType.PRICE,
                    timeframe=timeframe,
                    data=pd.DataFrame(),
                    source=self.primary_source,
                    timestamp=datetime.now(),
                    metadata={},
                    success=False,
                    error_message=str(response)
                )
            else:
                result[symbol] = response
        
        return result
    
    def get_available_symbols(self, source: Optional[DataSource] = None) -> List[str]:
        """Get list of available symbols"""
        # This would typically query the data provider's API
        # For now, return a sample list
        return [
            'AAPL', 'GOOGL', 'MSFT', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
            'SPY', 'QQQ', 'IWM', 'TLT', 'GLD', 'VIX', 'EURUSD', 'GBPUSD'
        ]
    
    def get_market_status(self) -> Dict[str, Any]:
        """Get current market status"""
        now = datetime.now()
        
        # Simple market hours check (US markets)
        market_open = now.replace(hour=9, minute=30, second=0, microsecond=0)
        market_close = now.replace(hour=16, minute=0, second=0, microsecond=0)
        
        is_weekday = now.weekday() < 5
        is_market_hours = market_open <= now <= market_close
        
        return {
            'is_open': is_weekday and is_market_hours,
            'next_open': market_open if now < market_open else market_open + timedelta(days=1),
            'next_close': market_close if now < market_close else market_close + timedelta(days=1),
            'timezone': 'US/Eastern',
            'current_time': now
        }
    
    def calculate_returns(self, price_data: pd.DataFrame,
                         method: str = 'simple') -> pd.Series:
        """Calculate returns from price data"""
        try:
            if 'close' not in price_data.columns:
                raise ValueError("Price data must contain 'close' column")
            
            prices = price_data['close']
            
            if method == 'simple':
                returns = prices.pct_change().dropna()
            elif method == 'log':
                returns = np.log(prices / prices.shift(1)).dropna()
            else:
                raise ValueError(f"Unknown return calculation method: {method}")
            
            return returns
            
        except Exception as e:
            logger.error(f"Error calculating returns: {e}")
            return pd.Series()
    
    def resample_data(self, data: pd.DataFrame, target_timeframe: TimeFrame) -> pd.DataFrame:
        """Resample data to different timeframe"""
        try:
            # Map timeframes to pandas frequency strings
            freq_map = {
                TimeFrame.MINUTE_1: '1T',
                TimeFrame.MINUTE_5: '5T',
                TimeFrame.MINUTE_15: '15T',
                TimeFrame.MINUTE_30: '30T',
                TimeFrame.HOUR_1: '1H',
                TimeFrame.HOUR_4: '4H',
                TimeFrame.DAILY: '1D',
                TimeFrame.WEEKLY: '1W',
                TimeFrame.MONTHLY: '1M'
            }
            
            freq = freq_map.get(target_timeframe)
            if not freq:
                raise ValueError(f"Unsupported timeframe: {target_timeframe}")
            
            # Resample OHLCV data
            resampled = data.resample(freq).agg({
                'open': 'first',
                'high': 'max',
                'low': 'min',
                'close': 'last',
                'volume': 'sum'
            }).dropna()
            
            return resampled
            
        except Exception as e:
            logger.error(f"Error resampling data: {e}")
            return pd.DataFrame()
    
    def _is_cache_valid(self, cache_key: str) -> bool:
        """Check if cached data is still valid"""
        if cache_key not in self.cache:
            return False
        
        cache_time = self.cache[cache_key]['timestamp']
        return datetime.now() - cache_time < self.cache_ttl
    
    def clear_cache(self):
        """Clear the data cache"""
        self.cache.clear()
    
    def get_cache_stats(self) -> Dict[str, Any]:
        """Get cache statistics"""
        return {
            'cache_size': len(self.cache),
            'cache_keys': list(self.cache.keys()),
            'cache_ttl_minutes': self.cache_ttl.total_seconds() / 60
        }