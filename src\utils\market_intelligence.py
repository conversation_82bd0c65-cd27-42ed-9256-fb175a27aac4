#!/usr/bin/env python3
"""
Market Intelligence
Advanced market intelligence gathering and analysis system.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime, timed<PERSON>ta
from collections import defaultdict
import statistics
import asyncio

logger = logging.getLogger(__name__)

class IntelligenceType(Enum):
    """Types of market intelligence."""
    PRICE_ACTION = "price_action"
    VOLUME_ANALYSIS = "volume_analysis"
    SENTIMENT_ANALYSIS = "sentiment_analysis"
    NEWS_ANALYSIS = "news_analysis"
    SOCIAL_MEDIA = "social_media"
    INSTITUTIONAL_FLOW = "institutional_flow"
    TECHNICAL_SIGNALS = "technical_signals"
    FUNDAMENTAL_DATA = "fundamental_data"
    MARKET_STRUCTURE = "market_structure"
    CORRELATION_ANALYSIS = "correlation_analysis"

class IntelligenceSource(Enum):
    """Sources of market intelligence."""
    PRICE_DATA = "price_data"
    NEWS_FEEDS = "news_feeds"
    SOCIAL_MEDIA = "social_media"
    ECONOMIC_DATA = "economic_data"
    EARNINGS_DATA = "earnings_data"
    INSIDER_TRADING = "insider_trading"
    OPTIONS_FLOW = "options_flow"
    FUTURES_DATA = "futures_data"
    CRYPTO_DATA = "crypto_data"
    FOREX_DATA = "forex_data"

class ConfidenceLevel(Enum):
    """Confidence levels for intelligence."""
    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"

class MarketRegime(Enum):
    """Market regime classifications."""
    BULL_MARKET = "bull_market"
    BEAR_MARKET = "bear_market"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    TRENDING = "trending"
    RANGING = "ranging"

@dataclass
class IntelligenceSignal:
    """A single intelligence signal."""
    signal_id: str
    intelligence_type: IntelligenceType
    source: IntelligenceSource
    symbol: str
    timeframe: str
    signal_strength: float
    confidence: ConfidenceLevel
    direction: str  # 'bullish', 'bearish', 'neutral'
    description: str
    supporting_data: Dict[str, Any]
    timestamp: datetime
    expiry: Optional[datetime] = None
    metadata: Dict[str, Any] = None

@dataclass
class MarketIntelligenceReport:
    """Comprehensive market intelligence report."""
    report_id: str
    symbol: str
    timeframe: str
    market_regime: MarketRegime
    overall_sentiment: str
    confidence_score: float
    key_signals: List[IntelligenceSignal]
    supporting_signals: List[IntelligenceSignal]
    conflicting_signals: List[IntelligenceSignal]
    risk_factors: List[str]
    opportunities: List[str]
    price_targets: Dict[str, float]
    volume_analysis: Dict[str, Any]
    sentiment_breakdown: Dict[str, float]
    correlation_insights: Dict[str, Any]
    market_structure_analysis: Dict[str, Any]
    intelligence_summary: str
    recommendations: List[str]
    next_review: datetime
    report_timestamp: datetime
    metadata: Dict[str, Any] = None

class MarketIntelligence:
    """Advanced market intelligence system."""
    
    def __init__(self):
        self.intelligence_weights = {
            IntelligenceType.PRICE_ACTION: 1.2,
            IntelligenceType.VOLUME_ANALYSIS: 1.1,
            IntelligenceType.SENTIMENT_ANALYSIS: 0.8,
            IntelligenceType.NEWS_ANALYSIS: 0.9,
            IntelligenceType.SOCIAL_MEDIA: 0.6,
            IntelligenceType.INSTITUTIONAL_FLOW: 1.3,
            IntelligenceType.TECHNICAL_SIGNALS: 1.0,
            IntelligenceType.FUNDAMENTAL_DATA: 1.1,
            IntelligenceType.MARKET_STRUCTURE: 1.0,
            IntelligenceType.CORRELATION_ANALYSIS: 0.7
        }
        
        self.source_reliability = {
            IntelligenceSource.PRICE_DATA: 1.0,
            IntelligenceSource.NEWS_FEEDS: 0.8,
            IntelligenceSource.SOCIAL_MEDIA: 0.5,
            IntelligenceSource.ECONOMIC_DATA: 0.9,
            IntelligenceSource.EARNINGS_DATA: 0.9,
            IntelligenceSource.INSIDER_TRADING: 0.7,
            IntelligenceSource.OPTIONS_FLOW: 0.8,
            IntelligenceSource.FUTURES_DATA: 0.9,
            IntelligenceSource.CRYPTO_DATA: 0.7,
            IntelligenceSource.FOREX_DATA: 0.8
        }
        
        self.confidence_thresholds = {
            ConfidenceLevel.VERY_HIGH: 0.9,
            ConfidenceLevel.HIGH: 0.75,
            ConfidenceLevel.MODERATE: 0.6,
            ConfidenceLevel.LOW: 0.4,
            ConfidenceLevel.VERY_LOW: 0.0
        }
        
        self.active_signals = []
        self.historical_signals = []
    
    async def gather_intelligence(self, symbol: str, timeframe: str,
                                intelligence_types: List[IntelligenceType] = None) -> MarketIntelligenceReport:
        """Gather comprehensive market intelligence."""
        try:
            if intelligence_types is None:
                intelligence_types = list(IntelligenceType)
            
            # Gather signals from different sources
            all_signals = []
            
            for intel_type in intelligence_types:
                signals = await self._gather_intelligence_by_type(symbol, timeframe, intel_type)
                all_signals.extend(signals)
            
            # Filter and validate signals
            valid_signals = self._validate_signals(all_signals)
            
            # Classify signals
            key_signals, supporting_signals, conflicting_signals = self._classify_signals(valid_signals)
            
            # Determine market regime
            market_regime = self._determine_market_regime(valid_signals, symbol, timeframe)
            
            # Calculate overall sentiment
            overall_sentiment = self._calculate_overall_sentiment(valid_signals)
            
            # Calculate confidence score
            confidence_score = self._calculate_confidence_score(valid_signals)
            
            # Analyze volume
            volume_analysis = self._analyze_volume(valid_signals)
            
            # Break down sentiment
            sentiment_breakdown = self._breakdown_sentiment(valid_signals)
            
            # Analyze correlations
            correlation_insights = self._analyze_correlations(valid_signals, symbol)
            
            # Analyze market structure
            market_structure_analysis = self._analyze_market_structure(valid_signals)
            
            # Identify risks and opportunities
            risk_factors = self._identify_risk_factors(valid_signals, market_regime)
            opportunities = self._identify_opportunities(valid_signals, market_regime)
            
            # Calculate price targets
            price_targets = self._calculate_price_targets(valid_signals, overall_sentiment)
            
            # Generate summary and recommendations
            intelligence_summary = self._generate_intelligence_summary(valid_signals, market_regime, overall_sentiment)
            recommendations = self._generate_recommendations(valid_signals, market_regime, risk_factors, opportunities)
            
            # Create report
            report = MarketIntelligenceReport(
                report_id=f"intel_{symbol}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                symbol=symbol,
                timeframe=timeframe,
                market_regime=market_regime,
                overall_sentiment=overall_sentiment,
                confidence_score=confidence_score,
                key_signals=key_signals,
                supporting_signals=supporting_signals,
                conflicting_signals=conflicting_signals,
                risk_factors=risk_factors,
                opportunities=opportunities,
                price_targets=price_targets,
                volume_analysis=volume_analysis,
                sentiment_breakdown=sentiment_breakdown,
                correlation_insights=correlation_insights,
                market_structure_analysis=market_structure_analysis,
                intelligence_summary=intelligence_summary,
                recommendations=recommendations,
                next_review=datetime.now() + timedelta(hours=1),
                report_timestamp=datetime.now(),
                metadata={
                    'total_signals': len(valid_signals),
                    'intelligence_types_analyzed': len(intelligence_types),
                    'signal_distribution': self._get_signal_distribution(valid_signals)
                }
            )
            
            return report
            
        except Exception as e:
            logger.error(f"Error gathering intelligence: {e}")
            return self._create_empty_report(symbol, timeframe)
    
    async def _gather_intelligence_by_type(self, symbol: str, timeframe: str,
                                         intel_type: IntelligenceType) -> List[IntelligenceSignal]:
        """Gather intelligence signals by type."""
        signals = []
        
        try:
            if intel_type == IntelligenceType.PRICE_ACTION:
                signals.extend(await self._analyze_price_action(symbol, timeframe))
            elif intel_type == IntelligenceType.VOLUME_ANALYSIS:
                signals.extend(await self._analyze_volume_patterns(symbol, timeframe))
            elif intel_type == IntelligenceType.SENTIMENT_ANALYSIS:
                signals.extend(await self._analyze_sentiment(symbol, timeframe))
            elif intel_type == IntelligenceType.NEWS_ANALYSIS:
                signals.extend(await self._analyze_news(symbol, timeframe))
            elif intel_type == IntelligenceType.SOCIAL_MEDIA:
                signals.extend(await self._analyze_social_media(symbol, timeframe))
            elif intel_type == IntelligenceType.INSTITUTIONAL_FLOW:
                signals.extend(await self._analyze_institutional_flow(symbol, timeframe))
            elif intel_type == IntelligenceType.TECHNICAL_SIGNALS:
                signals.extend(await self._analyze_technical_signals(symbol, timeframe))
            elif intel_type == IntelligenceType.FUNDAMENTAL_DATA:
                signals.extend(await self._analyze_fundamental_data(symbol, timeframe))
            elif intel_type == IntelligenceType.MARKET_STRUCTURE:
                signals.extend(await self._analyze_market_structure_signals(symbol, timeframe))
            elif intel_type == IntelligenceType.CORRELATION_ANALYSIS:
                signals.extend(await self._analyze_correlation_signals(symbol, timeframe))
        
        except Exception as e:
            logger.error(f"Error gathering {intel_type.value} intelligence: {e}")
        
        return signals
    
    async def _analyze_price_action(self, symbol: str, timeframe: str) -> List[IntelligenceSignal]:
        """Analyze price action for intelligence signals."""
        signals = []
        
        try:
            # Simulate price action analysis
            # In a real implementation, this would analyze actual price data
            
            # Example signals
            signals.append(IntelligenceSignal(
                signal_id=f"price_action_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.PRICE_ACTION,
                source=IntelligenceSource.PRICE_DATA,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=0.7,
                confidence=ConfidenceLevel.HIGH,
                direction="bullish",
                description="Strong upward price momentum detected",
                supporting_data={
                    'momentum_score': 0.75,
                    'trend_strength': 0.8,
                    'support_level': 100.0,
                    'resistance_level': 110.0
                },
                timestamp=datetime.now()
            ))
        
        except Exception as e:
            logger.error(f"Error analyzing price action: {e}")
        
        return signals
    
    async def _analyze_volume_patterns(self, symbol: str, timeframe: str) -> List[IntelligenceSignal]:
        """Analyze volume patterns for intelligence signals."""
        signals = []
        
        try:
            # Simulate volume analysis
            signals.append(IntelligenceSignal(
                signal_id=f"volume_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.VOLUME_ANALYSIS,
                source=IntelligenceSource.PRICE_DATA,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=0.6,
                confidence=ConfidenceLevel.MODERATE,
                direction="bullish",
                description="Above-average volume supporting price movement",
                supporting_data={
                    'volume_ratio': 1.5,
                    'volume_trend': 'increasing',
                    'accumulation_score': 0.65
                },
                timestamp=datetime.now()
            ))
        
        except Exception as e:
            logger.error(f"Error analyzing volume patterns: {e}")
        
        return signals
    
    async def _analyze_sentiment(self, symbol: str, timeframe: str) -> List[IntelligenceSignal]:
        """Analyze market sentiment for intelligence signals."""
        signals = []
        
        try:
            # Simulate sentiment analysis
            signals.append(IntelligenceSignal(
                signal_id=f"sentiment_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.SENTIMENT_ANALYSIS,
                source=IntelligenceSource.SOCIAL_MEDIA,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=0.5,
                confidence=ConfidenceLevel.MODERATE,
                direction="neutral",
                description="Mixed sentiment with slight bullish bias",
                supporting_data={
                    'sentiment_score': 0.55,
                    'bullish_ratio': 0.6,
                    'bearish_ratio': 0.4,
                    'sentiment_trend': 'improving'
                },
                timestamp=datetime.now()
            ))
        
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
        
        return signals
    
    async def _analyze_news(self, symbol: str, timeframe: str) -> List[IntelligenceSignal]:
        """Analyze news for intelligence signals."""
        signals = []
        
        try:
            # Simulate news analysis
            signals.append(IntelligenceSignal(
                signal_id=f"news_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.NEWS_ANALYSIS,
                source=IntelligenceSource.NEWS_FEEDS,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=0.8,
                confidence=ConfidenceLevel.HIGH,
                direction="bullish",
                description="Positive news coverage and analyst upgrades",
                supporting_data={
                    'news_sentiment': 0.75,
                    'analyst_upgrades': 3,
                    'analyst_downgrades': 1,
                    'news_volume': 'high'
                },
                timestamp=datetime.now()
            ))
        
        except Exception as e:
            logger.error(f"Error analyzing news: {e}")
        
        return signals
    
    async def _analyze_social_media(self, symbol: str, timeframe: str) -> List[IntelligenceSignal]:
        """Analyze social media for intelligence signals."""
        signals = []
        
        try:
            # Simulate social media analysis
            signals.append(IntelligenceSignal(
                signal_id=f"social_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.SOCIAL_MEDIA,
                source=IntelligenceSource.SOCIAL_MEDIA,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=0.4,
                confidence=ConfidenceLevel.LOW,
                direction="bullish",
                description="Increased social media mentions with positive tone",
                supporting_data={
                    'mention_volume': 150,
                    'positive_mentions': 90,
                    'negative_mentions': 60,
                    'trending_score': 0.6
                },
                timestamp=datetime.now()
            ))
        
        except Exception as e:
            logger.error(f"Error analyzing social media: {e}")
        
        return signals
    
    async def _analyze_institutional_flow(self, symbol: str, timeframe: str) -> List[IntelligenceSignal]:
        """Analyze institutional flow for intelligence signals."""
        signals = []
        
        try:
            # Simulate institutional flow analysis
            signals.append(IntelligenceSignal(
                signal_id=f"institutional_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.INSTITUTIONAL_FLOW,
                source=IntelligenceSource.OPTIONS_FLOW,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=0.9,
                confidence=ConfidenceLevel.VERY_HIGH,
                direction="bullish",
                description="Strong institutional buying detected",
                supporting_data={
                    'net_flow': 50000000,
                    'buy_sell_ratio': 2.5,
                    'large_block_trades': 15,
                    'institutional_sentiment': 'bullish'
                },
                timestamp=datetime.now()
            ))
        
        except Exception as e:
            logger.error(f"Error analyzing institutional flow: {e}")
        
        return signals
    
    async def _analyze_technical_signals(self, symbol: str, timeframe: str) -> List[IntelligenceSignal]:
        """Analyze technical signals for intelligence."""
        signals = []
        
        try:
            # Simulate technical signal analysis
            signals.append(IntelligenceSignal(
                signal_id=f"technical_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.TECHNICAL_SIGNALS,
                source=IntelligenceSource.PRICE_DATA,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=0.75,
                confidence=ConfidenceLevel.HIGH,
                direction="bullish",
                description="Multiple technical indicators showing bullish signals",
                supporting_data={
                    'rsi': 65,
                    'macd_signal': 'bullish',
                    'moving_average_trend': 'up',
                    'support_resistance': 'above_support'
                },
                timestamp=datetime.now()
            ))
        
        except Exception as e:
            logger.error(f"Error analyzing technical signals: {e}")
        
        return signals
    
    async def _analyze_fundamental_data(self, symbol: str, timeframe: str) -> List[IntelligenceSignal]:
        """Analyze fundamental data for intelligence signals."""
        signals = []
        
        try:
            # Simulate fundamental analysis
            signals.append(IntelligenceSignal(
                signal_id=f"fundamental_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.FUNDAMENTAL_DATA,
                source=IntelligenceSource.EARNINGS_DATA,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=0.7,
                confidence=ConfidenceLevel.HIGH,
                direction="bullish",
                description="Strong fundamental metrics and earnings growth",
                supporting_data={
                    'pe_ratio': 15.5,
                    'earnings_growth': 0.25,
                    'revenue_growth': 0.15,
                    'debt_to_equity': 0.3
                },
                timestamp=datetime.now()
            ))
        
        except Exception as e:
            logger.error(f"Error analyzing fundamental data: {e}")
        
        return signals
    
    async def _analyze_market_structure_signals(self, symbol: str, timeframe: str) -> List[IntelligenceSignal]:
        """Analyze market structure for intelligence signals."""
        signals = []
        
        try:
            # Simulate market structure analysis
            signals.append(IntelligenceSignal(
                signal_id=f"structure_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.MARKET_STRUCTURE,
                source=IntelligenceSource.PRICE_DATA,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=0.6,
                confidence=ConfidenceLevel.MODERATE,
                direction="neutral",
                description="Market structure showing consolidation pattern",
                supporting_data={
                    'trend_structure': 'consolidating',
                    'support_levels': [100, 95, 90],
                    'resistance_levels': [110, 115, 120],
                    'volatility_regime': 'normal'
                },
                timestamp=datetime.now()
            ))
        
        except Exception as e:
            logger.error(f"Error analyzing market structure: {e}")
        
        return signals
    
    async def _analyze_correlation_signals(self, symbol: str, timeframe: str) -> List[IntelligenceSignal]:
        """Analyze correlation patterns for intelligence signals."""
        signals = []
        
        try:
            # Simulate correlation analysis
            signals.append(IntelligenceSignal(
                signal_id=f"correlation_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.CORRELATION_ANALYSIS,
                source=IntelligenceSource.PRICE_DATA,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=0.5,
                confidence=ConfidenceLevel.MODERATE,
                direction="neutral",
                description="Correlation patterns suggest sector rotation",
                supporting_data={
                    'sector_correlation': 0.7,
                    'market_correlation': 0.8,
                    'correlation_trend': 'increasing',
                    'relative_strength': 1.1
                },
                timestamp=datetime.now()
            ))
        
        except Exception as e:
            logger.error(f"Error analyzing correlations: {e}")
        
        return signals
    
    def _validate_signals(self, signals: List[IntelligenceSignal]) -> List[IntelligenceSignal]:
        """Validate and filter intelligence signals."""
        valid_signals = []
        
        try:
            for signal in signals:
                # Check signal age
                if signal.expiry and datetime.now() > signal.expiry:
                    continue
                
                # Check signal strength
                if signal.signal_strength < 0.1:
                    continue
                
                # Check confidence level
                if signal.confidence == ConfidenceLevel.VERY_LOW:
                    continue
                
                valid_signals.append(signal)
        
        except Exception as e:
            logger.error(f"Error validating signals: {e}")
            return signals
        
        return valid_signals
    
    def _classify_signals(self, signals: List[IntelligenceSignal]) -> Tuple[List[IntelligenceSignal], List[IntelligenceSignal], List[IntelligenceSignal]]:
        """Classify signals into key, supporting, and conflicting."""
        key_signals = []
        supporting_signals = []
        conflicting_signals = []
        
        try:
            # Sort signals by strength and confidence
            sorted_signals = sorted(signals, key=lambda x: (x.signal_strength, 
                                                           self._confidence_to_numeric(x.confidence)), 
                                  reverse=True)
            
            # Determine overall direction
            directions = [s.direction for s in signals]
            direction_counts = {d: directions.count(d) for d in set(directions)}
            dominant_direction = max(direction_counts, key=direction_counts.get)
            
            # Classify signals
            for signal in sorted_signals:
                if signal.signal_strength >= 0.7 and signal.confidence in [ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH]:
                    key_signals.append(signal)
                elif signal.direction == dominant_direction:
                    supporting_signals.append(signal)
                else:
                    conflicting_signals.append(signal)
        
        except Exception as e:
            logger.error(f"Error classifying signals: {e}")
            return signals[:5], [], []
        
        return key_signals[:5], supporting_signals[:10], conflicting_signals[:5]
    
    def _determine_market_regime(self, signals: List[IntelligenceSignal], symbol: str, timeframe: str) -> MarketRegime:
        """Determine current market regime."""
        try:
            if not signals:
                return MarketRegime.SIDEWAYS
            
            # Analyze signal patterns
            directions = [s.direction for s in signals]
            strengths = [s.signal_strength for s in signals]
            
            bullish_count = directions.count('bullish')
            bearish_count = directions.count('bearish')
            neutral_count = directions.count('neutral')
            
            avg_strength = statistics.mean(strengths)
            
            # Determine regime
            if bullish_count > bearish_count * 1.5 and avg_strength > 0.6:
                return MarketRegime.BULL_MARKET
            elif bearish_count > bullish_count * 1.5 and avg_strength > 0.6:
                return MarketRegime.BEAR_MARKET
            elif avg_strength > 0.7:
                return MarketRegime.HIGH_VOLATILITY
            elif avg_strength < 0.3:
                return MarketRegime.LOW_VOLATILITY
            elif neutral_count > max(bullish_count, bearish_count):
                return MarketRegime.RANGING
            else:
                return MarketRegime.SIDEWAYS
                
        except Exception as e:
            logger.error(f"Error determining market regime: {e}")
            return MarketRegime.SIDEWAYS
    
    def _calculate_overall_sentiment(self, signals: List[IntelligenceSignal]) -> str:
        """Calculate overall market sentiment."""
        try:
            if not signals:
                return "neutral"
            
            # Weight signals by strength and confidence
            weighted_sentiment = 0.0
            total_weight = 0.0
            
            for signal in signals:
                # Convert direction to numeric
                if signal.direction == 'bullish':
                    direction_value = 1.0
                elif signal.direction == 'bearish':
                    direction_value = -1.0
                else:
                    direction_value = 0.0
                
                # Calculate weight
                confidence_weight = self._confidence_to_numeric(signal.confidence)
                weight = signal.signal_strength * confidence_weight
                
                weighted_sentiment += direction_value * weight
                total_weight += weight
            
            if total_weight > 0:
                normalized_sentiment = weighted_sentiment / total_weight
            else:
                normalized_sentiment = 0.0
            
            # Convert to sentiment string
            if normalized_sentiment > 0.3:
                return "bullish"
            elif normalized_sentiment < -0.3:
                return "bearish"
            else:
                return "neutral"
                
        except Exception as e:
            logger.error(f"Error calculating overall sentiment: {e}")
            return "neutral"
    
    def _calculate_confidence_score(self, signals: List[IntelligenceSignal]) -> float:
        """Calculate overall confidence score."""
        try:
            if not signals:
                return 0.0
            
            # Calculate weighted average confidence
            total_confidence = 0.0
            total_weight = 0.0
            
            for signal in signals:
                confidence_value = self._confidence_to_numeric(signal.confidence)
                weight = signal.signal_strength
                
                total_confidence += confidence_value * weight
                total_weight += weight
            
            if total_weight > 0:
                return total_confidence / total_weight
            else:
                return 0.0
                
        except Exception as e:
            logger.error(f"Error calculating confidence score: {e}")
            return 0.0
    
    def _confidence_to_numeric(self, confidence: ConfidenceLevel) -> float:
        """Convert confidence level to numeric value."""
        mapping = {
            ConfidenceLevel.VERY_LOW: 0.1,
            ConfidenceLevel.LOW: 0.3,
            ConfidenceLevel.MODERATE: 0.5,
            ConfidenceLevel.HIGH: 0.8,
            ConfidenceLevel.VERY_HIGH: 1.0
        }
        return mapping.get(confidence, 0.5)
    
    def _analyze_volume(self, signals: List[IntelligenceSignal]) -> Dict[str, Any]:
        """Analyze volume-related signals."""
        volume_analysis = {
            'volume_trend': 'neutral',
            'volume_strength': 0.5,
            'accumulation_distribution': 'neutral',
            'volume_price_relationship': 'normal'
        }
        
        try:
            volume_signals = [s for s in signals if s.intelligence_type == IntelligenceType.VOLUME_ANALYSIS]
            
            if volume_signals:
                # Analyze volume signals
                avg_strength = statistics.mean([s.signal_strength for s in volume_signals])
                volume_analysis['volume_strength'] = avg_strength
                
                # Determine volume trend
                bullish_volume = len([s for s in volume_signals if s.direction == 'bullish'])
                bearish_volume = len([s for s in volume_signals if s.direction == 'bearish'])
                
                if bullish_volume > bearish_volume:
                    volume_analysis['volume_trend'] = 'increasing'
                    volume_analysis['accumulation_distribution'] = 'accumulation'
                elif bearish_volume > bullish_volume:
                    volume_analysis['volume_trend'] = 'decreasing'
                    volume_analysis['accumulation_distribution'] = 'distribution'
        
        except Exception as e:
            logger.error(f"Error analyzing volume: {e}")
        
        return volume_analysis
    
    def _breakdown_sentiment(self, signals: List[IntelligenceSignal]) -> Dict[str, float]:
        """Break down sentiment by source and type."""
        sentiment_breakdown = {
            'technical_sentiment': 0.5,
            'fundamental_sentiment': 0.5,
            'news_sentiment': 0.5,
            'social_sentiment': 0.5,
            'institutional_sentiment': 0.5
        }
        
        try:
            # Group signals by type
            type_groups = defaultdict(list)
            for signal in signals:
                type_groups[signal.intelligence_type].append(signal)
            
            # Calculate sentiment for each type
            for intel_type, type_signals in type_groups.items():
                if type_signals:
                    sentiment_values = []
                    for signal in type_signals:
                        if signal.direction == 'bullish':
                            sentiment_values.append(0.5 + signal.signal_strength * 0.5)
                        elif signal.direction == 'bearish':
                            sentiment_values.append(0.5 - signal.signal_strength * 0.5)
                        else:
                            sentiment_values.append(0.5)
                    
                    avg_sentiment = statistics.mean(sentiment_values)
                    
                    # Map to breakdown categories
                    if intel_type in [IntelligenceType.TECHNICAL_SIGNALS, IntelligenceType.PRICE_ACTION]:
                        sentiment_breakdown['technical_sentiment'] = avg_sentiment
                    elif intel_type == IntelligenceType.FUNDAMENTAL_DATA:
                        sentiment_breakdown['fundamental_sentiment'] = avg_sentiment
                    elif intel_type == IntelligenceType.NEWS_ANALYSIS:
                        sentiment_breakdown['news_sentiment'] = avg_sentiment
                    elif intel_type == IntelligenceType.SOCIAL_MEDIA:
                        sentiment_breakdown['social_sentiment'] = avg_sentiment
                    elif intel_type == IntelligenceType.INSTITUTIONAL_FLOW:
                        sentiment_breakdown['institutional_sentiment'] = avg_sentiment
        
        except Exception as e:
            logger.error(f"Error breaking down sentiment: {e}")
        
        return sentiment_breakdown
    
    def _analyze_correlations(self, signals: List[IntelligenceSignal], symbol: str) -> Dict[str, Any]:
        """Analyze correlation insights."""
        correlation_insights = {
            'market_correlation': 0.5,
            'sector_correlation': 0.5,
            'correlation_trend': 'stable',
            'relative_strength': 1.0,
            'correlation_risk': 'moderate'
        }
        
        try:
            correlation_signals = [s for s in signals if s.intelligence_type == IntelligenceType.CORRELATION_ANALYSIS]
            
            if correlation_signals:
                # Extract correlation data
                for signal in correlation_signals:
                    supporting_data = signal.supporting_data or {}
                    
                    if 'market_correlation' in supporting_data:
                        correlation_insights['market_correlation'] = supporting_data['market_correlation']
                    
                    if 'sector_correlation' in supporting_data:
                        correlation_insights['sector_correlation'] = supporting_data['sector_correlation']
                    
                    if 'relative_strength' in supporting_data:
                        correlation_insights['relative_strength'] = supporting_data['relative_strength']
                    
                    if 'correlation_trend' in supporting_data:
                        correlation_insights['correlation_trend'] = supporting_data['correlation_trend']
                
                # Assess correlation risk
                avg_correlation = (correlation_insights['market_correlation'] + 
                                 correlation_insights['sector_correlation']) / 2
                
                if avg_correlation > 0.8:
                    correlation_insights['correlation_risk'] = 'high'
                elif avg_correlation > 0.6:
                    correlation_insights['correlation_risk'] = 'moderate'
                else:
                    correlation_insights['correlation_risk'] = 'low'
        
        except Exception as e:
            logger.error(f"Error analyzing correlations: {e}")
        
        return correlation_insights
    
    def _analyze_market_structure(self, signals: List[IntelligenceSignal]) -> Dict[str, Any]:
        """Analyze market structure."""
        structure_analysis = {
            'trend_structure': 'neutral',
            'support_resistance_strength': 'moderate',
            'volatility_regime': 'normal',
            'liquidity_conditions': 'normal',
            'market_efficiency': 'efficient'
        }
        
        try:
            structure_signals = [s for s in signals if s.intelligence_type == IntelligenceType.MARKET_STRUCTURE]
            
            if structure_signals:
                for signal in structure_signals:
                    supporting_data = signal.supporting_data or {}
                    
                    if 'trend_structure' in supporting_data:
                        structure_analysis['trend_structure'] = supporting_data['trend_structure']
                    
                    if 'volatility_regime' in supporting_data:
                        structure_analysis['volatility_regime'] = supporting_data['volatility_regime']
                    
                    # Assess support/resistance strength
                    if 'support_levels' in supporting_data and 'resistance_levels' in supporting_data:
                        support_count = len(supporting_data['support_levels'])
                        resistance_count = len(supporting_data['resistance_levels'])
                        
                        if support_count >= 3 and resistance_count >= 3:
                            structure_analysis['support_resistance_strength'] = 'strong'
                        elif support_count >= 2 and resistance_count >= 2:
                            structure_analysis['support_resistance_strength'] = 'moderate'
                        else:
                            structure_analysis['support_resistance_strength'] = 'weak'
        
        except Exception as e:
            logger.error(f"Error analyzing market structure: {e}")
        
        return structure_analysis
    
    def _identify_risk_factors(self, signals: List[IntelligenceSignal], market_regime: MarketRegime) -> List[str]:
        """Identify risk factors from intelligence."""
        risk_factors = []
        
        try:
            # Market regime-based risks
            if market_regime == MarketRegime.HIGH_VOLATILITY:
                risk_factors.append("High volatility environment increases risk")
            elif market_regime == MarketRegime.BEAR_MARKET:
                risk_factors.append("Bear market conditions present downside risk")
            
            # Signal-based risks
            conflicting_signals = [s for s in signals if s.direction != self._calculate_overall_sentiment(signals)]
            if len(conflicting_signals) > len(signals) * 0.3:
                risk_factors.append("Significant conflicting signals detected")
            
            # Low confidence risks
            low_confidence_signals = [s for s in signals if s.confidence in [ConfidenceLevel.LOW, ConfidenceLevel.VERY_LOW]]
            if len(low_confidence_signals) > len(signals) * 0.4:
                risk_factors.append("High proportion of low-confidence signals")
            
            # Institutional flow risks
            institutional_signals = [s for s in signals if s.intelligence_type == IntelligenceType.INSTITUTIONAL_FLOW]
            for signal in institutional_signals:
                if signal.direction == 'bearish' and signal.signal_strength > 0.7:
                    risk_factors.append("Strong institutional selling pressure")
        
        except Exception as e:
            logger.error(f"Error identifying risk factors: {e}")
        
        return risk_factors[:5]
    
    def _identify_opportunities(self, signals: List[IntelligenceSignal], market_regime: MarketRegime) -> List[str]:
        """Identify opportunities from intelligence."""
        opportunities = []
        
        try:
            # Market regime-based opportunities
            if market_regime == MarketRegime.BULL_MARKET:
                opportunities.append("Bull market conditions favor long positions")
            elif market_regime == MarketRegime.LOW_VOLATILITY:
                opportunities.append("Low volatility environment suitable for range trading")
            
            # Signal-based opportunities
            strong_signals = [s for s in signals if s.signal_strength > 0.7 and 
                            s.confidence in [ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH]]
            
            if strong_signals:
                opportunities.append(f"Strong {strong_signals[0].direction} signals present trading opportunity")
            
            # Institutional flow opportunities
            institutional_signals = [s for s in signals if s.intelligence_type == IntelligenceType.INSTITUTIONAL_FLOW]
            for signal in institutional_signals:
                if signal.direction == 'bullish' and signal.signal_strength > 0.7:
                    opportunities.append("Strong institutional buying creates momentum opportunity")
            
            # Technical opportunities
            technical_signals = [s for s in signals if s.intelligence_type == IntelligenceType.TECHNICAL_SIGNALS]
            for signal in technical_signals:
                if signal.signal_strength > 0.6:
                    opportunities.append(f"Technical setup favors {signal.direction} move")
        
        except Exception as e:
            logger.error(f"Error identifying opportunities: {e}")
        
        return opportunities[:5]
    
    def _calculate_price_targets(self, signals: List[IntelligenceSignal], overall_sentiment: str) -> Dict[str, float]:
        """Calculate price targets from intelligence."""
        targets = {}
        
        try:
            # Extract target-related data from signals
            target_values = []
            
            for signal in signals:
                supporting_data = signal.supporting_data or {}
                
                # Look for target-related fields
                for key in ['target', 'resistance_level', 'support_level', 'price_target']:
                    if key in supporting_data:
                        value = supporting_data[key]
                        if isinstance(value, (int, float)):
                            target_values.append(value)
                        elif isinstance(value, list):
                            target_values.extend([v for v in value if isinstance(v, (int, float))])
            
            if target_values:
                targets['mean_target'] = statistics.mean(target_values)
                targets['median_target'] = statistics.median(target_values)
                
                if overall_sentiment == 'bullish':
                    targets['upside_target'] = max(target_values)
                elif overall_sentiment == 'bearish':
                    targets['downside_target'] = min(target_values)
        
        except Exception as e:
            logger.error(f"Error calculating price targets: {e}")
        
        return targets
    
    def _generate_intelligence_summary(self, signals: List[IntelligenceSignal], 
                                     market_regime: MarketRegime, overall_sentiment: str) -> str:
        """Generate intelligence summary."""
        try:
            summary_parts = []
            
            # Market regime summary
            summary_parts.append(f"Market regime: {market_regime.value.replace('_', ' ').title()}")
            
            # Overall sentiment
            summary_parts.append(f"Overall sentiment: {overall_sentiment.title()}")
            
            # Signal count
            summary_parts.append(f"Based on {len(signals)} intelligence signals")
            
            # Key signal types
            signal_types = list(set([s.intelligence_type.value for s in signals]))
            summary_parts.append(f"Signal types: {', '.join(signal_types[:3])}")
            
            # Confidence assessment
            high_confidence_count = len([s for s in signals if s.confidence in [ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH]])
            if high_confidence_count > len(signals) * 0.6:
                summary_parts.append("High confidence in analysis")
            elif high_confidence_count > len(signals) * 0.3:
                summary_parts.append("Moderate confidence in analysis")
            else:
                summary_parts.append("Lower confidence due to mixed signals")
            
            return ". ".join(summary_parts) + "."
            
        except Exception as e:
            logger.error(f"Error generating intelligence summary: {e}")
            return "Intelligence analysis completed with mixed results."
    
    def _generate_recommendations(self, signals: List[IntelligenceSignal], market_regime: MarketRegime,
                                risk_factors: List[str], opportunities: List[str]) -> List[str]:
        """Generate actionable recommendations."""
        recommendations = []
        
        try:
            # Overall sentiment-based recommendations
            overall_sentiment = self._calculate_overall_sentiment(signals)
            
            if overall_sentiment == 'bullish':
                recommendations.append("Consider long positions with appropriate risk management")
            elif overall_sentiment == 'bearish':
                recommendations.append("Consider defensive positioning or short opportunities")
            else:
                recommendations.append("Maintain neutral stance until clearer signals emerge")
            
            # Risk-based recommendations
            if len(risk_factors) > 3:
                recommendations.append("Reduce position sizes due to elevated risk factors")
            
            # Opportunity-based recommendations
            if len(opportunities) > 2:
                recommendations.append("Multiple opportunities identified - prioritize highest conviction setups")
            
            # Market regime-based recommendations
            if market_regime == MarketRegime.HIGH_VOLATILITY:
                recommendations.append("Use wider stop-losses and smaller position sizes in high volatility")
            elif market_regime == MarketRegime.LOW_VOLATILITY:
                recommendations.append("Consider range-bound strategies in low volatility environment")
            
            # Signal quality recommendations
            high_quality_signals = [s for s in signals if s.signal_strength > 0.7 and 
                                  s.confidence in [ConfidenceLevel.HIGH, ConfidenceLevel.VERY_HIGH]]
            
            if len(high_quality_signals) < 2:
                recommendations.append("Wait for higher quality signals before taking significant positions")
        
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
        
        return recommendations[:5]
    
    def _get_signal_distribution(self, signals: List[IntelligenceSignal]) -> Dict[str, int]:
        """Get distribution of signal types."""
        distribution = defaultdict(int)
        
        try:
            for signal in signals:
                distribution[signal.intelligence_type.value] += 1
        
        except Exception as e:
            logger.error(f"Error getting signal distribution: {e}")
        
        return dict(distribution)
    
    def _create_empty_report(self, symbol: str, timeframe: str) -> MarketIntelligenceReport:
        """Create an empty report when intelligence gathering fails."""
        return MarketIntelligenceReport(
            report_id=f"empty_{symbol}_{timeframe}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            symbol=symbol,
            timeframe=timeframe,
            market_regime=MarketRegime.SIDEWAYS,
            overall_sentiment="neutral",
            confidence_score=0.0,
            key_signals=[],
            supporting_signals=[],
            conflicting_signals=[],
            risk_factors=["Insufficient intelligence data"],
            opportunities=[],
            price_targets={},
            volume_analysis={},
            sentiment_breakdown={},
            correlation_insights={},
            market_structure_analysis={},
            intelligence_summary="Insufficient data for comprehensive intelligence analysis.",
            recommendations=["Gather more market data before making decisions"],
            next_review=datetime.now() + timedelta(hours=1),
            report_timestamp=datetime.now(),
            metadata={'error': 'Intelligence gathering failed'}
        )
    
    def add_signal(self, signal: IntelligenceSignal):
        """Add a new intelligence signal."""
        try:
            self.active_signals.append(signal)
            
            # Clean up expired signals
            current_time = datetime.now()
            self.active_signals = [s for s in self.active_signals 
                                 if not s.expiry or s.expiry > current_time]
        
        except Exception as e:
            logger.error(f"Error adding signal: {e}")
    
    def get_active_signals(self, symbol: str = None, intelligence_type: IntelligenceType = None) -> List[IntelligenceSignal]:
        """Get active intelligence signals with optional filtering."""
        try:
            signals = self.active_signals.copy()
            
            if symbol:
                signals = [s for s in signals if s.symbol == symbol]
            
            if intelligence_type:
                signals = [s for s in signals if s.intelligence_type == intelligence_type]
            
            return signals
        
        except Exception as e:
            logger.error(f"Error getting active signals: {e}")
            return []
    
    def create_intelligence_signal(self, intelligence_type: str, source: str, symbol: str,
                                 timeframe: str, signal_strength: float, confidence: str,
                                 direction: str, description: str, 
                                 supporting_data: Dict[str, Any] = None) -> IntelligenceSignal:
        """Helper method to create IntelligenceSignal objects."""
        try:
            # Convert string enums
            intel_type_enum = IntelligenceType(intelligence_type.lower())
            source_enum = IntelligenceSource(source.lower())
            confidence_enum = ConfidenceLevel(confidence.lower())
            
            return IntelligenceSignal(
                signal_id=f"{intelligence_type}_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=intel_type_enum,
                source=source_enum,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=signal_strength,
                confidence=confidence_enum,
                direction=direction,
                description=description,
                supporting_data=supporting_data or {},
                timestamp=datetime.now()
            )
            
        except ValueError as e:
            logger.error(f"Invalid enum value: {e}")
            # Return with default values
            return IntelligenceSignal(
                signal_id=f"signal_{symbol}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                intelligence_type=IntelligenceType.TECHNICAL_SIGNALS,
                source=IntelligenceSource.PRICE_DATA,
                symbol=symbol,
                timeframe=timeframe,
                signal_strength=signal_strength,
                confidence=ConfidenceLevel.MODERATE,
                direction=direction,
                description=description,
                supporting_data=supporting_data or {},
                timestamp=datetime.now()
            )