"""Market microstructure analysis for the Noryon V2 trading system"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import logging
from collections import deque
import math

logger = logging.getLogger(__name__)


class LiquidityRegime(Enum):
    """Market liquidity regimes"""
    HIGH_LIQUIDITY = "high_liquidity"
    NORMAL_LIQUIDITY = "normal_liquidity"
    LOW_LIQUIDITY = "low_liquidity"
    STRESSED_LIQUIDITY = "stressed_liquidity"


class MarketRegime(Enum):
    """Market regimes"""
    TRENDING = "trending"
    MEAN_REVERTING = "mean_reverting"
    VOLATILE = "volatile"
    QUIET = "quiet"
    STRESSED = "stressed"


class OrderFlowDirection(Enum):
    """Order flow direction"""
    BUYING_PRESSURE = "buying_pressure"
    SELLING_PRESSURE = "selling_pressure"
    BALANCED = "balanced"
    UNCERTAIN = "uncertain"


@dataclass
class OrderBookLevel:
    """Order book level data"""
    price: float
    size: float
    orders: int = 1


@dataclass
class OrderBookSnapshot:
    """Order book snapshot"""
    timestamp: datetime
    symbol: str
    bids: List[OrderBookLevel]
    asks: List[OrderBookLevel]
    mid_price: float
    spread: float
    total_bid_size: float
    total_ask_size: float
    imbalance: float  # (bid_size - ask_size) / (bid_size + ask_size)


@dataclass
class TradeData:
    """Individual trade data"""
    timestamp: datetime
    price: float
    size: float
    side: str  # 'buy' or 'sell' (aggressor side)
    trade_id: str


@dataclass
class LiquidityMetrics:
    """Liquidity metrics"""
    symbol: str
    timestamp: datetime
    bid_ask_spread: float
    effective_spread: float
    quoted_spread: float
    depth_at_best: float
    depth_5_levels: float
    depth_10_levels: float
    market_impact_1k: float  # Market impact for $1k trade
    market_impact_10k: float  # Market impact for $10k trade
    price_impact: float
    resilience_time: float  # Time to recover from impact
    turnover_rate: float
    amihud_illiquidity: float
    kyle_lambda: float
    liquidity_score: float  # 0-1 composite score


@dataclass
class MicrostructureSignals:
    """Microstructure-based trading signals"""
    symbol: str
    timestamp: datetime
    order_flow_imbalance: float
    price_pressure: float
    liquidity_shock: float
    informed_trading_probability: float
    market_making_opportunity: float
    momentum_signal: float
    mean_reversion_signal: float
    volatility_signal: float
    regime_signal: MarketRegime
    confidence: float


@dataclass
class VWAPAnalysis:
    """VWAP analysis results"""
    symbol: str
    timestamp: datetime
    current_vwap: float
    session_vwap: float
    vwap_deviation: float  # Current price vs VWAP
    vwap_trend: str  # 'above', 'below', 'converging'
    volume_profile: Dict[float, float]  # Price -> Volume
    poc_price: float  # Point of Control (highest volume price)
    value_area_high: float
    value_area_low: float
    volume_weighted_std: float


class MarketMicrostructureAnalyzer:
    """Market microstructure analyzer"""
    
    def __init__(self, lookback_periods: int = 100):
        self.lookback_periods = lookback_periods
        self.order_book_history = deque(maxlen=lookback_periods)
        self.trade_history = deque(maxlen=lookback_periods * 10)
        self.price_history = deque(maxlen=lookback_periods)
        self.volume_history = deque(maxlen=lookback_periods)
        self.vwap_data = {}
        
    def update_order_book(self, order_book: OrderBookSnapshot):
        """Update order book data"""
        try:
            self.order_book_history.append(order_book)
            self.price_history.append(order_book.mid_price)
            
            # Update VWAP data
            symbol = order_book.symbol
            if symbol not in self.vwap_data:
                self.vwap_data[symbol] = {
                    'prices': [],
                    'volumes': [],
                    'timestamps': [],
                    'cumulative_volume': 0,
                    'cumulative_pv': 0
                }
            
        except Exception as e:
            logger.error(f"Error updating order book: {e}")
    
    def update_trade(self, trade: TradeData):
        """Update trade data"""
        try:
            self.trade_history.append(trade)
            self.volume_history.append(trade.size)
            
            # Update VWAP calculations
            symbol = trade.price  # Assuming symbol is available
            if symbol in self.vwap_data:
                vwap_data = self.vwap_data[symbol]
                vwap_data['prices'].append(trade.price)
                vwap_data['volumes'].append(trade.size)
                vwap_data['timestamps'].append(trade.timestamp)
                vwap_data['cumulative_volume'] += trade.size
                vwap_data['cumulative_pv'] += trade.price * trade.size
            
        except Exception as e:
            logger.error(f"Error updating trade: {e}")
    
    def calculate_liquidity_metrics(self, symbol: str) -> Optional[LiquidityMetrics]:
        """Calculate comprehensive liquidity metrics"""
        try:
            if not self.order_book_history:
                return None
            
            latest_book = self.order_book_history[-1]
            if latest_book.symbol != symbol:
                return None
            
            # Basic spread metrics
            bid_ask_spread = latest_book.spread
            quoted_spread = bid_ask_spread / latest_book.mid_price
            
            # Effective spread (using recent trades)
            effective_spread = self._calculate_effective_spread(symbol)
            
            # Depth metrics
            depth_at_best = latest_book.bids[0].size + latest_book.asks[0].size if latest_book.bids and latest_book.asks else 0
            depth_5_levels = sum(level.size for level in latest_book.bids[:5]) + sum(level.size for level in latest_book.asks[:5])
            depth_10_levels = sum(level.size for level in latest_book.bids[:10]) + sum(level.size for level in latest_book.asks[:10])
            
            # Market impact estimates
            market_impact_1k = self._estimate_market_impact(latest_book, 1000)
            market_impact_10k = self._estimate_market_impact(latest_book, 10000)
            
            # Price impact and resilience
            price_impact = self._calculate_price_impact(symbol)
            resilience_time = self._calculate_resilience_time(symbol)
            
            # Turnover and illiquidity measures
            turnover_rate = self._calculate_turnover_rate(symbol)
            amihud_illiquidity = self._calculate_amihud_illiquidity(symbol)
            kyle_lambda = self._calculate_kyle_lambda(symbol)
            
            # Composite liquidity score
            liquidity_score = self._calculate_liquidity_score(
                quoted_spread, depth_at_best, market_impact_1k, turnover_rate
            )
            
            return LiquidityMetrics(
                symbol=symbol,
                timestamp=datetime.now(),
                bid_ask_spread=bid_ask_spread,
                effective_spread=effective_spread,
                quoted_spread=quoted_spread,
                depth_at_best=depth_at_best,
                depth_5_levels=depth_5_levels,
                depth_10_levels=depth_10_levels,
                market_impact_1k=market_impact_1k,
                market_impact_10k=market_impact_10k,
                price_impact=price_impact,
                resilience_time=resilience_time,
                turnover_rate=turnover_rate,
                amihud_illiquidity=amihud_illiquidity,
                kyle_lambda=kyle_lambda,
                liquidity_score=liquidity_score
            )
            
        except Exception as e:
            logger.error(f"Error calculating liquidity metrics: {e}")
            return None
    
    def generate_microstructure_signals(self, symbol: str) -> Optional[MicrostructureSignals]:
        """Generate trading signals based on microstructure analysis"""
        try:
            if len(self.order_book_history) < 10:
                return None
            
            # Order flow imbalance
            order_flow_imbalance = self._calculate_order_flow_imbalance(symbol)
            
            # Price pressure from order book
            price_pressure = self._calculate_price_pressure()
            
            # Liquidity shock detection
            liquidity_shock = self._detect_liquidity_shock()
            
            # Informed trading probability
            informed_trading_prob = self._calculate_informed_trading_probability(symbol)
            
            # Market making opportunity
            market_making_opp = self._calculate_market_making_opportunity()
            
            # Momentum and mean reversion signals
            momentum_signal = self._calculate_momentum_signal()
            mean_reversion_signal = self._calculate_mean_reversion_signal()
            
            # Volatility signal
            volatility_signal = self._calculate_volatility_signal()
            
            # Market regime detection
            regime_signal = self._detect_market_regime()
            
            # Overall confidence
            confidence = self._calculate_signal_confidence(
                order_flow_imbalance, price_pressure, liquidity_shock
            )
            
            return MicrostructureSignals(
                symbol=symbol,
                timestamp=datetime.now(),
                order_flow_imbalance=order_flow_imbalance,
                price_pressure=price_pressure,
                liquidity_shock=liquidity_shock,
                informed_trading_probability=informed_trading_prob,
                market_making_opportunity=market_making_opp,
                momentum_signal=momentum_signal,
                mean_reversion_signal=mean_reversion_signal,
                volatility_signal=volatility_signal,
                regime_signal=regime_signal,
                confidence=confidence
            )
            
        except Exception as e:
            logger.error(f"Error generating microstructure signals: {e}")
            return None
    
    def analyze_vwap(self, symbol: str) -> Optional[VWAPAnalysis]:
        """Analyze VWAP and volume profile"""
        try:
            if symbol not in self.vwap_data or not self.vwap_data[symbol]['prices']:
                return None
            
            vwap_data = self.vwap_data[symbol]
            prices = np.array(vwap_data['prices'])
            volumes = np.array(vwap_data['volumes'])
            
            # Calculate current VWAP
            if vwap_data['cumulative_volume'] > 0:
                current_vwap = vwap_data['cumulative_pv'] / vwap_data['cumulative_volume']
            else:
                current_vwap = prices[-1] if len(prices) > 0 else 0
            
            # Session VWAP (last 6.5 hours of trading)
            session_start = datetime.now() - timedelta(hours=6.5)
            session_mask = np.array(vwap_data['timestamps']) >= session_start
            
            if np.any(session_mask):
                session_prices = prices[session_mask]
                session_volumes = volumes[session_mask]
                session_vwap = np.average(session_prices, weights=session_volumes) if len(session_prices) > 0 else current_vwap
            else:
                session_vwap = current_vwap
            
            # Current price vs VWAP
            current_price = prices[-1] if len(prices) > 0 else current_vwap
            vwap_deviation = (current_price - current_vwap) / current_vwap if current_vwap > 0 else 0
            
            # VWAP trend
            if vwap_deviation > 0.001:
                vwap_trend = 'above'
            elif vwap_deviation < -0.001:
                vwap_trend = 'below'
            else:
                vwap_trend = 'converging'
            
            # Volume profile
            volume_profile = self._calculate_volume_profile(prices, volumes)
            
            # Point of Control (POC)
            poc_price = max(volume_profile.items(), key=lambda x: x[1])[0] if volume_profile else current_price
            
            # Value Area (70% of volume)
            value_area_high, value_area_low = self._calculate_value_area(volume_profile)
            
            # Volume weighted standard deviation
            volume_weighted_std = np.sqrt(np.average((prices - current_vwap)**2, weights=volumes)) if len(prices) > 0 else 0
            
            return VWAPAnalysis(
                symbol=symbol,
                timestamp=datetime.now(),
                current_vwap=current_vwap,
                session_vwap=session_vwap,
                vwap_deviation=vwap_deviation,
                vwap_trend=vwap_trend,
                volume_profile=volume_profile,
                poc_price=poc_price,
                value_area_high=value_area_high,
                value_area_low=value_area_low,
                volume_weighted_std=volume_weighted_std
            )
            
        except Exception as e:
            logger.error(f"Error analyzing VWAP: {e}")
            return None
    
    def detect_liquidity_regime(self) -> LiquidityRegime:
        """Detect current liquidity regime"""
        try:
            if len(self.order_book_history) < 10:
                return LiquidityRegime.NORMAL_LIQUIDITY
            
            recent_books = list(self.order_book_history)[-10:]
            
            # Calculate average spread and depth
            avg_spread = np.mean([book.spread / book.mid_price for book in recent_books])
            avg_depth = np.mean([book.total_bid_size + book.total_ask_size for book in recent_books])
            
            # Calculate spread volatility
            spread_volatility = np.std([book.spread / book.mid_price for book in recent_books])
            
            # Classify regime
            if avg_spread < 0.0005 and avg_depth > 10000:  # Tight spreads, deep book
                return LiquidityRegime.HIGH_LIQUIDITY
            elif avg_spread > 0.002 or avg_depth < 1000:  # Wide spreads or thin book
                return LiquidityRegime.LOW_LIQUIDITY
            elif spread_volatility > 0.001:  # Volatile spreads
                return LiquidityRegime.STRESSED_LIQUIDITY
            else:
                return LiquidityRegime.NORMAL_LIQUIDITY
                
        except Exception as e:
            logger.error(f"Error detecting liquidity regime: {e}")
            return LiquidityRegime.NORMAL_LIQUIDITY
    
    def estimate_optimal_order_size(self, symbol: str, target_impact: float = 0.001) -> float:
        """Estimate optimal order size for target market impact"""
        try:
            liquidity_metrics = self.calculate_liquidity_metrics(symbol)
            if not liquidity_metrics:
                return 1000.0  # Default size
            
            # Use Kyle's lambda to estimate impact
            if liquidity_metrics.kyle_lambda > 0:
                optimal_size = target_impact / liquidity_metrics.kyle_lambda
                return max(100, min(10000, optimal_size))  # Bound between 100 and 10k
            
            # Fallback to depth-based estimate
            return min(liquidity_metrics.depth_at_best * 0.1, 5000)
            
        except Exception as e:
            logger.error(f"Error estimating optimal order size: {e}")
            return 1000.0
    
    def get_execution_timing_signal(self) -> Dict[str, float]:
        """Get timing signals for order execution"""
        try:
            if len(self.order_book_history) < 5:
                return {'urgency': 0.5, 'patience': 0.5, 'aggression': 0.5}
            
            recent_books = list(self.order_book_history)[-5:]
            
            # Calculate spread trend
            spreads = [book.spread / book.mid_price for book in recent_books]
            spread_trend = (spreads[-1] - spreads[0]) / spreads[0] if spreads[0] > 0 else 0
            
            # Calculate imbalance trend
            imbalances = [book.imbalance for book in recent_books]
            imbalance_trend = np.mean(imbalances)
            
            # Calculate volatility
            prices = [book.mid_price for book in recent_books]
            price_volatility = np.std(prices) / np.mean(prices) if len(prices) > 1 else 0
            
            # Generate signals
            urgency = min(1.0, max(0.0, 0.5 + spread_trend * 10 + price_volatility * 5))
            patience = 1.0 - urgency
            aggression = min(1.0, max(0.0, 0.5 + abs(imbalance_trend) * 2))
            
            return {
                'urgency': urgency,
                'patience': patience,
                'aggression': aggression
            }
            
        except Exception as e:
            logger.error(f"Error getting execution timing signal: {e}")
            return {'urgency': 0.5, 'patience': 0.5, 'aggression': 0.5}
    
    # Helper methods
    
    def _calculate_effective_spread(self, symbol: str) -> float:
        """Calculate effective spread from recent trades"""
        try:
            recent_trades = [trade for trade in self.trade_history if len(self.trade_history) > 0][-10:]
            if not recent_trades:
                return 0.0
            
            # Calculate effective spread as 2 * |trade_price - mid_price|
            effective_spreads = []
            for trade in recent_trades:
                # Find corresponding order book
                closest_book = min(self.order_book_history, 
                                 key=lambda book: abs((book.timestamp - trade.timestamp).total_seconds()))
                effective_spread = 2 * abs(trade.price - closest_book.mid_price)
                effective_spreads.append(effective_spread)
            
            return np.mean(effective_spreads) if effective_spreads else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating effective spread: {e}")
            return 0.0
    
    def _estimate_market_impact(self, order_book: OrderBookSnapshot, trade_size: float) -> float:
        """Estimate market impact for a given trade size"""
        try:
            # Simple impact model based on order book depth
            cumulative_size = 0
            weighted_price = 0
            
            # For buy order, walk through asks
            for level in order_book.asks:
                if cumulative_size >= trade_size:
                    break
                
                level_size = min(level.size, trade_size - cumulative_size)
                weighted_price += level.price * level_size
                cumulative_size += level_size
            
            if cumulative_size > 0:
                avg_execution_price = weighted_price / cumulative_size
                impact = (avg_execution_price - order_book.mid_price) / order_book.mid_price
                return abs(impact)
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error estimating market impact: {e}")
            return 0.0
    
    def _calculate_price_impact(self, symbol: str) -> float:
        """Calculate recent price impact"""
        try:
            if len(self.trade_history) < 5:
                return 0.0
            
            recent_trades = list(self.trade_history)[-5:]
            price_changes = []
            
            for i in range(1, len(recent_trades)):
                price_change = abs(recent_trades[i].price - recent_trades[i-1].price) / recent_trades[i-1].price
                price_changes.append(price_change)
            
            return np.mean(price_changes) if price_changes else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating price impact: {e}")
            return 0.0
    
    def _calculate_resilience_time(self, symbol: str) -> float:
        """Calculate market resilience time"""
        # Simplified resilience calculation
        return 30.0  # 30 seconds default
    
    def _calculate_turnover_rate(self, symbol: str) -> float:
        """Calculate turnover rate"""
        try:
            if not self.trade_history:
                return 0.0
            
            recent_trades = list(self.trade_history)[-20:]
            total_volume = sum(trade.size for trade in recent_trades)
            time_span = (recent_trades[-1].timestamp - recent_trades[0].timestamp).total_seconds() / 3600  # hours
            
            return total_volume / time_span if time_span > 0 else 0.0
            
        except Exception as e:
            logger.error(f"Error calculating turnover rate: {e}")
            return 0.0
    
    def _calculate_amihud_illiquidity(self, symbol: str) -> float:
        """Calculate Amihud illiquidity measure"""
        try:
            if len(self.trade_history) < 2 or len(self.price_history) < 2:
                return 0.0
            
            # Calculate daily return and volume
            price_changes = np.diff(list(self.price_history))
            returns = price_changes / np.array(list(self.price_history)[:-1])
            volumes = list(self.volume_history)[-len(returns):]
            
            if len(volumes) == 0:
                return 0.0
            
            # Amihud = |return| / volume
            amihud_values = np.abs(returns) / np.array(volumes)
            return np.mean(amihud_values[np.isfinite(amihud_values)])
            
        except Exception as e:
            logger.error(f"Error calculating Amihud illiquidity: {e}")
            return 0.0
    
    def _calculate_kyle_lambda(self, symbol: str) -> float:
        """Calculate Kyle's lambda (price impact coefficient)"""
        try:
            if len(self.trade_history) < 10:
                return 0.001  # Default value
            
            # Simplified Kyle's lambda calculation
            recent_trades = list(self.trade_history)[-10:]
            price_changes = []
            order_flows = []
            
            for i in range(1, len(recent_trades)):
                price_change = recent_trades[i].price - recent_trades[i-1].price
                order_flow = recent_trades[i].size if recent_trades[i].side == 'buy' else -recent_trades[i].size
                
                price_changes.append(price_change)
                order_flows.append(order_flow)
            
            if len(price_changes) > 0 and np.std(order_flows) > 0:
                # Linear regression coefficient
                correlation = np.corrcoef(price_changes, order_flows)[0, 1]
                lambda_kyle = correlation * np.std(price_changes) / np.std(order_flows)
                return abs(lambda_kyle)
            
            return 0.001
            
        except Exception as e:
            logger.error(f"Error calculating Kyle's lambda: {e}")
            return 0.001
    
    def _calculate_liquidity_score(self, quoted_spread: float, depth: float, 
                                 market_impact: float, turnover: float) -> float:
        """Calculate composite liquidity score (0-1)"""
        try:
            # Normalize components (lower is better for spread and impact, higher is better for depth and turnover)
            spread_score = max(0, 1 - quoted_spread * 1000)  # Penalize wide spreads
            depth_score = min(1, depth / 10000)  # Reward depth up to 10k
            impact_score = max(0, 1 - market_impact * 1000)  # Penalize high impact
            turnover_score = min(1, turnover / 1000000)  # Reward turnover up to 1M
            
            # Weighted average
            liquidity_score = (spread_score * 0.3 + depth_score * 0.3 + 
                             impact_score * 0.25 + turnover_score * 0.15)
            
            return max(0, min(1, liquidity_score))
            
        except Exception as e:
            logger.error(f"Error calculating liquidity score: {e}")
            return 0.5
    
    def _calculate_order_flow_imbalance(self, symbol: str) -> float:
        """Calculate order flow imbalance"""
        try:
            if len(self.trade_history) < 10:
                return 0.0
            
            recent_trades = list(self.trade_history)[-10:]
            buy_volume = sum(trade.size for trade in recent_trades if trade.side == 'buy')
            sell_volume = sum(trade.size for trade in recent_trades if trade.side == 'sell')
            
            total_volume = buy_volume + sell_volume
            if total_volume > 0:
                return (buy_volume - sell_volume) / total_volume
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating order flow imbalance: {e}")
            return 0.0
    
    def _calculate_price_pressure(self) -> float:
        """Calculate price pressure from order book"""
        try:
            if not self.order_book_history:
                return 0.0
            
            latest_book = self.order_book_history[-1]
            return latest_book.imbalance
            
        except Exception as e:
            logger.error(f"Error calculating price pressure: {e}")
            return 0.0
    
    def _detect_liquidity_shock(self) -> float:
        """Detect liquidity shock"""
        try:
            if len(self.order_book_history) < 5:
                return 0.0
            
            recent_books = list(self.order_book_history)[-5:]
            depth_changes = []
            
            for i in range(1, len(recent_books)):
                depth_change = ((recent_books[i].total_bid_size + recent_books[i].total_ask_size) - 
                              (recent_books[i-1].total_bid_size + recent_books[i-1].total_ask_size))
                depth_changes.append(depth_change)
            
            if depth_changes:
                # Negative change indicates liquidity shock
                avg_change = np.mean(depth_changes)
                return max(0, -avg_change / 1000)  # Normalize
            
            return 0.0
            
        except Exception as e:
            logger.error(f"Error detecting liquidity shock: {e}")
            return 0.0
    
    def _calculate_informed_trading_probability(self, symbol: str) -> float:
        """Calculate probability of informed trading"""
        try:
            # Simplified PIN (Probability of Informed Trading) calculation
            if len(self.trade_history) < 20:
                return 0.5
            
            recent_trades = list(self.trade_history)[-20:]
            
            # Calculate buy/sell imbalance
            buy_trades = [t for t in recent_trades if t.side == 'buy']
            sell_trades = [t for t in recent_trades if t.side == 'sell']
            
            if len(buy_trades) + len(sell_trades) == 0:
                return 0.5
            
            imbalance = abs(len(buy_trades) - len(sell_trades)) / len(recent_trades)
            
            # Higher imbalance suggests more informed trading
            return min(1.0, imbalance * 2)
            
        except Exception as e:
            logger.error(f"Error calculating informed trading probability: {e}")
            return 0.5
    
    def _calculate_market_making_opportunity(self) -> float:
        """Calculate market making opportunity score"""
        try:
            if not self.order_book_history:
                return 0.0
            
            latest_book = self.order_book_history[-1]
            
            # Good market making when spreads are wide and book is balanced
            spread_score = min(1.0, latest_book.spread / latest_book.mid_price * 1000)
            balance_score = 1.0 - abs(latest_book.imbalance)
            
            return (spread_score + balance_score) / 2
            
        except Exception as e:
            logger.error(f"Error calculating market making opportunity: {e}")
            return 0.0
    
    def _calculate_momentum_signal(self) -> float:
        """Calculate momentum signal"""
        try:
            if len(self.price_history) < 10:
                return 0.0
            
            prices = list(self.price_history)[-10:]
            
            # Simple momentum: recent price change
            momentum = (prices[-1] - prices[0]) / prices[0] if prices[0] > 0 else 0
            
            # Normalize to [-1, 1]
            return max(-1, min(1, momentum * 100))
            
        except Exception as e:
            logger.error(f"Error calculating momentum signal: {e}")
            return 0.0
    
    def _calculate_mean_reversion_signal(self) -> float:
        """Calculate mean reversion signal"""
        try:
            if len(self.price_history) < 20:
                return 0.0
            
            prices = np.array(list(self.price_history)[-20:])
            
            # Calculate deviation from moving average
            ma = np.mean(prices)
            current_price = prices[-1]
            
            deviation = (current_price - ma) / ma if ma > 0 else 0
            
            # Mean reversion signal is opposite of deviation
            return max(-1, min(1, -deviation * 10))
            
        except Exception as e:
            logger.error(f"Error calculating mean reversion signal: {e}")
            return 0.0
    
    def _calculate_volatility_signal(self) -> float:
        """Calculate volatility signal"""
        try:
            if len(self.price_history) < 10:
                return 0.0
            
            prices = np.array(list(self.price_history)[-10:])
            returns = np.diff(prices) / prices[:-1]
            
            volatility = np.std(returns) if len(returns) > 1 else 0
            
            # Normalize volatility
            return min(1.0, volatility * 100)
            
        except Exception as e:
            logger.error(f"Error calculating volatility signal: {e}")
            return 0.0
    
    def _detect_market_regime(self) -> MarketRegime:
        """Detect current market regime"""
        try:
            if len(self.price_history) < 20:
                return MarketRegime.QUIET
            
            prices = np.array(list(self.price_history)[-20:])
            returns = np.diff(prices) / prices[:-1]
            
            volatility = np.std(returns)
            trend = (prices[-1] - prices[0]) / prices[0]
            
            if volatility > 0.02:  # High volatility
                if abs(trend) > 0.01:
                    return MarketRegime.TRENDING
                else:
                    return MarketRegime.VOLATILE
            elif abs(trend) > 0.005:
                return MarketRegime.TRENDING
            elif volatility < 0.005:
                return MarketRegime.QUIET
            else:
                return MarketRegime.MEAN_REVERTING
                
        except Exception as e:
            logger.error(f"Error detecting market regime: {e}")
            return MarketRegime.QUIET
    
    def _calculate_signal_confidence(self, order_flow_imbalance: float, 
                                   price_pressure: float, liquidity_shock: float) -> float:
        """Calculate overall signal confidence"""
        try:
            # Higher confidence when signals are strong and consistent
            signal_strength = (abs(order_flow_imbalance) + abs(price_pressure)) / 2
            liquidity_penalty = liquidity_shock  # Lower confidence during liquidity shocks
            
            confidence = signal_strength * (1 - liquidity_penalty)
            return max(0, min(1, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating signal confidence: {e}")
            return 0.5
    
    def _calculate_volume_profile(self, prices: np.ndarray, volumes: np.ndarray) -> Dict[float, float]:
        """Calculate volume profile"""
        try:
            if len(prices) == 0 or len(volumes) == 0:
                return {}
            
            # Create price bins
            price_min, price_max = np.min(prices), np.max(prices)
            num_bins = min(50, len(prices) // 2)  # Adaptive number of bins
            
            if price_max == price_min:
                return {price_min: np.sum(volumes)}
            
            bins = np.linspace(price_min, price_max, num_bins + 1)
            
            # Aggregate volume by price bins
            volume_profile = {}
            for i in range(len(bins) - 1):
                bin_center = (bins[i] + bins[i + 1]) / 2
                mask = (prices >= bins[i]) & (prices < bins[i + 1])
                volume_profile[bin_center] = np.sum(volumes[mask])
            
            return volume_profile
            
        except Exception as e:
            logger.error(f"Error calculating volume profile: {e}")
            return {}
    
    def _calculate_value_area(self, volume_profile: Dict[float, float]) -> Tuple[float, float]:
        """Calculate value area (70% of volume)"""
        try:
            if not volume_profile:
                return 0.0, 0.0
            
            # Sort by volume
            sorted_profile = sorted(volume_profile.items(), key=lambda x: x[1], reverse=True)
            
            total_volume = sum(volume_profile.values())
            target_volume = total_volume * 0.7
            
            cumulative_volume = 0
            value_area_prices = []
            
            for price, volume in sorted_profile:
                cumulative_volume += volume
                value_area_prices.append(price)
                
                if cumulative_volume >= target_volume:
                    break
            
            if value_area_prices:
                return max(value_area_prices), min(value_area_prices)
            
            return 0.0, 0.0
            
        except Exception as e:
            logger.error(f"Error calculating value area: {e}")
            return 0.0, 0.0