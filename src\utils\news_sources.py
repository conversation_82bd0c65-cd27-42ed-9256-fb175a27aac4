#!/usr/bin/env python3
"""
News Sources Configuration
Defines various news sources and their RSS feeds for market analysis.
"""

from typing import Dict, List
from dataclasses import dataclass

@dataclass
class NewsSource:
    """Configuration for a news source."""
    name: str
    url: str
    category: str
    priority: int = 1
    enabled: bool = True

# Financial News Sources
FINANCIAL_SOURCES = {
    'reuters_business': NewsSource(
        name='Reuters Business',
        url='https://feeds.reuters.com/reuters/businessNews',
        category='business',
        priority=1
    ),
    'bloomberg': NewsSource(
        name='Bloomberg',
        url='https://feeds.bloomberg.com/markets/news.rss',
        category='markets',
        priority=1
    ),
    'cnbc': NewsSource(
        name='CNBC',
        url='https://www.cnbc.com/id/100003114/device/rss/rss.html',
        category='markets',
        priority=2
    ),
    'marketwatch': NewsSource(
        name='MarketWatch',
        url='https://feeds.marketwatch.com/marketwatch/topstories/',
        category='markets',
        priority=2
    ),
    'yahoo_finance': NewsSource(
        name='Yahoo Finance',
        url='https://feeds.finance.yahoo.com/rss/2.0/headline',
        category='finance',
        priority=3
    )
}

# Crypto News Sources
CRYPTO_SOURCES = {
    'coindesk': NewsSource(
        name='CoinDesk',
        url='https://feeds.coindesk.com/coindesk/rss',
        category='crypto',
        priority=1
    ),
    'cointelegraph': NewsSource(
        name='Cointelegraph',
        url='https://cointelegraph.com/rss',
        category='crypto',
        priority=2
    ),
    'decrypt': NewsSource(
        name='Decrypt',
        url='https://decrypt.co/feed',
        category='crypto',
        priority=3
    )
}

# Economic News Sources
ECONOMIC_SOURCES = {
    'fed_news': NewsSource(
        name='Federal Reserve News',
        url='https://www.federalreserve.gov/feeds/press_all.xml',
        category='economic',
        priority=1
    ),
    'treasury': NewsSource(
        name='US Treasury',
        url='https://home.treasury.gov/rss/press-releases',
        category='economic',
        priority=2
    )
}

# All sources combined
ALL_SOURCES = {
    **FINANCIAL_SOURCES,
    **CRYPTO_SOURCES,
    **ECONOMIC_SOURCES
}

def get_sources_by_category(category: str) -> Dict[str, NewsSource]:
    """Get news sources filtered by category."""
    return {k: v for k, v in ALL_SOURCES.items() if v.category == category}

def get_enabled_sources() -> Dict[str, NewsSource]:
    """Get only enabled news sources."""
    return {k: v for k, v in ALL_SOURCES.items() if v.enabled}

def get_priority_sources(max_priority: int = 2) -> Dict[str, NewsSource]:
    """Get news sources with priority <= max_priority."""
    return {k: v for k, v in ALL_SOURCES.items() if v.priority <= max_priority}

def get_source_urls() -> List[str]:
    """Get list of all enabled source URLs."""
    return [source.url for source in get_enabled_sources().values()]

def get_high_priority_urls() -> List[str]:
    """Get URLs for high priority sources only."""
    return [source.url for source in get_priority_sources(1).values()]

class NewsSourceManager:
    """Manages news sources and provides methods to fetch and filter them."""
    
    def __init__(self):
        self.sources = ALL_SOURCES.copy()
    
    def get_all_sources(self) -> Dict[str, NewsSource]:
        """Get all configured news sources."""
        return self.sources
    
    def get_enabled_sources(self) -> Dict[str, NewsSource]:
        """Get only enabled news sources."""
        return {k: v for k, v in self.sources.items() if v.enabled}
    
    def get_sources_by_category(self, category: str) -> Dict[str, NewsSource]:
        """Get news sources filtered by category."""
        return {k: v for k, v in self.sources.items() if v.category == category}
    
    def get_priority_sources(self, max_priority: int = 2) -> Dict[str, NewsSource]:
        """Get news sources with priority <= max_priority."""
        return {k: v for k, v in self.sources.items() if v.priority <= max_priority}
    
    def get_source_urls(self, category: str = None, max_priority: int = None) -> List[str]:
        """Get list of source URLs with optional filtering."""
        sources = self.get_enabled_sources()
        
        if category:
            sources = {k: v for k, v in sources.items() if v.category == category}
        
        if max_priority is not None:
            sources = {k: v for k, v in sources.items() if v.priority <= max_priority}
        
        return [source.url for source in sources.values()]
    
    def enable_source(self, source_key: str) -> bool:
        """Enable a news source."""
        if source_key in self.sources:
            self.sources[source_key].enabled = True
            return True
        return False
    
    def disable_source(self, source_key: str) -> bool:
        """Disable a news source."""
        if source_key in self.sources:
            self.sources[source_key].enabled = False
            return True
        return False
    
    def add_custom_source(self, key: str, source: NewsSource) -> None:
        """Add a custom news source."""
        self.sources[key] = source
    
    def remove_source(self, source_key: str) -> bool:
        """Remove a news source."""
        if source_key in self.sources:
            del self.sources[source_key]
            return True
        return False