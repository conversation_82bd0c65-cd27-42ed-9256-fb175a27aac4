"""Order management system for the Noryon V2 trading system"""

import uuid
import asyncio
from typing import Dict, List, Optional, Callable, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import logging
from abc import ABC, abstractmethod

from src.core.models import TradeOrder, ExecutionReport

logger = logging.getLogger(__name__)


class OrderStatus(Enum):
    """Order status enumeration"""
    PENDING = "pending"
    SUBMITTED = "submitted"
    PARTIALLY_FILLED = "partially_filled"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"
    EXPIRED = "expired"


class OrderType(Enum):
    """Order type enumeration"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    STOP_LIMIT = "stop_limit"
    TRAILING_STOP = "trailing_stop"
    ICEBERG = "iceberg"
    TWAP = "twap"
    VWAP = "vwap"


class OrderSide(Enum):
    """Order side enumeration"""
    BUY = "buy"
    SELL = "sell"


class TimeInForce(Enum):
    """Time in force enumeration"""
    GTC = "gtc"  # Good Till Cancelled
    IOC = "ioc"  # Immediate or Cancel
    FOK = "fok"  # Fill or Kill
    DAY = "day"  # Day order
    GTD = "gtd"  # Good Till Date


class RiskCheckResult(Enum):
    """Risk check result enumeration"""
    APPROVED = "approved"
    REJECTED = "rejected"
    WARNING = "warning"


@dataclass
class OrderValidationResult:
    """Order validation result"""
    is_valid: bool
    risk_result: RiskCheckResult
    messages: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    suggested_modifications: Dict[str, Any] = field(default_factory=dict)


@dataclass
class PositionInfo:
    """Position information"""
    symbol: str
    quantity: float
    average_price: float
    market_value: float
    unrealized_pnl: float
    realized_pnl: float
    last_updated: datetime


@dataclass
class RiskLimits:
    """Risk limits configuration"""
    max_position_size: float = 1000000.0  # Maximum position size in USD
    max_order_size: float = 100000.0      # Maximum single order size in USD
    max_daily_loss: float = 50000.0       # Maximum daily loss in USD
    max_portfolio_exposure: float = 0.95   # Maximum portfolio exposure (0-1)
    max_sector_exposure: float = 0.3       # Maximum sector exposure (0-1)
    max_single_stock_exposure: float = 0.1 # Maximum single stock exposure (0-1)
    min_cash_balance: float = 10000.0      # Minimum cash balance in USD


class OrderExecutor(ABC):
    """Abstract base class for order executors"""
    
    @abstractmethod
    async def execute_order(self, order: TradeOrder) -> ExecutionReport:
        """Execute a trade order"""
        pass
    
    @abstractmethod
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        pass
    
    @abstractmethod
    async def get_order_status(self, order_id: str) -> Optional[TradeOrder]:
        """Get order status"""
        pass


class SimulatedExecutor(OrderExecutor):
    """Simulated order executor for testing"""
    
    def __init__(self, fill_probability: float = 0.95, latency_ms: int = 100):
        self.fill_probability = fill_probability
        self.latency_ms = latency_ms
        self.orders = {}
        
    async def execute_order(self, order: TradeOrder) -> ExecutionReport:
        """Simulate order execution"""
        try:
            # Simulate network latency
            await asyncio.sleep(self.latency_ms / 1000.0)
            
            # Store order
            self.orders[order.order_id] = order
            
            # Simulate execution
            import random
            if random.random() < self.fill_probability:
                # Order filled
                execution_price = order.price or 100.0  # Use order price or default
                slippage = random.uniform(-0.001, 0.001)  # Random slippage
                actual_price = execution_price * (1 + slippage)
                
                # Update order status
                order.status = OrderStatus.FILLED.value
                order.filled_quantity = order.quantity
                order.average_fill_price = actual_price
                order.execution_timestamp = datetime.now()
                
                # Create execution report
                report = ExecutionReport(
                    report_id=str(uuid.uuid4()),
                    order_id=order.order_id,
                    symbol=order.symbol,
                    side=order.side,
                    executed_quantity=order.quantity,
                    execution_price=actual_price,
                    execution_time=datetime.now(),
                    commission=order.quantity * actual_price * 0.001,  # 0.1% commission
                    fees=5.0,  # Fixed fee
                    execution_venue="SIMULATED",
                    liquidity_flag="taker",
                    slippage=abs(slippage),
                    market_impact=abs(slippage) * 0.5,
                    execution_quality=0.95,
                    notes="Simulated execution"
                )
                
                return report
            else:
                # Order rejected
                order.status = OrderStatus.REJECTED.value
                raise Exception("Order rejected by simulated executor")
                
        except Exception as e:
            logger.error(f"Error executing order {order.order_id}: {e}")
            order.status = OrderStatus.REJECTED.value
            raise
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        if order_id in self.orders:
            order = self.orders[order_id]
            if order.status in [OrderStatus.PENDING.value, OrderStatus.SUBMITTED.value]:
                order.status = OrderStatus.CANCELLED.value
                return True
        return False
    
    async def get_order_status(self, order_id: str) -> Optional[TradeOrder]:
        """Get order status"""
        return self.orders.get(order_id)


class RiskManager:
    """Risk management component"""
    
    def __init__(self, risk_limits: RiskLimits):
        self.risk_limits = risk_limits
        self.positions = {}
        self.daily_pnl = 0.0
        self.cash_balance = 100000.0  # Starting cash
        
    def validate_order(self, order: TradeOrder, current_price: float = None) -> OrderValidationResult:
        """Validate an order against risk limits"""
        messages = []
        warnings = []
        risk_result = RiskCheckResult.APPROVED
        
        try:
            # Calculate order value
            price = current_price or order.price or 100.0
            order_value = order.quantity * price
            
            # Check maximum order size
            if order_value > self.risk_limits.max_order_size:
                messages.append(f"Order size ${order_value:,.2f} exceeds maximum ${self.risk_limits.max_order_size:,.2f}")
                risk_result = RiskCheckResult.REJECTED
            
            # Check cash balance for buy orders
            if order.side == OrderSide.BUY.value and order_value > self.cash_balance:
                messages.append(f"Insufficient cash balance: ${self.cash_balance:,.2f} < ${order_value:,.2f}")
                risk_result = RiskCheckResult.REJECTED
            
            # Check position limits
            current_position = self.positions.get(order.symbol, PositionInfo(
                symbol=order.symbol,
                quantity=0.0,
                average_price=0.0,
                market_value=0.0,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                last_updated=datetime.now()
            ))
            
            # Calculate new position size
            if order.side == OrderSide.BUY.value:
                new_quantity = current_position.quantity + order.quantity
            else:
                new_quantity = current_position.quantity - order.quantity
            
            new_position_value = abs(new_quantity * price)
            
            if new_position_value > self.risk_limits.max_position_size:
                messages.append(f"Position size ${new_position_value:,.2f} would exceed maximum ${self.risk_limits.max_position_size:,.2f}")
                risk_result = RiskCheckResult.REJECTED
            
            # Check daily loss limit
            if self.daily_pnl < -self.risk_limits.max_daily_loss:
                messages.append(f"Daily loss limit exceeded: ${abs(self.daily_pnl):,.2f}")
                risk_result = RiskCheckResult.REJECTED
            
            # Add warnings for large orders
            if order_value > self.risk_limits.max_order_size * 0.8:
                warnings.append(f"Large order warning: ${order_value:,.2f}")
                if risk_result == RiskCheckResult.APPROVED:
                    risk_result = RiskCheckResult.WARNING
            
            return OrderValidationResult(
                is_valid=(risk_result != RiskCheckResult.REJECTED),
                risk_result=risk_result,
                messages=messages,
                warnings=warnings
            )
            
        except Exception as e:
            logger.error(f"Error validating order: {e}")
            return OrderValidationResult(
                is_valid=False,
                risk_result=RiskCheckResult.REJECTED,
                messages=[f"Validation error: {str(e)}"]
            )
    
    def update_position(self, symbol: str, quantity_change: float, price: float):
        """Update position after trade execution"""
        if symbol not in self.positions:
            self.positions[symbol] = PositionInfo(
                symbol=symbol,
                quantity=0.0,
                average_price=0.0,
                market_value=0.0,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                last_updated=datetime.now()
            )
        
        position = self.positions[symbol]
        old_quantity = position.quantity
        new_quantity = old_quantity + quantity_change
        
        # Update average price
        if new_quantity != 0:
            if old_quantity == 0:
                position.average_price = price
            else:
                total_cost = (old_quantity * position.average_price) + (quantity_change * price)
                position.average_price = total_cost / new_quantity
        
        position.quantity = new_quantity
        position.market_value = new_quantity * price
        position.last_updated = datetime.now()
        
        # Update cash balance
        self.cash_balance -= quantity_change * price
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary"""
        total_value = self.cash_balance
        total_positions = len([p for p in self.positions.values() if p.quantity != 0])
        
        for position in self.positions.values():
            total_value += position.market_value
        
        return {
            'total_value': total_value,
            'cash_balance': self.cash_balance,
            'total_positions': total_positions,
            'daily_pnl': self.daily_pnl,
            'positions': {symbol: pos.__dict__ for symbol, pos in self.positions.items() if pos.quantity != 0}
        }


class OrderManagementSystem:
    """Main order management system"""
    
    def __init__(self, executor: OrderExecutor, risk_limits: Optional[RiskLimits] = None):
        self.executor = executor
        self.risk_manager = RiskManager(risk_limits or RiskLimits())
        self.orders = {}
        self.execution_reports = {}
        self.order_callbacks = {}
        
    async def submit_order(self, order: TradeOrder, 
                          validation_callback: Optional[Callable] = None) -> Tuple[bool, OrderValidationResult]:
        """Submit an order for execution"""
        try:
            # Validate order
            validation_result = self.risk_manager.validate_order(order)
            
            if validation_callback:
                await validation_callback(order, validation_result)
            
            if not validation_result.is_valid:
                logger.warning(f"Order {order.order_id} rejected: {validation_result.messages}")
                order.status = OrderStatus.REJECTED.value
                self.orders[order.order_id] = order
                return False, validation_result
            
            # Store order
            order.status = OrderStatus.SUBMITTED.value
            self.orders[order.order_id] = order
            
            # Execute order
            try:
                execution_report = await self.executor.execute_order(order)
                self.execution_reports[execution_report.report_id] = execution_report
                
                # Update position
                quantity_change = execution_report.executed_quantity
                if order.side == OrderSide.SELL.value:
                    quantity_change = -quantity_change
                
                self.risk_manager.update_position(
                    order.symbol, 
                    quantity_change, 
                    execution_report.execution_price
                )
                
                logger.info(f"Order {order.order_id} executed successfully")
                return True, validation_result
                
            except Exception as e:
                logger.error(f"Order execution failed: {e}")
                order.status = OrderStatus.REJECTED.value
                return False, validation_result
            
        except Exception as e:
            logger.error(f"Error submitting order: {e}")
            order.status = OrderStatus.REJECTED.value
            self.orders[order.order_id] = order
            return False, OrderValidationResult(
                is_valid=False,
                risk_result=RiskCheckResult.REJECTED,
                messages=[f"Submission error: {str(e)}"]
            )
    
    async def cancel_order(self, order_id: str) -> bool:
        """Cancel an order"""
        try:
            if order_id not in self.orders:
                logger.warning(f"Order {order_id} not found")
                return False
            
            success = await self.executor.cancel_order(order_id)
            if success:
                self.orders[order_id].status = OrderStatus.CANCELLED.value
                logger.info(f"Order {order_id} cancelled successfully")
            
            return success
            
        except Exception as e:
            logger.error(f"Error cancelling order {order_id}: {e}")
            return False
    
    def get_order(self, order_id: str) -> Optional[TradeOrder]:
        """Get order by ID"""
        return self.orders.get(order_id)
    
    def get_orders_by_symbol(self, symbol: str) -> List[TradeOrder]:
        """Get all orders for a symbol"""
        return [order for order in self.orders.values() if order.symbol == symbol]
    
    def get_orders_by_status(self, status: OrderStatus) -> List[TradeOrder]:
        """Get all orders with a specific status"""
        return [order for order in self.orders.values() if order.status == status.value]
    
    def get_active_orders(self) -> List[TradeOrder]:
        """Get all active orders"""
        active_statuses = [OrderStatus.PENDING.value, OrderStatus.SUBMITTED.value, OrderStatus.PARTIALLY_FILLED.value]
        return [order for order in self.orders.values() if order.status in active_statuses]
    
    def get_execution_report(self, report_id: str) -> Optional[ExecutionReport]:
        """Get execution report by ID"""
        return self.execution_reports.get(report_id)
    
    def get_execution_reports_by_order(self, order_id: str) -> List[ExecutionReport]:
        """Get all execution reports for an order"""
        return [report for report in self.execution_reports.values() if report.order_id == order_id]
    
    def get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary"""
        return self.risk_manager.get_portfolio_summary()
    
    def get_order_statistics(self) -> Dict[str, Any]:
        """Get order statistics"""
        total_orders = len(self.orders)
        filled_orders = len([o for o in self.orders.values() if o.status == OrderStatus.FILLED.value])
        cancelled_orders = len([o for o in self.orders.values() if o.status == OrderStatus.CANCELLED.value])
        rejected_orders = len([o for o in self.orders.values() if o.status == OrderStatus.REJECTED.value])
        
        fill_rate = (filled_orders / total_orders * 100) if total_orders > 0 else 0
        
        return {
            'total_orders': total_orders,
            'filled_orders': filled_orders,
            'cancelled_orders': cancelled_orders,
            'rejected_orders': rejected_orders,
            'fill_rate_percent': fill_rate,
            'total_executions': len(self.execution_reports)
        }
    
    def create_market_order(self, symbol: str, side: OrderSide, quantity: float) -> TradeOrder:
        """Create a market order"""
        return TradeOrder(
            order_id=str(uuid.uuid4()),
            symbol=symbol,
            order_type=OrderType.MARKET.value,
            side=side.value,
            quantity=quantity,
            time_in_force=TimeInForce.IOC.value
        )
    
    def create_limit_order(self, symbol: str, side: OrderSide, quantity: float, 
                          price: float, time_in_force: TimeInForce = TimeInForce.GTC) -> TradeOrder:
        """Create a limit order"""
        return TradeOrder(
            order_id=str(uuid.uuid4()),
            symbol=symbol,
            order_type=OrderType.LIMIT.value,
            side=side.value,
            quantity=quantity,
            price=price,
            time_in_force=time_in_force.value
        )
    
    def create_stop_order(self, symbol: str, side: OrderSide, quantity: float, 
                         stop_price: float) -> TradeOrder:
        """Create a stop order"""
        return TradeOrder(
            order_id=str(uuid.uuid4()),
            symbol=symbol,
            order_type=OrderType.STOP.value,
            side=side.value,
            quantity=quantity,
            stop_price=stop_price,
            time_in_force=TimeInForce.GTC.value
        )
    
    def create_stop_limit_order(self, symbol: str, side: OrderSide, quantity: float,
                               stop_price: float, limit_price: float) -> TradeOrder:
        """Create a stop-limit order"""
        return TradeOrder(
            order_id=str(uuid.uuid4()),
            symbol=symbol,
            order_type=OrderType.STOP_LIMIT.value,
            side=side.value,
            quantity=quantity,
            price=limit_price,
            stop_price=stop_price,
            time_in_force=TimeInForce.GTC.value
        )
    
    async def bulk_submit_orders(self, orders: List[TradeOrder]) -> List[Tuple[bool, OrderValidationResult]]:
        """Submit multiple orders"""
        results = []
        for order in orders:
            result = await self.submit_order(order)
            results.append(result)
        return results
    
    async def cancel_all_orders(self, symbol: Optional[str] = None) -> Dict[str, bool]:
        """Cancel all orders or all orders for a specific symbol"""
        results = {}
        active_orders = self.get_active_orders()
        
        for order in active_orders:
            if symbol is None or order.symbol == symbol:
                success = await self.cancel_order(order.order_id)
                results[order.order_id] = success
        
        return results
    
    def set_risk_limits(self, risk_limits: RiskLimits):
        """Update risk limits"""
        self.risk_manager.risk_limits = risk_limits
    
    def get_risk_limits(self) -> RiskLimits:
        """Get current risk limits"""
        return self.risk_manager.risk_limits