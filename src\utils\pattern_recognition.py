#!/usr/bin/env python3
"""
Advanced Pattern Recognition
Comprehensive pattern recognition for technical analysis.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
import logging
from scipy import signal
from scipy.stats import linregress

logger = logging.getLogger(__name__)

class PatternType(Enum):
    """Types of chart patterns."""
    REVERSAL = "reversal"
    CONTINUATION = "continuation"
    CONSOLIDATION = "consolidation"
    BREAKOUT = "breakout"
    HARMONIC = "harmonic"
    CANDLESTICK = "candlestick"

class PatternStrength(Enum):
    """Pattern strength levels."""
    WEAK = "weak"
    MODERATE = "moderate"
    STRONG = "strong"
    VERY_STRONG = "very_strong"

class PatternDirection(Enum):
    """Pattern direction."""
    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"

@dataclass
class PatternResult:
    """Result of pattern recognition."""
    name: str
    pattern_type: PatternType
    direction: PatternDirection
    strength: PatternStrength
    start_index: int
    end_index: int
    confidence: float
    key_levels: List[float]
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    metadata: Dict = None

class AdvancedPatternRecognition:
    """Advanced pattern recognition system."""
    
    def __init__(self):
        self.patterns = {}
        self.min_pattern_length = 5
        self.max_pattern_length = 100
        self.confidence_threshold = 0.6
    
    def recognize_all_patterns(self, data: pd.DataFrame, 
                             symbol: str = None) -> List[PatternResult]:
        """Recognize all patterns in the given data."""
        try:
            if data.empty or len(data) < self.min_pattern_length:
                logger.warning(f"Insufficient data for pattern recognition: {len(data)} rows")
                return []
            
            patterns = []
            
            # Extract OHLCV data
            high = data['high'].values if 'high' in data.columns else data['close'].values
            low = data['low'].values if 'low' in data.columns else data['close'].values
            open_price = data['open'].values if 'open' in data.columns else data['close'].values
            close = data['close'].values
            volume = data['volume'].values if 'volume' in data.columns else np.ones(len(close))
            
            # Chart Patterns
            patterns.extend(self._recognize_chart_patterns(high, low, close))
            
            # Candlestick Patterns
            patterns.extend(self._recognize_candlestick_patterns(open_price, high, low, close))
            
            # Harmonic Patterns
            patterns.extend(self._recognize_harmonic_patterns(high, low, close))
            
            # Support/Resistance Patterns
            patterns.extend(self._recognize_support_resistance(high, low, close))
            
            # Volume Patterns
            patterns.extend(self._recognize_volume_patterns(close, volume))
            
            # Filter by confidence
            patterns = [p for p in patterns if p.confidence >= self.confidence_threshold]
            
            # Sort by confidence
            patterns.sort(key=lambda x: x.confidence, reverse=True)
            
            return patterns
            
        except Exception as e:
            logger.error(f"Error recognizing patterns: {e}")
            return []
    
    def _recognize_chart_patterns(self, high: np.ndarray, low: np.ndarray, 
                                close: np.ndarray) -> List[PatternResult]:
        """Recognize classic chart patterns."""
        patterns = []
        
        try:
            # Head and Shoulders
            patterns.extend(self._find_head_and_shoulders(high, low, close))
            
            # Double Top/Bottom
            patterns.extend(self._find_double_top_bottom(high, low, close))
            
            # Triangle Patterns
            patterns.extend(self._find_triangles(high, low, close))
            
            # Flag and Pennant
            patterns.extend(self._find_flags_pennants(high, low, close))
            
            # Wedges
            patterns.extend(self._find_wedges(high, low, close))
            
            # Cup and Handle
            patterns.extend(self._find_cup_and_handle(high, low, close))
            
        except Exception as e:
            logger.error(f"Error recognizing chart patterns: {e}")
        
        return patterns
    
    def _recognize_candlestick_patterns(self, open_price: np.ndarray, high: np.ndarray,
                                      low: np.ndarray, close: np.ndarray) -> List[PatternResult]:
        """Recognize candlestick patterns."""
        patterns = []
        
        try:
            # Single candlestick patterns
            patterns.extend(self._find_doji(open_price, high, low, close))
            patterns.extend(self._find_hammer(open_price, high, low, close))
            patterns.extend(self._find_shooting_star(open_price, high, low, close))
            patterns.extend(self._find_engulfing(open_price, high, low, close))
            
            # Multi-candlestick patterns
            patterns.extend(self._find_morning_evening_star(open_price, high, low, close))
            patterns.extend(self._find_three_soldiers_crows(open_price, high, low, close))
            
        except Exception as e:
            logger.error(f"Error recognizing candlestick patterns: {e}")
        
        return patterns
    
    def _recognize_harmonic_patterns(self, high: np.ndarray, low: np.ndarray,
                                   close: np.ndarray) -> List[PatternResult]:
        """Recognize harmonic patterns."""
        patterns = []
        
        try:
            # Gartley Pattern
            patterns.extend(self._find_gartley(high, low, close))
            
            # Butterfly Pattern
            patterns.extend(self._find_butterfly(high, low, close))
            
            # Bat Pattern
            patterns.extend(self._find_bat(high, low, close))
            
            # Crab Pattern
            patterns.extend(self._find_crab(high, low, close))
            
        except Exception as e:
            logger.error(f"Error recognizing harmonic patterns: {e}")
        
        return patterns
    
    def _recognize_support_resistance(self, high: np.ndarray, low: np.ndarray,
                                    close: np.ndarray) -> List[PatternResult]:
        """Recognize support and resistance patterns."""
        patterns = []
        
        try:
            # Support levels
            support_levels = self._find_support_levels(low, close)
            for level in support_levels:
                patterns.append(PatternResult(
                    name="Support_Level",
                    pattern_type=PatternType.CONSOLIDATION,
                    direction=PatternDirection.BULLISH,
                    strength=PatternStrength.MODERATE,
                    start_index=level['start'],
                    end_index=level['end'],
                    confidence=level['confidence'],
                    key_levels=[level['price']]
                ))
            
            # Resistance levels
            resistance_levels = self._find_resistance_levels(high, close)
            for level in resistance_levels:
                patterns.append(PatternResult(
                    name="Resistance_Level",
                    pattern_type=PatternType.CONSOLIDATION,
                    direction=PatternDirection.BEARISH,
                    strength=PatternStrength.MODERATE,
                    start_index=level['start'],
                    end_index=level['end'],
                    confidence=level['confidence'],
                    key_levels=[level['price']]
                ))
            
        except Exception as e:
            logger.error(f"Error recognizing support/resistance: {e}")
        
        return patterns
    
    def _recognize_volume_patterns(self, close: np.ndarray, volume: np.ndarray) -> List[PatternResult]:
        """Recognize volume-based patterns."""
        patterns = []
        
        try:
            # Volume breakout
            volume_breakouts = self._find_volume_breakouts(close, volume)
            for breakout in volume_breakouts:
                patterns.append(PatternResult(
                    name="Volume_Breakout",
                    pattern_type=PatternType.BREAKOUT,
                    direction=breakout['direction'],
                    strength=PatternStrength.STRONG,
                    start_index=breakout['start'],
                    end_index=breakout['end'],
                    confidence=breakout['confidence'],
                    key_levels=[breakout['price']]
                ))
            
            # Volume divergence
            divergences = self._find_volume_divergence(close, volume)
            for div in divergences:
                patterns.append(PatternResult(
                    name="Volume_Divergence",
                    pattern_type=PatternType.REVERSAL,
                    direction=div['direction'],
                    strength=PatternStrength.MODERATE,
                    start_index=div['start'],
                    end_index=div['end'],
                    confidence=div['confidence'],
                    key_levels=[]
                ))
            
        except Exception as e:
            logger.error(f"Error recognizing volume patterns: {e}")
        
        return patterns
    
    # Chart Pattern Recognition Methods
    def _find_head_and_shoulders(self, high: np.ndarray, low: np.ndarray, 
                               close: np.ndarray) -> List[PatternResult]:
        """Find Head and Shoulders patterns."""
        patterns = []
        
        try:
            # Find local maxima
            peaks = signal.find_peaks(high, distance=5)[0]
            
            if len(peaks) >= 3:
                for i in range(len(peaks) - 2):
                    left_shoulder = peaks[i]
                    head = peaks[i + 1]
                    right_shoulder = peaks[i + 2]
                    
                    # Check if head is higher than shoulders
                    if (high[head] > high[left_shoulder] and 
                        high[head] > high[right_shoulder] and
                        abs(high[left_shoulder] - high[right_shoulder]) / high[head] < 0.05):
                        
                        # Calculate neckline
                        neckline_left = np.min(low[left_shoulder:head])
                        neckline_right = np.min(low[head:right_shoulder])
                        neckline = (neckline_left + neckline_right) / 2
                        
                        confidence = self._calculate_pattern_confidence(
                            high[left_shoulder:right_shoulder+1],
                            low[left_shoulder:right_shoulder+1]
                        )
                        
                        if confidence > 0.6:
                            patterns.append(PatternResult(
                                name="Head_and_Shoulders",
                                pattern_type=PatternType.REVERSAL,
                                direction=PatternDirection.BEARISH,
                                strength=PatternStrength.STRONG,
                                start_index=left_shoulder,
                                end_index=right_shoulder,
                                confidence=confidence,
                                key_levels=[high[head], neckline],
                                target_price=neckline - (high[head] - neckline),
                                stop_loss=high[head]
                            ))
        
        except Exception as e:
            logger.error(f"Error finding head and shoulders: {e}")
        
        return patterns
    
    def _find_double_top_bottom(self, high: np.ndarray, low: np.ndarray,
                              close: np.ndarray) -> List[PatternResult]:
        """Find Double Top and Double Bottom patterns."""
        patterns = []
        
        try:
            # Double Top
            peaks = signal.find_peaks(high, distance=10)[0]
            if len(peaks) >= 2:
                for i in range(len(peaks) - 1):
                    peak1 = peaks[i]
                    peak2 = peaks[i + 1]
                    
                    # Check if peaks are similar height
                    if abs(high[peak1] - high[peak2]) / max(high[peak1], high[peak2]) < 0.03:
                        valley = np.argmin(low[peak1:peak2]) + peak1
                        
                        confidence = self._calculate_pattern_confidence(
                            high[peak1:peak2+1], low[peak1:peak2+1]
                        )
                        
                        if confidence > 0.65:
                            patterns.append(PatternResult(
                                name="Double_Top",
                                pattern_type=PatternType.REVERSAL,
                                direction=PatternDirection.BEARISH,
                                strength=PatternStrength.STRONG,
                                start_index=peak1,
                                end_index=peak2,
                                confidence=confidence,
                                key_levels=[high[peak1], low[valley]],
                                target_price=low[valley] - (high[peak1] - low[valley]),
                                stop_loss=max(high[peak1], high[peak2])
                            ))
            
            # Double Bottom
            troughs = signal.find_peaks(-low, distance=10)[0]
            if len(troughs) >= 2:
                for i in range(len(troughs) - 1):
                    trough1 = troughs[i]
                    trough2 = troughs[i + 1]
                    
                    # Check if troughs are similar depth
                    if abs(low[trough1] - low[trough2]) / min(low[trough1], low[trough2]) < 0.03:
                        peak = np.argmax(high[trough1:trough2]) + trough1
                        
                        confidence = self._calculate_pattern_confidence(
                            high[trough1:trough2+1], low[trough1:trough2+1]
                        )
                        
                        if confidence > 0.65:
                            patterns.append(PatternResult(
                                name="Double_Bottom",
                                pattern_type=PatternType.REVERSAL,
                                direction=PatternDirection.BULLISH,
                                strength=PatternStrength.STRONG,
                                start_index=trough1,
                                end_index=trough2,
                                confidence=confidence,
                                key_levels=[low[trough1], high[peak]],
                                target_price=high[peak] + (high[peak] - low[trough1]),
                                stop_loss=min(low[trough1], low[trough2])
                            ))
        
        except Exception as e:
            logger.error(f"Error finding double top/bottom: {e}")
        
        return patterns
    
    def _find_triangles(self, high: np.ndarray, low: np.ndarray,
                       close: np.ndarray) -> List[PatternResult]:
        """Find triangle patterns."""
        patterns = []
        
        try:
            window = 20
            for i in range(window, len(high) - window):
                segment_high = high[i-window:i+window]
                segment_low = low[i-window:i+window]
                
                # Calculate trend lines
                high_trend = self._calculate_trend_line(segment_high)
                low_trend = self._calculate_trend_line(segment_low)
                
                if high_trend and low_trend:
                    high_slope = high_trend['slope']
                    low_slope = low_trend['slope']
                    
                    # Ascending Triangle
                    if abs(high_slope) < 0.001 and low_slope > 0.001:
                        patterns.append(PatternResult(
                            name="Ascending_Triangle",
                            pattern_type=PatternType.CONTINUATION,
                            direction=PatternDirection.BULLISH,
                            strength=PatternStrength.MODERATE,
                            start_index=i-window,
                            end_index=i+window,
                            confidence=0.7,
                            key_levels=[np.max(segment_high), np.min(segment_low)]
                        ))
                    
                    # Descending Triangle
                    elif abs(low_slope) < 0.001 and high_slope < -0.001:
                        patterns.append(PatternResult(
                            name="Descending_Triangle",
                            pattern_type=PatternType.CONTINUATION,
                            direction=PatternDirection.BEARISH,
                            strength=PatternStrength.MODERATE,
                            start_index=i-window,
                            end_index=i+window,
                            confidence=0.7,
                            key_levels=[np.max(segment_high), np.min(segment_low)]
                        ))
                    
                    # Symmetrical Triangle
                    elif high_slope < -0.001 and low_slope > 0.001:
                        patterns.append(PatternResult(
                            name="Symmetrical_Triangle",
                            pattern_type=PatternType.CONTINUATION,
                            direction=PatternDirection.NEUTRAL,
                            strength=PatternStrength.MODERATE,
                            start_index=i-window,
                            end_index=i+window,
                            confidence=0.65,
                            key_levels=[np.max(segment_high), np.min(segment_low)]
                        ))
        
        except Exception as e:
            logger.error(f"Error finding triangles: {e}")
        
        return patterns
    
    def _find_flags_pennants(self, high: np.ndarray, low: np.ndarray,
                           close: np.ndarray) -> List[PatternResult]:
        """Find flag and pennant patterns."""
        patterns = []
        
        try:
            # Look for strong moves followed by consolidation
            for i in range(20, len(close) - 10):
                # Check for strong move
                move_start = i - 20
                move_end = i
                price_change = (close[move_end] - close[move_start]) / close[move_start]
                
                if abs(price_change) > 0.05:  # 5% move
                    # Check for consolidation
                    consolidation = close[move_end:move_end+10]
                    if len(consolidation) >= 5:
                        volatility = np.std(consolidation) / np.mean(consolidation)
                        
                        if volatility < 0.02:  # Low volatility consolidation
                            direction = PatternDirection.BULLISH if price_change > 0 else PatternDirection.BEARISH
                            pattern_name = "Bull_Flag" if price_change > 0 else "Bear_Flag"
                            
                            patterns.append(PatternResult(
                                name=pattern_name,
                                pattern_type=PatternType.CONTINUATION,
                                direction=direction,
                                strength=PatternStrength.MODERATE,
                                start_index=move_start,
                                end_index=move_end+10,
                                confidence=0.7,
                                key_levels=[close[move_start], close[move_end]]
                            ))
        
        except Exception as e:
            logger.error(f"Error finding flags/pennants: {e}")
        
        return patterns
    
    def _find_wedges(self, high: np.ndarray, low: np.ndarray,
                    close: np.ndarray) -> List[PatternResult]:
        """Find wedge patterns."""
        patterns = []
        
        try:
            window = 25
            for i in range(window, len(high) - 5):
                segment_high = high[i-window:i]
                segment_low = low[i-window:i]
                
                high_trend = self._calculate_trend_line(segment_high)
                low_trend = self._calculate_trend_line(segment_low)
                
                if high_trend and low_trend:
                    high_slope = high_trend['slope']
                    low_slope = low_trend['slope']
                    
                    # Rising Wedge (bearish)
                    if high_slope > 0 and low_slope > 0 and high_slope < low_slope:
                        patterns.append(PatternResult(
                            name="Rising_Wedge",
                            pattern_type=PatternType.REVERSAL,
                            direction=PatternDirection.BEARISH,
                            strength=PatternStrength.MODERATE,
                            start_index=i-window,
                            end_index=i,
                            confidence=0.65,
                            key_levels=[np.max(segment_high), np.min(segment_low)]
                        ))
                    
                    # Falling Wedge (bullish)
                    elif high_slope < 0 and low_slope < 0 and abs(high_slope) < abs(low_slope):
                        patterns.append(PatternResult(
                            name="Falling_Wedge",
                            pattern_type=PatternType.REVERSAL,
                            direction=PatternDirection.BULLISH,
                            strength=PatternStrength.MODERATE,
                            start_index=i-window,
                            end_index=i,
                            confidence=0.65,
                            key_levels=[np.max(segment_high), np.min(segment_low)]
                        ))
        
        except Exception as e:
            logger.error(f"Error finding wedges: {e}")
        
        return patterns
    
    def _find_cup_and_handle(self, high: np.ndarray, low: np.ndarray,
                           close: np.ndarray) -> List[PatternResult]:
        """Find cup and handle patterns."""
        patterns = []
        
        try:
            min_length = 30
            for i in range(min_length, len(close) - 10):
                segment = close[i-min_length:i]
                
                # Find the cup shape
                start_price = segment[0]
                end_price = segment[-1]
                min_price = np.min(segment)
                min_index = np.argmin(segment)
                
                # Check cup criteria
                if (abs(start_price - end_price) / start_price < 0.05 and  # Similar levels
                    (start_price - min_price) / start_price > 0.15 and      # Significant depth
                    min_length * 0.3 < min_index < min_length * 0.7):       # Bottom in middle third
                    
                    # Look for handle
                    handle_segment = close[i:i+10] if i+10 <= len(close) else close[i:]
                    if len(handle_segment) >= 5:
                        handle_volatility = np.std(handle_segment) / np.mean(handle_segment)
                        
                        if handle_volatility < 0.03:  # Low volatility handle
                            patterns.append(PatternResult(
                                name="Cup_and_Handle",
                                pattern_type=PatternType.CONTINUATION,
                                direction=PatternDirection.BULLISH,
                                strength=PatternStrength.STRONG,
                                start_index=i-min_length,
                                end_index=i+len(handle_segment),
                                confidence=0.75,
                                key_levels=[start_price, min_price],
                                target_price=start_price + (start_price - min_price),
                                stop_loss=min_price
                            ))
        
        except Exception as e:
            logger.error(f"Error finding cup and handle: {e}")
        
        return patterns
    
    # Candlestick Pattern Recognition Methods
    def _find_doji(self, open_price: np.ndarray, high: np.ndarray,
                  low: np.ndarray, close: np.ndarray) -> List[PatternResult]:
        """Find Doji candlestick patterns."""
        patterns = []
        
        try:
            for i in range(len(close)):
                body_size = abs(close[i] - open_price[i])
                total_range = high[i] - low[i]
                
                if total_range > 0 and body_size / total_range < 0.1:  # Small body
                    patterns.append(PatternResult(
                        name="Doji",
                        pattern_type=PatternType.REVERSAL,
                        direction=PatternDirection.NEUTRAL,
                        strength=PatternStrength.MODERATE,
                        start_index=i,
                        end_index=i,
                        confidence=0.6,
                        key_levels=[close[i]]
                    ))
        
        except Exception as e:
            logger.error(f"Error finding doji: {e}")
        
        return patterns
    
    def _find_hammer(self, open_price: np.ndarray, high: np.ndarray,
                    low: np.ndarray, close: np.ndarray) -> List[PatternResult]:
        """Find Hammer candlestick patterns."""
        patterns = []
        
        try:
            for i in range(1, len(close)):
                body_size = abs(close[i] - open_price[i])
                lower_shadow = min(open_price[i], close[i]) - low[i]
                upper_shadow = high[i] - max(open_price[i], close[i])
                total_range = high[i] - low[i]
                
                if (total_range > 0 and
                    lower_shadow > 2 * body_size and  # Long lower shadow
                    upper_shadow < body_size and      # Short upper shadow
                    close[i] > close[i-1]):           # Bullish context
                    
                    patterns.append(PatternResult(
                        name="Hammer",
                        pattern_type=PatternType.REVERSAL,
                        direction=PatternDirection.BULLISH,
                        strength=PatternStrength.MODERATE,
                        start_index=i,
                        end_index=i,
                        confidence=0.7,
                        key_levels=[low[i], close[i]]
                    ))
        
        except Exception as e:
            logger.error(f"Error finding hammer: {e}")
        
        return patterns
    
    def _find_shooting_star(self, open_price: np.ndarray, high: np.ndarray,
                          low: np.ndarray, close: np.ndarray) -> List[PatternResult]:
        """Find Shooting Star candlestick patterns."""
        patterns = []
        
        try:
            for i in range(1, len(close)):
                body_size = abs(close[i] - open_price[i])
                lower_shadow = min(open_price[i], close[i]) - low[i]
                upper_shadow = high[i] - max(open_price[i], close[i])
                total_range = high[i] - low[i]
                
                if (total_range > 0 and
                    upper_shadow > 2 * body_size and  # Long upper shadow
                    lower_shadow < body_size and      # Short lower shadow
                    close[i] < close[i-1]):           # Bearish context
                    
                    patterns.append(PatternResult(
                        name="Shooting_Star",
                        pattern_type=PatternType.REVERSAL,
                        direction=PatternDirection.BEARISH,
                        strength=PatternStrength.MODERATE,
                        start_index=i,
                        end_index=i,
                        confidence=0.7,
                        key_levels=[high[i], close[i]]
                    ))
        
        except Exception as e:
            logger.error(f"Error finding shooting star: {e}")
        
        return patterns
    
    def _find_engulfing(self, open_price: np.ndarray, high: np.ndarray,
                       low: np.ndarray, close: np.ndarray) -> List[PatternResult]:
        """Find Engulfing candlestick patterns."""
        patterns = []
        
        try:
            for i in range(1, len(close)):
                prev_body = abs(close[i-1] - open_price[i-1])
                curr_body = abs(close[i] - open_price[i])
                
                # Bullish Engulfing
                if (close[i-1] < open_price[i-1] and  # Previous red candle
                    close[i] > open_price[i] and      # Current green candle
                    open_price[i] < close[i-1] and    # Opens below previous close
                    close[i] > open_price[i-1] and    # Closes above previous open
                    curr_body > prev_body):           # Larger body
                    
                    patterns.append(PatternResult(
                        name="Bullish_Engulfing",
                        pattern_type=PatternType.REVERSAL,
                        direction=PatternDirection.BULLISH,
                        strength=PatternStrength.STRONG,
                        start_index=i-1,
                        end_index=i,
                        confidence=0.8,
                        key_levels=[open_price[i], close[i]]
                    ))
                
                # Bearish Engulfing
                elif (close[i-1] > open_price[i-1] and  # Previous green candle
                      close[i] < open_price[i] and      # Current red candle
                      open_price[i] > close[i-1] and    # Opens above previous close
                      close[i] < open_price[i-1] and    # Closes below previous open
                      curr_body > prev_body):           # Larger body
                    
                    patterns.append(PatternResult(
                        name="Bearish_Engulfing",
                        pattern_type=PatternType.REVERSAL,
                        direction=PatternDirection.BEARISH,
                        strength=PatternStrength.STRONG,
                        start_index=i-1,
                        end_index=i,
                        confidence=0.8,
                        key_levels=[open_price[i], close[i]]
                    ))
        
        except Exception as e:
            logger.error(f"Error finding engulfing: {e}")
        
        return patterns
    
    def _find_morning_evening_star(self, open_price: np.ndarray, high: np.ndarray,
                                 low: np.ndarray, close: np.ndarray) -> List[PatternResult]:
        """Find Morning Star and Evening Star patterns."""
        patterns = []
        
        try:
            for i in range(2, len(close)):
                # Morning Star
                if (close[i-2] < open_price[i-2] and  # First candle is red
                    abs(close[i-1] - open_price[i-1]) < abs(close[i-2] - open_price[i-2]) * 0.3 and  # Small middle candle
                    close[i] > open_price[i] and      # Third candle is green
                    close[i] > (open_price[i-2] + close[i-2]) / 2):  # Closes above midpoint of first candle
                    
                    patterns.append(PatternResult(
                        name="Morning_Star",
                        pattern_type=PatternType.REVERSAL,
                        direction=PatternDirection.BULLISH,
                        strength=PatternStrength.STRONG,
                        start_index=i-2,
                        end_index=i,
                        confidence=0.85,
                        key_levels=[low[i-1], close[i]]
                    ))
                
                # Evening Star
                elif (close[i-2] > open_price[i-2] and  # First candle is green
                      abs(close[i-1] - open_price[i-1]) < abs(close[i-2] - open_price[i-2]) * 0.3 and  # Small middle candle
                      close[i] < open_price[i] and      # Third candle is red
                      close[i] < (open_price[i-2] + close[i-2]) / 2):  # Closes below midpoint of first candle
                    
                    patterns.append(PatternResult(
                        name="Evening_Star",
                        pattern_type=PatternType.REVERSAL,
                        direction=PatternDirection.BEARISH,
                        strength=PatternStrength.STRONG,
                        start_index=i-2,
                        end_index=i,
                        confidence=0.85,
                        key_levels=[high[i-1], close[i]]
                    ))
        
        except Exception as e:
            logger.error(f"Error finding morning/evening star: {e}")
        
        return patterns
    
    def _find_three_soldiers_crows(self, open_price: np.ndarray, high: np.ndarray,
                                 low: np.ndarray, close: np.ndarray) -> List[PatternResult]:
        """Find Three White Soldiers and Three Black Crows patterns."""
        patterns = []
        
        try:
            for i in range(2, len(close)):
                # Three White Soldiers
                if (all(close[j] > open_price[j] for j in range(i-2, i+1)) and  # All green candles
                    all(close[j] > close[j-1] for j in range(i-1, i+1)) and     # Ascending closes
                    all(open_price[j] > open_price[j-1] for j in range(i-1, i+1))):  # Ascending opens
                    
                    patterns.append(PatternResult(
                        name="Three_White_Soldiers",
                        pattern_type=PatternType.CONTINUATION,
                        direction=PatternDirection.BULLISH,
                        strength=PatternStrength.STRONG,
                        start_index=i-2,
                        end_index=i,
                        confidence=0.8,
                        key_levels=[open_price[i-2], close[i]]
                    ))
                
                # Three Black Crows
                elif (all(close[j] < open_price[j] for j in range(i-2, i+1)) and  # All red candles
                      all(close[j] < close[j-1] for j in range(i-1, i+1)) and     # Descending closes
                      all(open_price[j] < open_price[j-1] for j in range(i-1, i+1))):  # Descending opens
                    
                    patterns.append(PatternResult(
                        name="Three_Black_Crows",
                        pattern_type=PatternType.CONTINUATION,
                        direction=PatternDirection.BEARISH,
                        strength=PatternStrength.STRONG,
                        start_index=i-2,
                        end_index=i,
                        confidence=0.8,
                        key_levels=[open_price[i-2], close[i]]
                    ))
        
        except Exception as e:
            logger.error(f"Error finding three soldiers/crows: {e}")
        
        return patterns
    
    # Harmonic Pattern Recognition Methods
    def _find_gartley(self, high: np.ndarray, low: np.ndarray,
                     close: np.ndarray) -> List[PatternResult]:
        """Find Gartley harmonic patterns."""
        patterns = []
        
        try:
            # Simplified Gartley detection
            # This is a basic implementation - real harmonic pattern detection is very complex
            for i in range(50, len(close) - 10):
                segment = close[i-50:i]
                if len(segment) >= 5:
                    # Look for XABCD pattern structure
                    extremes = self._find_extremes(segment)
                    if len(extremes) >= 5:
                        # Basic Fibonacci ratio checks would go here
                        patterns.append(PatternResult(
                            name="Gartley_Pattern",
                            pattern_type=PatternType.HARMONIC,
                            direction=PatternDirection.NEUTRAL,
                            strength=PatternStrength.MODERATE,
                            start_index=i-50,
                            end_index=i,
                            confidence=0.6,
                            key_levels=extremes[:5]
                        ))
        
        except Exception as e:
            logger.error(f"Error finding Gartley: {e}")
        
        return patterns
    
    def _find_butterfly(self, high: np.ndarray, low: np.ndarray,
                       close: np.ndarray) -> List[PatternResult]:
        """Find Butterfly harmonic patterns."""
        patterns = []
        # Simplified implementation
        return patterns
    
    def _find_bat(self, high: np.ndarray, low: np.ndarray,
                 close: np.ndarray) -> List[PatternResult]:
        """Find Bat harmonic patterns."""
        patterns = []
        # Simplified implementation
        return patterns
    
    def _find_crab(self, high: np.ndarray, low: np.ndarray,
                  close: np.ndarray) -> List[PatternResult]:
        """Find Crab harmonic patterns."""
        patterns = []
        # Simplified implementation
        return patterns
    
    # Support/Resistance Methods
    def _find_support_levels(self, low: np.ndarray, close: np.ndarray) -> List[Dict]:
        """Find support levels."""
        levels = []
        
        try:
            # Find local minima
            troughs = signal.find_peaks(-low, distance=5)[0]
            
            for trough in troughs:
                # Count how many times price bounced from this level
                level_price = low[trough]
                tolerance = level_price * 0.02  # 2% tolerance
                
                bounces = 0
                for i in range(len(low)):
                    if abs(low[i] - level_price) <= tolerance and close[i] > low[i]:
                        bounces += 1
                
                if bounces >= 2:
                    levels.append({
                        'price': level_price,
                        'start': max(0, trough - 10),
                        'end': min(len(low) - 1, trough + 10),
                        'confidence': min(0.9, 0.5 + bounces * 0.1),
                        'bounces': bounces
                    })
        
        except Exception as e:
            logger.error(f"Error finding support levels: {e}")
        
        return levels
    
    def _find_resistance_levels(self, high: np.ndarray, close: np.ndarray) -> List[Dict]:
        """Find resistance levels."""
        levels = []
        
        try:
            # Find local maxima
            peaks = signal.find_peaks(high, distance=5)[0]
            
            for peak in peaks:
                # Count how many times price was rejected from this level
                level_price = high[peak]
                tolerance = level_price * 0.02  # 2% tolerance
                
                rejections = 0
                for i in range(len(high)):
                    if abs(high[i] - level_price) <= tolerance and close[i] < high[i]:
                        rejections += 1
                
                if rejections >= 2:
                    levels.append({
                        'price': level_price,
                        'start': max(0, peak - 10),
                        'end': min(len(high) - 1, peak + 10),
                        'confidence': min(0.9, 0.5 + rejections * 0.1),
                        'rejections': rejections
                    })
        
        except Exception as e:
            logger.error(f"Error finding resistance levels: {e}")
        
        return levels
    
    # Volume Pattern Methods
    def _find_volume_breakouts(self, close: np.ndarray, volume: np.ndarray) -> List[Dict]:
        """Find volume breakout patterns."""
        breakouts = []
        
        try:
            volume_ma = pd.Series(volume).rolling(window=20).mean().values
            
            for i in range(20, len(volume) - 1):
                if volume[i] > 2 * volume_ma[i]:  # Volume spike
                    price_change = (close[i] - close[i-1]) / close[i-1]
                    
                    if abs(price_change) > 0.02:  # Significant price move
                        direction = PatternDirection.BULLISH if price_change > 0 else PatternDirection.BEARISH
                        
                        breakouts.append({
                            'price': close[i],
                            'start': i-1,
                            'end': i,
                            'direction': direction,
                            'confidence': min(0.9, 0.6 + abs(price_change) * 10),
                            'volume_ratio': volume[i] / volume_ma[i]
                        })
        
        except Exception as e:
            logger.error(f"Error finding volume breakouts: {e}")
        
        return breakouts
    
    def _find_volume_divergence(self, close: np.ndarray, volume: np.ndarray) -> List[Dict]:
        """Find volume divergence patterns."""
        divergences = []
        
        try:
            window = 10
            for i in range(window, len(close) - window):
                price_trend = (close[i+window] - close[i-window]) / close[i-window]
                volume_trend = (np.mean(volume[i:i+window]) - np.mean(volume[i-window:i])) / np.mean(volume[i-window:i])
                
                # Bullish divergence: price down, volume up
                if price_trend < -0.05 and volume_trend > 0.2:
                    divergences.append({
                        'start': i-window,
                        'end': i+window,
                        'direction': PatternDirection.BULLISH,
                        'confidence': 0.7,
                        'price_trend': price_trend,
                        'volume_trend': volume_trend
                    })
                
                # Bearish divergence: price up, volume down
                elif price_trend > 0.05 and volume_trend < -0.2:
                    divergences.append({
                        'start': i-window,
                        'end': i+window,
                        'direction': PatternDirection.BEARISH,
                        'confidence': 0.7,
                        'price_trend': price_trend,
                        'volume_trend': volume_trend
                    })
        
        except Exception as e:
            logger.error(f"Error finding volume divergence: {e}")
        
        return divergences
    
    # Helper Methods
    def _calculate_trend_line(self, data: np.ndarray) -> Optional[Dict]:
        """Calculate trend line for given data."""
        try:
            if len(data) < 2:
                return None
            
            x = np.arange(len(data))
            slope, intercept, r_value, p_value, std_err = linregress(x, data)
            
            return {
                'slope': slope,
                'intercept': intercept,
                'r_squared': r_value ** 2,
                'p_value': p_value
            }
        
        except Exception as e:
            logger.error(f"Error calculating trend line: {e}")
            return None
    
    def _calculate_pattern_confidence(self, high: np.ndarray, low: np.ndarray) -> float:
        """Calculate confidence score for a pattern."""
        try:
            if len(high) < 2 or len(low) < 2:
                return 0.0
            
            # Basic confidence based on price action consistency
            price_range = np.max(high) - np.min(low)
            volatility = np.std(high - low)
            
            if price_range == 0:
                return 0.0
            
            consistency = 1 - (volatility / price_range)
            return max(0.0, min(1.0, consistency))
        
        except Exception as e:
            logger.error(f"Error calculating pattern confidence: {e}")
            return 0.0
    
    def _find_extremes(self, data: np.ndarray) -> List[float]:
        """Find extreme points in data."""
        try:
            peaks = signal.find_peaks(data, distance=3)[0]
            troughs = signal.find_peaks(-data, distance=3)[0]
            
            extremes = []
            all_extremes = sorted(list(peaks) + list(troughs))
            
            for idx in all_extremes:
                extremes.append(data[idx])
            
            return extremes
        
        except Exception as e:
            logger.error(f"Error finding extremes: {e}")
            return []
    
    def get_pattern_summary(self, patterns: List[PatternResult]) -> Dict[str, Any]:
        """Get summary of recognized patterns."""
        summary = {
            'total_patterns': len(patterns),
            'by_type': {},
            'by_direction': {},
            'by_strength': {},
            'high_confidence': [],
            'recent_patterns': []
        }
        
        try:
            for pattern in patterns:
                # Group by type
                pattern_type = pattern.pattern_type.value
                if pattern_type not in summary['by_type']:
                    summary['by_type'][pattern_type] = 0
                summary['by_type'][pattern_type] += 1
                
                # Group by direction
                direction = pattern.direction.value
                if direction not in summary['by_direction']:
                    summary['by_direction'][direction] = 0
                summary['by_direction'][direction] += 1
                
                # Group by strength
                strength = pattern.strength.value
                if strength not in summary['by_strength']:
                    summary['by_strength'][strength] = 0
                summary['by_strength'][strength] += 1
                
                # High confidence patterns
                if pattern.confidence > 0.8:
                    summary['high_confidence'].append({
                        'name': pattern.name,
                        'confidence': pattern.confidence,
                        'direction': pattern.direction.value
                    })
                
                # Recent patterns (last 20% of data)
                data_length = 100  # Assume 100 data points for percentage calculation
                if pattern.end_index > data_length * 0.8:
                    summary['recent_patterns'].append({
                        'name': pattern.name,
                        'direction': pattern.direction.value,
                        'strength': pattern.strength.value
                    })
        
        except Exception as e:
            logger.error(f"Error creating pattern summary: {e}")
        
        return summary