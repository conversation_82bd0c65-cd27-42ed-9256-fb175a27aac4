"""Portfolio analytics and performance measurement for the Noryon V2 trading system"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import logging
from scipy import stats
from scipy.optimize import minimize

logger = logging.getLogger(__name__)


class PerformanceMetric(Enum):
    """Types of performance metrics"""
    TOTAL_RETURN = "total_return"
    ANNUALIZED_RETURN = "annualized_return"
    VOLATILITY = "volatility"
    SHARPE_RATIO = "sharpe_ratio"
    SORTINO_RATIO = "sortino_ratio"
    CALMAR_RATIO = "calmar_ratio"
    MAX_DRAWDOWN = "max_drawdown"
    VAR = "var"
    CVAR = "cvar"
    BETA = "beta"
    ALPHA = "alpha"
    INFORMATION_RATIO = "information_ratio"
    TRACKING_ERROR = "tracking_error"


class RiskMetric(Enum):
    """Types of risk metrics"""
    VOLATILITY = "volatility"
    DOWNSIDE_DEVIATION = "downside_deviation"
    MAX_DRAWDOWN = "max_drawdown"
    VAR_95 = "var_95"
    VAR_99 = "var_99"
    CVAR_95 = "cvar_95"
    CVAR_99 = "cvar_99"
    SKEWNESS = "skewness"
    KURTOSIS = "kurtosis"
    TAIL_RATIO = "tail_ratio"


@dataclass
class PerformanceReport:
    """Portfolio performance report"""
    portfolio_id: str
    start_date: datetime
    end_date: datetime
    total_return: float
    annualized_return: float
    volatility: float
    sharpe_ratio: float
    sortino_ratio: float
    max_drawdown: float
    var_95: float
    cvar_95: float
    beta: Optional[float]
    alpha: Optional[float]
    win_rate: float
    profit_factor: float
    calmar_ratio: float
    timestamp: datetime
    

@dataclass
class RiskReport:
    """Portfolio risk report"""
    portfolio_id: str
    analysis_date: datetime
    var_1d_95: float
    var_1d_99: float
    cvar_1d_95: float
    cvar_1d_99: float
    max_drawdown: float
    volatility: float
    downside_deviation: float
    skewness: float
    kurtosis: float
    tail_ratio: float
    concentration_risk: float
    liquidity_risk: float
    correlation_risk: float
    

@dataclass
class AttributionResult:
    """Performance attribution result"""
    total_return: float
    benchmark_return: float
    active_return: float
    asset_allocation_effect: Dict[str, float]
    security_selection_effect: Dict[str, float]
    interaction_effect: Dict[str, float]
    total_attribution: float
    

class PortfolioAnalytics:
    """Comprehensive portfolio analytics and performance measurement"""
    
    def __init__(self, risk_free_rate: float = 0.02):
        self.risk_free_rate = risk_free_rate
        self.trading_days_per_year = 252
        
    def calculate_performance_metrics(self, returns: pd.Series,
                                    benchmark_returns: Optional[pd.Series] = None) -> Dict[str, float]:
        """Calculate comprehensive performance metrics"""
        try:
            metrics = {}
            
            # Basic return metrics
            metrics['total_return'] = self._calculate_total_return(returns)
            metrics['annualized_return'] = self._calculate_annualized_return(returns)
            metrics['volatility'] = self._calculate_volatility(returns)
            
            # Risk-adjusted metrics
            metrics['sharpe_ratio'] = self._calculate_sharpe_ratio(returns)
            metrics['sortino_ratio'] = self._calculate_sortino_ratio(returns)
            metrics['calmar_ratio'] = self._calculate_calmar_ratio(returns)
            
            # Drawdown metrics
            metrics['max_drawdown'] = self._calculate_max_drawdown(returns)
            metrics['avg_drawdown'] = self._calculate_avg_drawdown(returns)
            metrics['drawdown_duration'] = self._calculate_drawdown_duration(returns)
            
            # Risk metrics
            metrics['var_95'] = self._calculate_var(returns, 0.95)
            metrics['var_99'] = self._calculate_var(returns, 0.99)
            metrics['cvar_95'] = self._calculate_cvar(returns, 0.95)
            metrics['cvar_99'] = self._calculate_cvar(returns, 0.99)
            
            # Distribution metrics
            metrics['skewness'] = returns.skew()
            metrics['kurtosis'] = returns.kurtosis()
            
            # Trading metrics
            metrics['win_rate'] = self._calculate_win_rate(returns)
            metrics['profit_factor'] = self._calculate_profit_factor(returns)
            metrics['avg_win'] = returns[returns > 0].mean() if (returns > 0).any() else 0
            metrics['avg_loss'] = returns[returns < 0].mean() if (returns < 0).any() else 0
            
            # Benchmark comparison (if provided)
            if benchmark_returns is not None:
                metrics['beta'] = self._calculate_beta(returns, benchmark_returns)
                metrics['alpha'] = self._calculate_alpha(returns, benchmark_returns)
                metrics['information_ratio'] = self._calculate_information_ratio(returns, benchmark_returns)
                metrics['tracking_error'] = self._calculate_tracking_error(returns, benchmark_returns)
                metrics['up_capture'] = self._calculate_up_capture(returns, benchmark_returns)
                metrics['down_capture'] = self._calculate_down_capture(returns, benchmark_returns)
            
            return metrics
            
        except Exception as e:
            logger.error(f"Error calculating performance metrics: {e}")
            return self._get_default_metrics()
    
    def generate_performance_report(self, portfolio_returns: pd.Series,
                                  portfolio_id: str,
                                  benchmark_returns: Optional[pd.Series] = None) -> PerformanceReport:
        """Generate comprehensive performance report"""
        try:
            metrics = self.calculate_performance_metrics(portfolio_returns, benchmark_returns)
            
            return PerformanceReport(
                portfolio_id=portfolio_id,
                start_date=portfolio_returns.index[0],
                end_date=portfolio_returns.index[-1],
                total_return=metrics['total_return'],
                annualized_return=metrics['annualized_return'],
                volatility=metrics['volatility'],
                sharpe_ratio=metrics['sharpe_ratio'],
                sortino_ratio=metrics['sortino_ratio'],
                max_drawdown=metrics['max_drawdown'],
                var_95=metrics['var_95'],
                cvar_95=metrics['cvar_95'],
                beta=metrics.get('beta'),
                alpha=metrics.get('alpha'),
                win_rate=metrics['win_rate'],
                profit_factor=metrics['profit_factor'],
                calmar_ratio=metrics['calmar_ratio'],
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error generating performance report: {e}")
            return self._get_default_performance_report(portfolio_id)
    
    def generate_risk_report(self, portfolio_returns: pd.Series,
                           portfolio_weights: Dict[str, float],
                           portfolio_id: str) -> RiskReport:
        """Generate comprehensive risk report"""
        try:
            # Calculate risk metrics
            var_95 = self._calculate_var(portfolio_returns, 0.95)
            var_99 = self._calculate_var(portfolio_returns, 0.99)
            cvar_95 = self._calculate_cvar(portfolio_returns, 0.95)
            cvar_99 = self._calculate_cvar(portfolio_returns, 0.99)
            
            max_drawdown = self._calculate_max_drawdown(portfolio_returns)
            volatility = self._calculate_volatility(portfolio_returns)
            downside_deviation = self._calculate_downside_deviation(portfolio_returns)
            
            skewness = portfolio_returns.skew()
            kurtosis = portfolio_returns.kurtosis()
            tail_ratio = self._calculate_tail_ratio(portfolio_returns)
            
            # Portfolio-specific risks
            concentration_risk = self._calculate_concentration_risk(portfolio_weights)
            liquidity_risk = self._estimate_liquidity_risk(portfolio_weights)
            correlation_risk = self._estimate_correlation_risk(portfolio_returns)
            
            return RiskReport(
                portfolio_id=portfolio_id,
                analysis_date=datetime.now(),
                var_1d_95=var_95,
                var_1d_99=var_99,
                cvar_1d_95=cvar_95,
                cvar_1d_99=cvar_99,
                max_drawdown=max_drawdown,
                volatility=volatility,
                downside_deviation=downside_deviation,
                skewness=skewness,
                kurtosis=kurtosis,
                tail_ratio=tail_ratio,
                concentration_risk=concentration_risk,
                liquidity_risk=liquidity_risk,
                correlation_risk=correlation_risk
            )
            
        except Exception as e:
            logger.error(f"Error generating risk report: {e}")
            return self._get_default_risk_report(portfolio_id)
    
    def calculate_attribution(self, portfolio_returns: pd.DataFrame,
                            portfolio_weights: pd.DataFrame,
                            benchmark_returns: pd.DataFrame,
                            benchmark_weights: pd.DataFrame) -> AttributionResult:
        """Calculate performance attribution"""
        try:
            # Align data
            common_dates = portfolio_returns.index.intersection(benchmark_returns.index)
            portfolio_returns = portfolio_returns.loc[common_dates]
            benchmark_returns = benchmark_returns.loc[common_dates]
            
            # Calculate total returns
            portfolio_total = (portfolio_returns * portfolio_weights.shift(1)).sum(axis=1).sum()
            benchmark_total = (benchmark_returns * benchmark_weights.shift(1)).sum(axis=1).sum()
            active_return = portfolio_total - benchmark_total
            
            # Asset allocation effect
            asset_allocation_effect = {}
            for asset in portfolio_returns.columns:
                if asset in benchmark_returns.columns:
                    weight_diff = portfolio_weights[asset].mean() - benchmark_weights[asset].mean()
                    benchmark_asset_return = benchmark_returns[asset].sum()
                    asset_allocation_effect[asset] = weight_diff * benchmark_asset_return
            
            # Security selection effect
            security_selection_effect = {}
            for asset in portfolio_returns.columns:
                if asset in benchmark_returns.columns:
                    return_diff = portfolio_returns[asset].sum() - benchmark_returns[asset].sum()
                    benchmark_weight = benchmark_weights[asset].mean()
                    security_selection_effect[asset] = benchmark_weight * return_diff
            
            # Interaction effect
            interaction_effect = {}
            for asset in portfolio_returns.columns:
                if asset in benchmark_returns.columns:
                    weight_diff = portfolio_weights[asset].mean() - benchmark_weights[asset].mean()
                    return_diff = portfolio_returns[asset].sum() - benchmark_returns[asset].sum()
                    interaction_effect[asset] = weight_diff * return_diff
            
            total_attribution = (sum(asset_allocation_effect.values()) + 
                               sum(security_selection_effect.values()) + 
                               sum(interaction_effect.values()))
            
            return AttributionResult(
                total_return=portfolio_total,
                benchmark_return=benchmark_total,
                active_return=active_return,
                asset_allocation_effect=asset_allocation_effect,
                security_selection_effect=security_selection_effect,
                interaction_effect=interaction_effect,
                total_attribution=total_attribution
            )
            
        except Exception as e:
            logger.error(f"Error calculating attribution: {e}")
            return self._get_default_attribution_result()
    
    # Helper methods for metric calculations
    def _calculate_total_return(self, returns: pd.Series) -> float:
        """Calculate total return"""
        return (1 + returns).prod() - 1
    
    def _calculate_annualized_return(self, returns: pd.Series) -> float:
        """Calculate annualized return"""
        total_return = self._calculate_total_return(returns)
        years = len(returns) / self.trading_days_per_year
        return (1 + total_return) ** (1 / years) - 1 if years > 0 else 0
    
    def _calculate_volatility(self, returns: pd.Series) -> float:
        """Calculate annualized volatility"""
        return returns.std() * np.sqrt(self.trading_days_per_year)
    
    def _calculate_sharpe_ratio(self, returns: pd.Series) -> float:
        """Calculate Sharpe ratio"""
        excess_returns = returns - self.risk_free_rate / self.trading_days_per_year
        return excess_returns.mean() / excess_returns.std() * np.sqrt(self.trading_days_per_year)
    
    def _calculate_sortino_ratio(self, returns: pd.Series) -> float:
        """Calculate Sortino ratio"""
        excess_returns = returns - self.risk_free_rate / self.trading_days_per_year
        downside_deviation = self._calculate_downside_deviation(returns)
        return excess_returns.mean() / downside_deviation * np.sqrt(self.trading_days_per_year)
    
    def _calculate_calmar_ratio(self, returns: pd.Series) -> float:
        """Calculate Calmar ratio"""
        annualized_return = self._calculate_annualized_return(returns)
        max_drawdown = self._calculate_max_drawdown(returns)
        return annualized_return / abs(max_drawdown) if max_drawdown != 0 else 0
    
    def _calculate_max_drawdown(self, returns: pd.Series) -> float:
        """Calculate maximum drawdown"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()
    
    def _calculate_avg_drawdown(self, returns: pd.Series) -> float:
        """Calculate average drawdown"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown[drawdown < 0].mean() if (drawdown < 0).any() else 0
    
    def _calculate_drawdown_duration(self, returns: pd.Series) -> float:
        """Calculate average drawdown duration"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        
        # Find drawdown periods
        in_drawdown = drawdown < 0
        drawdown_periods = []
        start = None
        
        for i, is_dd in enumerate(in_drawdown):
            if is_dd and start is None:
                start = i
            elif not is_dd and start is not None:
                drawdown_periods.append(i - start)
                start = None
        
        return np.mean(drawdown_periods) if drawdown_periods else 0
    
    def _calculate_var(self, returns: pd.Series, confidence_level: float) -> float:
        """Calculate Value at Risk"""
        return -np.percentile(returns, (1 - confidence_level) * 100)
    
    def _calculate_cvar(self, returns: pd.Series, confidence_level: float) -> float:
        """Calculate Conditional Value at Risk"""
        var = self._calculate_var(returns, confidence_level)
        return -returns[returns <= -var].mean() if (returns <= -var).any() else 0
    
    def _calculate_downside_deviation(self, returns: pd.Series, target: float = 0) -> float:
        """Calculate downside deviation"""
        downside_returns = returns[returns < target] - target
        return np.sqrt((downside_returns ** 2).mean()) if len(downside_returns) > 0 else 0
    
    def _calculate_win_rate(self, returns: pd.Series) -> float:
        """Calculate win rate"""
        return (returns > 0).mean()
    
    def _calculate_profit_factor(self, returns: pd.Series) -> float:
        """Calculate profit factor"""
        profits = returns[returns > 0].sum()
        losses = abs(returns[returns < 0].sum())
        return profits / losses if losses != 0 else float('inf')
    
    def _calculate_tail_ratio(self, returns: pd.Series) -> float:
        """Calculate tail ratio (95th percentile / 5th percentile)"""
        return np.percentile(returns, 95) / abs(np.percentile(returns, 5))
    
    def _calculate_beta(self, returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """Calculate beta"""
        aligned_data = pd.concat([returns, benchmark_returns], axis=1).dropna()
        if len(aligned_data) < 2:
            return 1.0
        covariance = aligned_data.cov().iloc[0, 1]
        benchmark_variance = aligned_data.iloc[:, 1].var()
        return covariance / benchmark_variance if benchmark_variance != 0 else 1.0
    
    def _calculate_alpha(self, returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """Calculate alpha"""
        beta = self._calculate_beta(returns, benchmark_returns)
        portfolio_return = self._calculate_annualized_return(returns)
        benchmark_return = self._calculate_annualized_return(benchmark_returns)
        return portfolio_return - (self.risk_free_rate + beta * (benchmark_return - self.risk_free_rate))
    
    def _calculate_information_ratio(self, returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """Calculate information ratio"""
        active_returns = returns - benchmark_returns
        tracking_error = self._calculate_tracking_error(returns, benchmark_returns)
        return active_returns.mean() / tracking_error * np.sqrt(self.trading_days_per_year) if tracking_error != 0 else 0
    
    def _calculate_tracking_error(self, returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """Calculate tracking error"""
        active_returns = returns - benchmark_returns
        return active_returns.std() * np.sqrt(self.trading_days_per_year)
    
    def _calculate_up_capture(self, returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """Calculate up capture ratio"""
        up_periods = benchmark_returns > 0
        if not up_periods.any():
            return 1.0
        portfolio_up = returns[up_periods].mean()
        benchmark_up = benchmark_returns[up_periods].mean()
        return portfolio_up / benchmark_up if benchmark_up != 0 else 1.0
    
    def _calculate_down_capture(self, returns: pd.Series, benchmark_returns: pd.Series) -> float:
        """Calculate down capture ratio"""
        down_periods = benchmark_returns < 0
        if not down_periods.any():
            return 1.0
        portfolio_down = returns[down_periods].mean()
        benchmark_down = benchmark_returns[down_periods].mean()
        return portfolio_down / benchmark_down if benchmark_down != 0 else 1.0
    
    def _calculate_concentration_risk(self, weights: Dict[str, float]) -> float:
        """Calculate concentration risk using Herfindahl index"""
        weight_values = list(weights.values())
        return sum(w**2 for w in weight_values)
    
    def _estimate_liquidity_risk(self, weights: Dict[str, float]) -> float:
        """Estimate liquidity risk (simplified)"""
        # This is a simplified estimation - in practice would use actual liquidity data
        return max(weights.values()) if weights else 0.0
    
    def _estimate_correlation_risk(self, returns: pd.Series) -> float:
        """Estimate correlation risk (simplified)"""
        # This is a simplified estimation - in practice would analyze correlation matrix
        return returns.std() * 0.5  # Placeholder calculation
    
    # Default result methods
    def _get_default_metrics(self) -> Dict[str, float]:
        """Get default metrics when calculation fails"""
        return {
            'total_return': 0.0,
            'annualized_return': 0.0,
            'volatility': 0.15,
            'sharpe_ratio': 0.0,
            'sortino_ratio': 0.0,
            'calmar_ratio': 0.0,
            'max_drawdown': -0.05,
            'var_95': 0.02,
            'cvar_95': 0.03,
            'win_rate': 0.5,
            'profit_factor': 1.0,
            'skewness': 0.0,
            'kurtosis': 0.0
        }
    
    def _get_default_performance_report(self, portfolio_id: str) -> PerformanceReport:
        """Get default performance report when calculation fails"""
        return PerformanceReport(
            portfolio_id=portfolio_id,
            start_date=datetime.now() - timedelta(days=365),
            end_date=datetime.now(),
            total_return=0.0,
            annualized_return=0.0,
            volatility=0.15,
            sharpe_ratio=0.0,
            sortino_ratio=0.0,
            max_drawdown=-0.05,
            var_95=0.02,
            cvar_95=0.03,
            beta=1.0,
            alpha=0.0,
            win_rate=0.5,
            profit_factor=1.0,
            calmar_ratio=0.0,
            timestamp=datetime.now()
        )
    
    def _get_default_risk_report(self, portfolio_id: str) -> RiskReport:
        """Get default risk report when calculation fails"""
        return RiskReport(
            portfolio_id=portfolio_id,
            analysis_date=datetime.now(),
            var_1d_95=0.02,
            var_1d_99=0.03,
            cvar_1d_95=0.025,
            cvar_1d_99=0.035,
            max_drawdown=-0.05,
            volatility=0.15,
            downside_deviation=0.10,
            skewness=0.0,
            kurtosis=0.0,
            tail_ratio=1.0,
            concentration_risk=0.2,
            liquidity_risk=0.1,
            correlation_risk=0.15
        )
    
    def _get_default_attribution_result(self) -> AttributionResult:
        """Get default attribution result when calculation fails"""
        return AttributionResult(
            total_return=0.0,
            benchmark_return=0.0,
            active_return=0.0,
            asset_allocation_effect={},
            security_selection_effect={},
            interaction_effect={},
            total_attribution=0.0
        )