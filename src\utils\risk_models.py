"""Risk models and calculators for the Noryon V2 trading system"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass
from enum import Enum
from datetime import datetime, timedelta
import logging
from scipy import stats
from scipy.optimize import minimize

logger = logging.getLogger(__name__)


class RiskModelType(Enum):
    """Types of risk models"""
    PARAMETRIC = "parametric"
    HISTORICAL = "historical"
    MONTE_CARLO = "monte_carlo"
    EXTREME_VALUE = "extreme_value"


class StressTestType(Enum):
    """Types of stress tests"""
    HISTORICAL_SCENARIO = "historical_scenario"
    HYPOTHETICAL_SCENARIO = "hypothetical_scenario"
    SENSITIVITY_ANALYSIS = "sensitivity_analysis"
    WORST_CASE = "worst_case"
    CORRELATION_BREAKDOWN = "correlation_breakdown"


@dataclass
class VaRResult:
    """Value at Risk calculation result"""
    var_value: float
    confidence_level: float
    time_horizon: int
    model_type: RiskModelType
    portfolio_value: float
    timestamp: datetime
    components: Dict[str, float]
    

@dataclass
class StressTestResult:
    """Stress test result"""
    test_id: str
    test_type: StressTestType
    scenario_name: str
    portfolio_pnl: float
    individual_pnl: Dict[str, float]
    risk_metrics: Dict[str, float]
    timestamp: datetime
    description: str


@dataclass
class CorrelationResult:
    """Correlation analysis result"""
    correlation_matrix: pd.DataFrame
    eigenvalues: List[float]
    eigenvectors: np.ndarray
    concentration_ratio: float
    effective_assets: int
    timestamp: datetime


class VaRCalculator:
    """Value at Risk calculator with multiple methodologies"""
    
    def __init__(self, confidence_levels: List[float] = None):
        self.confidence_levels = confidence_levels or [0.95, 0.99, 0.999]
        self.historical_window = 252  # Trading days
        
    def calculate_parametric_var(self, returns: pd.Series, 
                                confidence_level: float = 0.95,
                                time_horizon: int = 1) -> VaRResult:
        """Calculate parametric VaR assuming normal distribution"""
        try:
            mean_return = returns.mean()
            std_return = returns.std()
            
            # Adjust for time horizon
            horizon_mean = mean_return * time_horizon
            horizon_std = std_return * np.sqrt(time_horizon)
            
            # Calculate VaR
            z_score = stats.norm.ppf(1 - confidence_level)
            var_value = -(horizon_mean + z_score * horizon_std)
            
            return VaRResult(
                var_value=var_value,
                confidence_level=confidence_level,
                time_horizon=time_horizon,
                model_type=RiskModelType.PARAMETRIC,
                portfolio_value=1.0,  # Normalized
                timestamp=datetime.now(),
                components={'mean': horizon_mean, 'volatility': horizon_std}
            )
            
        except Exception as e:
            logger.error(f"Error calculating parametric VaR: {e}")
            return self._create_default_var_result(confidence_level, time_horizon)
    
    def calculate_historical_var(self, returns: pd.Series,
                                confidence_level: float = 0.95,
                                time_horizon: int = 1) -> VaRResult:
        """Calculate historical VaR using empirical distribution"""
        try:
            # Adjust returns for time horizon if needed
            if time_horizon > 1:
                # Aggregate returns over time horizon
                aggregated_returns = []
                for i in range(len(returns) - time_horizon + 1):
                    period_return = returns.iloc[i:i+time_horizon].sum()
                    aggregated_returns.append(period_return)
                returns_to_use = pd.Series(aggregated_returns)
            else:
                returns_to_use = returns
            
            # Calculate VaR as percentile
            var_percentile = (1 - confidence_level) * 100
            var_value = -np.percentile(returns_to_use, var_percentile)
            
            return VaRResult(
                var_value=var_value,
                confidence_level=confidence_level,
                time_horizon=time_horizon,
                model_type=RiskModelType.HISTORICAL,
                portfolio_value=1.0,
                timestamp=datetime.now(),
                components={'observations': len(returns_to_use)}
            )
            
        except Exception as e:
            logger.error(f"Error calculating historical VaR: {e}")
            return self._create_default_var_result(confidence_level, time_horizon)
    
    def calculate_monte_carlo_var(self, returns: pd.Series,
                                 confidence_level: float = 0.95,
                                 time_horizon: int = 1,
                                 num_simulations: int = 10000) -> VaRResult:
        """Calculate Monte Carlo VaR using simulated returns"""
        try:
            mean_return = returns.mean()
            std_return = returns.std()
            
            # Generate random scenarios
            np.random.seed(42)  # For reproducibility
            simulated_returns = np.random.normal(
                mean_return * time_horizon,
                std_return * np.sqrt(time_horizon),
                num_simulations
            )
            
            # Calculate VaR
            var_percentile = (1 - confidence_level) * 100
            var_value = -np.percentile(simulated_returns, var_percentile)
            
            return VaRResult(
                var_value=var_value,
                confidence_level=confidence_level,
                time_horizon=time_horizon,
                model_type=RiskModelType.MONTE_CARLO,
                portfolio_value=1.0,
                timestamp=datetime.now(),
                components={'simulations': num_simulations}
            )
            
        except Exception as e:
            logger.error(f"Error calculating Monte Carlo VaR: {e}")
            return self._create_default_var_result(confidence_level, time_horizon)
    
    def calculate_portfolio_var(self, returns_matrix: pd.DataFrame,
                               weights: np.ndarray,
                               confidence_level: float = 0.95,
                               method: RiskModelType = RiskModelType.PARAMETRIC) -> VaRResult:
        """Calculate portfolio VaR considering correlations"""
        try:
            # Calculate portfolio returns
            portfolio_returns = (returns_matrix * weights).sum(axis=1)
            
            # Use appropriate method
            if method == RiskModelType.PARAMETRIC:
                return self.calculate_parametric_var(portfolio_returns, confidence_level)
            elif method == RiskModelType.HISTORICAL:
                return self.calculate_historical_var(portfolio_returns, confidence_level)
            elif method == RiskModelType.MONTE_CARLO:
                return self.calculate_monte_carlo_var(portfolio_returns, confidence_level)
            else:
                return self.calculate_parametric_var(portfolio_returns, confidence_level)
                
        except Exception as e:
            logger.error(f"Error calculating portfolio VaR: {e}")
            return self._create_default_var_result(confidence_level, 1)
    
    def calculate_component_var(self, returns_matrix: pd.DataFrame,
                               weights: np.ndarray,
                               confidence_level: float = 0.95) -> Dict[str, float]:
        """Calculate component VaR for each asset"""
        try:
            portfolio_var = self.calculate_portfolio_var(
                returns_matrix, weights, confidence_level
            ).var_value
            
            component_vars = {}
            
            for i, asset in enumerate(returns_matrix.columns):
                # Calculate marginal VaR by bumping weight slightly
                epsilon = 0.001
                bumped_weights = weights.copy()
                bumped_weights[i] += epsilon
                bumped_weights = bumped_weights / bumped_weights.sum()  # Renormalize
                
                bumped_var = self.calculate_portfolio_var(
                    returns_matrix, bumped_weights, confidence_level
                ).var_value
                
                marginal_var = (bumped_var - portfolio_var) / epsilon
                component_var = marginal_var * weights[i]
                component_vars[asset] = component_var
            
            return component_vars
            
        except Exception as e:
            logger.error(f"Error calculating component VaR: {e}")
            return {}
    
    def _create_default_var_result(self, confidence_level: float, 
                                  time_horizon: int) -> VaRResult:
        """Create a default VaR result when calculation fails"""
        return VaRResult(
            var_value=0.05,  # 5% default
            confidence_level=confidence_level,
            time_horizon=time_horizon,
            model_type=RiskModelType.PARAMETRIC,
            portfolio_value=1.0,
            timestamp=datetime.now(),
            components={'default': True}
        )


class StressTestEngine:
    """Stress testing engine for portfolio risk assessment"""
    
    def __init__(self):
        self.historical_scenarios = self._load_historical_scenarios()
        
    def _load_historical_scenarios(self) -> Dict[str, Dict[str, float]]:
        """Load predefined historical stress scenarios"""
        return {
            'black_monday_1987': {
                'equity_shock': -0.22,
                'bond_shock': 0.02,
                'fx_shock': 0.05,
                'commodity_shock': -0.10
            },
            'dotcom_crash_2000': {
                'equity_shock': -0.15,
                'bond_shock': 0.03,
                'fx_shock': 0.02,
                'commodity_shock': -0.05
            },
            'financial_crisis_2008': {
                'equity_shock': -0.20,
                'bond_shock': 0.05,
                'fx_shock': 0.10,
                'commodity_shock': -0.15
            },
            'covid_crash_2020': {
                'equity_shock': -0.12,
                'bond_shock': 0.02,
                'fx_shock': 0.08,
                'commodity_shock': -0.20
            }
        }
    
    def run_historical_scenario(self, portfolio_data: pd.DataFrame,
                               weights: np.ndarray,
                               scenario_name: str) -> StressTestResult:
        """Run a historical stress scenario"""
        try:
            if scenario_name not in self.historical_scenarios:
                raise ValueError(f"Unknown scenario: {scenario_name}")
            
            scenario = self.historical_scenarios[scenario_name]
            
            # Apply shocks to portfolio
            shocked_returns = self._apply_shocks(portfolio_data, scenario)
            portfolio_pnl = (shocked_returns * weights).sum()
            
            # Calculate individual asset P&L
            individual_pnl = {}
            for i, asset in enumerate(portfolio_data.columns):
                individual_pnl[asset] = shocked_returns.iloc[i] * weights[i]
            
            # Calculate risk metrics
            risk_metrics = self._calculate_stress_metrics(shocked_returns, weights)
            
            return StressTestResult(
                test_id=f"hist_{scenario_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                test_type=StressTestType.HISTORICAL_SCENARIO,
                scenario_name=scenario_name,
                portfolio_pnl=portfolio_pnl,
                individual_pnl=individual_pnl,
                risk_metrics=risk_metrics,
                timestamp=datetime.now(),
                description=f"Historical scenario: {scenario_name}"
            )
            
        except Exception as e:
            logger.error(f"Error running historical scenario: {e}")
            return self._create_default_stress_result(scenario_name)
    
    def run_sensitivity_analysis(self, portfolio_data: pd.DataFrame,
                                weights: np.ndarray,
                                shock_ranges: Dict[str, List[float]]) -> List[StressTestResult]:
        """Run sensitivity analysis with various shock levels"""
        results = []
        
        try:
            for factor, shock_levels in shock_ranges.items():
                for shock in shock_levels:
                    scenario = {factor: shock}
                    
                    shocked_returns = self._apply_shocks(portfolio_data, scenario)
                    portfolio_pnl = (shocked_returns * weights).sum()
                    
                    individual_pnl = {}
                    for i, asset in enumerate(portfolio_data.columns):
                        individual_pnl[asset] = shocked_returns.iloc[i] * weights[i]
                    
                    risk_metrics = self._calculate_stress_metrics(shocked_returns, weights)
                    
                    result = StressTestResult(
                        test_id=f"sens_{factor}_{shock}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                        test_type=StressTestType.SENSITIVITY_ANALYSIS,
                        scenario_name=f"{factor}_shock_{shock}",
                        portfolio_pnl=portfolio_pnl,
                        individual_pnl=individual_pnl,
                        risk_metrics=risk_metrics,
                        timestamp=datetime.now(),
                        description=f"Sensitivity test: {factor} shock of {shock}"
                    )
                    results.append(result)
            
            return results
            
        except Exception as e:
            logger.error(f"Error running sensitivity analysis: {e}")
            return [self._create_default_stress_result("sensitivity_analysis")]
    
    def run_worst_case_scenario(self, portfolio_data: pd.DataFrame,
                               weights: np.ndarray,
                               confidence_level: float = 0.99) -> StressTestResult:
        """Run worst-case scenario analysis"""
        try:
            # Calculate historical worst returns for each asset
            worst_returns = portfolio_data.quantile(1 - confidence_level)
            
            portfolio_pnl = (worst_returns * weights).sum()
            
            individual_pnl = {}
            for i, asset in enumerate(portfolio_data.columns):
                individual_pnl[asset] = worst_returns.iloc[i] * weights[i]
            
            risk_metrics = self._calculate_stress_metrics(worst_returns, weights)
            
            return StressTestResult(
                test_id=f"worst_case_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                test_type=StressTestType.WORST_CASE,
                scenario_name=f"worst_case_{confidence_level}",
                portfolio_pnl=portfolio_pnl,
                individual_pnl=individual_pnl,
                risk_metrics=risk_metrics,
                timestamp=datetime.now(),
                description=f"Worst case scenario at {confidence_level} confidence"
            )
            
        except Exception as e:
            logger.error(f"Error running worst case scenario: {e}")
            return self._create_default_stress_result("worst_case")
    
    def _apply_shocks(self, portfolio_data: pd.DataFrame, 
                     scenario: Dict[str, float]) -> pd.Series:
        """Apply shocks to portfolio data"""
        shocked_returns = portfolio_data.mean().copy()
        
        # Simple mapping of shock types to assets
        # In practice, this would be more sophisticated
        for shock_type, shock_value in scenario.items():
            if 'equity' in shock_type:
                # Apply to equity-like assets
                equity_assets = [col for col in portfolio_data.columns 
                               if any(term in col.lower() for term in ['stock', 'equity', 'spy', 'qqq'])]
                for asset in equity_assets:
                    shocked_returns[asset] += shock_value
            elif 'bond' in shock_type:
                # Apply to bond-like assets
                bond_assets = [col for col in portfolio_data.columns 
                             if any(term in col.lower() for term in ['bond', 'treasury', 'tlt'])]
                for asset in bond_assets:
                    shocked_returns[asset] += shock_value
            # Add more shock type mappings as needed
        
        return shocked_returns
    
    def _calculate_stress_metrics(self, returns: pd.Series, 
                                 weights: np.ndarray) -> Dict[str, float]:
        """Calculate risk metrics for stress test"""
        try:
            portfolio_return = (returns * weights).sum()
            
            return {
                'portfolio_return': portfolio_return,
                'max_individual_loss': returns.min(),
                'concentration_risk': max(weights) / sum(weights),
                'diversification_ratio': len([w for w in weights if w > 0.01])
            }
        except Exception as e:
            logger.error(f"Error calculating stress metrics: {e}")
            return {'portfolio_return': 0.0}
    
    def _create_default_stress_result(self, scenario_name: str) -> StressTestResult:
        """Create default stress test result when calculation fails"""
        return StressTestResult(
            test_id=f"default_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            test_type=StressTestType.HYPOTHETICAL_SCENARIO,
            scenario_name=scenario_name,
            portfolio_pnl=-0.05,  # Default 5% loss
            individual_pnl={},
            risk_metrics={'default': True},
            timestamp=datetime.now(),
            description="Default stress test result"
        )


class CorrelationAnalyzer:
    """Correlation analysis and monitoring"""
    
    def __init__(self, lookback_window: int = 252):
        self.lookback_window = lookback_window
        
    def calculate_correlation_matrix(self, returns_data: pd.DataFrame) -> CorrelationResult:
        """Calculate correlation matrix and related metrics"""
        try:
            # Calculate correlation matrix
            corr_matrix = returns_data.corr()
            
            # Eigenvalue decomposition
            eigenvalues, eigenvectors = np.linalg.eigh(corr_matrix.values)
            eigenvalues = eigenvalues[::-1]  # Sort in descending order
            eigenvectors = eigenvectors[:, ::-1]
            
            # Calculate concentration metrics
            concentration_ratio = eigenvalues[0] / sum(eigenvalues)
            effective_assets = 1 / sum((eigenvalues / sum(eigenvalues))**2)
            
            return CorrelationResult(
                correlation_matrix=corr_matrix,
                eigenvalues=eigenvalues.tolist(),
                eigenvectors=eigenvectors,
                concentration_ratio=concentration_ratio,
                effective_assets=int(effective_assets),
                timestamp=datetime.now()
            )
            
        except Exception as e:
            logger.error(f"Error calculating correlation matrix: {e}")
            return self._create_default_correlation_result(returns_data.columns)
    
    def detect_correlation_regime_change(self, returns_data: pd.DataFrame,
                                        window_size: int = 60) -> Dict[str, Any]:
        """Detect changes in correlation regime"""
        try:
            correlations_over_time = []
            dates = []
            
            for i in range(window_size, len(returns_data)):
                window_data = returns_data.iloc[i-window_size:i]
                corr_matrix = window_data.corr()
                
                # Calculate average correlation (excluding diagonal)
                mask = np.triu(np.ones_like(corr_matrix.values, dtype=bool), k=1)
                avg_correlation = corr_matrix.values[mask].mean()
                
                correlations_over_time.append(avg_correlation)
                dates.append(returns_data.index[i])
            
            # Detect regime changes using rolling statistics
            corr_series = pd.Series(correlations_over_time, index=dates)
            rolling_mean = corr_series.rolling(window=20).mean()
            rolling_std = corr_series.rolling(window=20).std()
            
            # Identify regime changes (simplified)
            regime_changes = []
            for i in range(1, len(corr_series)):
                if abs(corr_series.iloc[i] - rolling_mean.iloc[i]) > 2 * rolling_std.iloc[i]:
                    regime_changes.append({
                        'date': dates[i],
                        'correlation': corr_series.iloc[i],
                        'deviation': abs(corr_series.iloc[i] - rolling_mean.iloc[i]) / rolling_std.iloc[i]
                    })
            
            return {
                'correlation_time_series': corr_series,
                'regime_changes': regime_changes,
                'current_correlation': correlations_over_time[-1] if correlations_over_time else 0.0,
                'correlation_trend': 'increasing' if len(correlations_over_time) > 1 and 
                                   correlations_over_time[-1] > correlations_over_time[-10] else 'decreasing'
            }
            
        except Exception as e:
            logger.error(f"Error detecting correlation regime change: {e}")
            return {'correlation_time_series': pd.Series(), 'regime_changes': []}
    
    def calculate_tail_dependence(self, returns_data: pd.DataFrame,
                                 threshold: float = 0.05) -> Dict[str, float]:
        """Calculate tail dependence between assets"""
        try:
            tail_dependencies = {}
            
            for i, asset1 in enumerate(returns_data.columns):
                for j, asset2 in enumerate(returns_data.columns[i+1:], i+1):
                    # Lower tail dependence
                    asset1_data = returns_data[asset1]
                    asset2_data = returns_data[asset2]
                    
                    # Find threshold values
                    threshold1 = asset1_data.quantile(threshold)
                    threshold2 = asset2_data.quantile(threshold)
                    
                    # Count joint exceedances
                    joint_exceedances = ((asset1_data <= threshold1) & 
                                        (asset2_data <= threshold2)).sum()
                    individual_exceedances = (asset1_data <= threshold1).sum()
                    
                    if individual_exceedances > 0:
                        tail_dep = joint_exceedances / individual_exceedances
                    else:
                        tail_dep = 0.0
                    
                    tail_dependencies[f"{asset1}_{asset2}"] = tail_dep
            
            return tail_dependencies
            
        except Exception as e:
            logger.error(f"Error calculating tail dependence: {e}")
            return {}
    
    def _create_default_correlation_result(self, columns) -> CorrelationResult:
        """Create default correlation result when calculation fails"""
        n_assets = len(columns)
        identity_matrix = pd.DataFrame(np.eye(n_assets), 
                                     index=columns, columns=columns)
        
        return CorrelationResult(
            correlation_matrix=identity_matrix,
            eigenvalues=[1.0] * n_assets,
            eigenvectors=np.eye(n_assets),
            concentration_ratio=1.0 / n_assets,
            effective_assets=n_assets,
            timestamp=datetime.now()
        )