#!/usr/bin/env python3
"""
Advanced Sentiment Analyzer
Provides sentiment analysis capabilities for news and market data.
"""

import re
from typing import Dict, List, Tuple, Optional
from enum import Enum
from dataclasses import dataclass
from textblob import TextBlob
import logging

logger = logging.getLogger(__name__)

class SentimentPolarity(Enum):
    """Sentiment polarity levels."""
    VERY_NEGATIVE = -2
    NEGATIVE = -1
    NEUTRAL = 0
    POSITIVE = 1
    VERY_POSITIVE = 2

@dataclass
class SentimentResult:
    """Result of sentiment analysis."""
    polarity: float  # -1.0 to 1.0
    subjectivity: float  # 0.0 to 1.0
    confidence: float  # 0.0 to 1.0
    sentiment_label: SentimentPolarity
    keywords: List[str]
    market_relevance: float  # 0.0 to 1.0

class AdvancedSentimentAnalyzer:
    """Advanced sentiment analyzer with market-specific enhancements."""
    
    def __init__(self):
        # Market-specific positive keywords
        self.positive_keywords = {
            'bullish', 'rally', 'surge', 'gains', 'profit', 'growth', 'boom',
            'uptrend', 'breakout', 'momentum', 'strong', 'outperform', 'beat',
            'exceed', 'record', 'high', 'upgrade', 'buy', 'optimistic',
            'confident', 'positive', 'recovery', 'expansion', 'increase'
        }
        
        # Market-specific negative keywords
        self.negative_keywords = {
            'bearish', 'crash', 'plunge', 'losses', 'decline', 'recession',
            'downtrend', 'breakdown', 'weak', 'underperform', 'miss', 'fall',
            'drop', 'low', 'downgrade', 'sell', 'pessimistic', 'concern',
            'negative', 'crisis', 'contraction', 'decrease', 'volatility',
            'uncertainty', 'risk', 'fear'
        }
        
        # Market relevance keywords
        self.market_keywords = {
            'stock', 'market', 'trading', 'price', 'volume', 'earnings',
            'revenue', 'profit', 'loss', 'dividend', 'share', 'equity',
            'bond', 'commodity', 'currency', 'forex', 'crypto', 'bitcoin',
            'ethereum', 'fed', 'interest', 'rate', 'inflation', 'gdp',
            'economic', 'financial', 'investment', 'portfolio', 'fund'
        }
        
        # Intensity modifiers
        self.intensity_modifiers = {
            'very': 1.5, 'extremely': 2.0, 'highly': 1.3, 'significantly': 1.4,
            'substantially': 1.4, 'dramatically': 1.8, 'sharply': 1.6,
            'slightly': 0.7, 'somewhat': 0.8, 'moderately': 0.9
        }
    
    def analyze_text(self, text: str) -> SentimentResult:
        """Analyze sentiment of given text."""
        try:
            # Clean and preprocess text
            cleaned_text = self._preprocess_text(text)
            
            # Basic sentiment analysis using TextBlob
            blob = TextBlob(cleaned_text)
            base_polarity = blob.sentiment.polarity
            subjectivity = blob.sentiment.subjectivity
            
            # Enhanced analysis with market-specific adjustments
            market_adjustment = self._calculate_market_adjustment(cleaned_text)
            intensity_adjustment = self._calculate_intensity_adjustment(cleaned_text)
            
            # Calculate final polarity
            adjusted_polarity = base_polarity + market_adjustment
            adjusted_polarity = max(-1.0, min(1.0, adjusted_polarity * intensity_adjustment))
            
            # Extract keywords
            keywords = self._extract_keywords(cleaned_text)
            
            # Calculate market relevance
            market_relevance = self._calculate_market_relevance(cleaned_text)
            
            # Calculate confidence
            confidence = self._calculate_confidence(adjusted_polarity, subjectivity, market_relevance)
            
            # Determine sentiment label
            sentiment_label = self._get_sentiment_label(adjusted_polarity)
            
            return SentimentResult(
                polarity=adjusted_polarity,
                subjectivity=subjectivity,
                confidence=confidence,
                sentiment_label=sentiment_label,
                keywords=keywords,
                market_relevance=market_relevance
            )
            
        except Exception as e:
            logger.error(f"Error analyzing sentiment: {e}")
            return SentimentResult(
                polarity=0.0,
                subjectivity=0.0,
                confidence=0.0,
                sentiment_label=SentimentPolarity.NEUTRAL,
                keywords=[],
                market_relevance=0.0
            )
    
    def _preprocess_text(self, text: str) -> str:
        """Clean and preprocess text for analysis."""
        # Convert to lowercase
        text = text.lower()
        
        # Remove URLs
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # Remove special characters but keep spaces and basic punctuation
        text = re.sub(r'[^a-zA-Z0-9\s.,!?-]', '', text)
        
        # Remove extra whitespace
        text = re.sub(r'\s+', ' ', text).strip()
        
        return text
    
    def _calculate_market_adjustment(self, text: str) -> float:
        """Calculate sentiment adjustment based on market-specific keywords."""
        words = text.split()
        adjustment = 0.0
        
        for word in words:
            if word in self.positive_keywords:
                adjustment += 0.1
            elif word in self.negative_keywords:
                adjustment -= 0.1
        
        return max(-0.5, min(0.5, adjustment))
    
    def _calculate_intensity_adjustment(self, text: str) -> float:
        """Calculate intensity adjustment based on modifier words."""
        words = text.split()
        max_intensity = 1.0
        
        for word in words:
            if word in self.intensity_modifiers:
                max_intensity = max(max_intensity, self.intensity_modifiers[word])
        
        return max_intensity
    
    def _extract_keywords(self, text: str) -> List[str]:
        """Extract relevant keywords from text."""
        words = text.split()
        keywords = []
        
        # Extract market-relevant keywords
        for word in words:
            if (word in self.market_keywords or 
                word in self.positive_keywords or 
                word in self.negative_keywords):
                keywords.append(word)
        
        return list(set(keywords))  # Remove duplicates
    
    def _calculate_market_relevance(self, text: str) -> float:
        """Calculate how relevant the text is to market/financial topics."""
        words = set(text.split())
        market_word_count = len(words.intersection(self.market_keywords))
        total_words = len(words)
        
        if total_words == 0:
            return 0.0
        
        relevance = market_word_count / total_words
        return min(1.0, relevance * 5)  # Scale up relevance
    
    def _calculate_confidence(self, polarity: float, subjectivity: float, market_relevance: float) -> float:
        """Calculate confidence score for the sentiment analysis."""
        # Higher confidence for:
        # - Strong polarity (away from neutral)
        # - Lower subjectivity (more objective)
        # - Higher market relevance
        
        polarity_confidence = abs(polarity)
        objectivity_confidence = 1.0 - subjectivity
        relevance_confidence = market_relevance
        
        # Weighted average
        confidence = (polarity_confidence * 0.4 + 
                     objectivity_confidence * 0.3 + 
                     relevance_confidence * 0.3)
        
        return min(1.0, confidence)
    
    def _get_sentiment_label(self, polarity: float) -> SentimentPolarity:
        """Convert polarity score to sentiment label."""
        if polarity <= -0.6:
            return SentimentPolarity.VERY_NEGATIVE
        elif polarity <= -0.2:
            return SentimentPolarity.NEGATIVE
        elif polarity >= 0.6:
            return SentimentPolarity.VERY_POSITIVE
        elif polarity >= 0.2:
            return SentimentPolarity.POSITIVE
        else:
            return SentimentPolarity.NEUTRAL
    
    def analyze_batch(self, texts: List[str]) -> List[SentimentResult]:
        """Analyze sentiment for multiple texts."""
        return [self.analyze_text(text) for text in texts]
    
    def get_overall_sentiment(self, results: List[SentimentResult]) -> SentimentResult:
        """Calculate overall sentiment from multiple results."""
        if not results:
            return SentimentResult(
                polarity=0.0,
                subjectivity=0.0,
                confidence=0.0,
                sentiment_label=SentimentPolarity.NEUTRAL,
                keywords=[],
                market_relevance=0.0
            )
        
        # Weighted average based on confidence
        total_weight = sum(r.confidence for r in results)
        if total_weight == 0:
            total_weight = len(results)
        
        avg_polarity = sum(r.polarity * r.confidence for r in results) / total_weight
        avg_subjectivity = sum(r.subjectivity * r.confidence for r in results) / total_weight
        avg_confidence = sum(r.confidence for r in results) / len(results)
        avg_market_relevance = sum(r.market_relevance for r in results) / len(results)
        
        # Combine all keywords
        all_keywords = []
        for result in results:
            all_keywords.extend(result.keywords)
        unique_keywords = list(set(all_keywords))
        
        return SentimentResult(
            polarity=avg_polarity,
            subjectivity=avg_subjectivity,
            confidence=avg_confidence,
            sentiment_label=self._get_sentiment_label(avg_polarity),
            keywords=unique_keywords,
            market_relevance=avg_market_relevance
        )