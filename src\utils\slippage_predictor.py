"""Slippage prediction for the Noryon V2 trading system"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta
import logging
from collections import deque
import math

logger = logging.getLogger(__name__)


class SlippageModel(Enum):
    """Slippage prediction models"""
    LINEAR = "linear"
    SQUARE_ROOT = "square_root"
    LOGARITHMIC = "logarithmic"
    MACHINE_LEARNING = "machine_learning"
    HYBRID = "hybrid"


class MarketCondition(Enum):
    """Market conditions affecting slippage"""
    NORMAL = "normal"
    VOLATILE = "volatile"
    ILLIQUID = "illiquid"
    STRESSED = "stressed"
    OPENING = "opening"
    CLOSING = "closing"


class OrderType(Enum):
    """Order types for slippage prediction"""
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"
    ICEBERG = "iceberg"
    TWAP = "twap"
    VWAP = "vwap"


@dataclass
class SlippageFactors:
    """Factors affecting slippage"""
    order_size: float
    average_daily_volume: float
    bid_ask_spread: float
    market_volatility: float
    time_of_day: float  # 0-1, where 0 is market open
    market_impact: float
    liquidity_score: float
    momentum: float
    order_book_depth: float
    recent_volume: float


@dataclass
class SlippagePrediction:
    """Slippage prediction result"""
    symbol: str
    timestamp: datetime
    predicted_slippage: float  # Basis points
    confidence: float  # 0-1
    model_used: SlippageModel
    market_condition: MarketCondition
    factors: SlippageFactors
    min_slippage: float  # Lower bound
    max_slippage: float  # Upper bound
    expected_cost: float  # Dollar amount
    recommendations: List[str]


@dataclass
class HistoricalSlippage:
    """Historical slippage data point"""
    timestamp: datetime
    symbol: str
    order_size: float
    actual_slippage: float
    predicted_slippage: float
    market_condition: MarketCondition
    factors: SlippageFactors


@dataclass
class SlippageModelPerformance:
    """Model performance metrics"""
    model: SlippageModel
    mae: float  # Mean Absolute Error
    rmse: float  # Root Mean Square Error
    mape: float  # Mean Absolute Percentage Error
    accuracy_within_5bp: float  # Percentage of predictions within 5bp
    accuracy_within_10bp: float  # Percentage of predictions within 10bp
    bias: float  # Average prediction error
    r_squared: float  # Coefficient of determination
    sample_size: int


class SlippagePredictor:
    """Advanced slippage prediction system"""
    
    def __init__(self, lookback_days: int = 30):
        self.lookback_days = lookback_days
        self.historical_data = deque(maxlen=lookback_days * 100)  # Store historical slippage
        self.model_parameters = {
            SlippageModel.LINEAR: {'alpha': 0.5, 'beta': 0.3},
            SlippageModel.SQUARE_ROOT: {'alpha': 0.7, 'beta': 0.4},
            SlippageModel.LOGARITHMIC: {'alpha': 0.6, 'beta': 0.35},
            SlippageModel.HYBRID: {'weights': [0.3, 0.3, 0.4]}
        }
        self.market_condition_adjustments = {
            MarketCondition.NORMAL: 1.0,
            MarketCondition.VOLATILE: 1.5,
            MarketCondition.ILLIQUID: 2.0,
            MarketCondition.STRESSED: 2.5,
            MarketCondition.OPENING: 1.3,
            MarketCondition.CLOSING: 1.4
        }
        
    def predict_slippage(self, symbol: str, order_size: float, order_type: OrderType,
                        factors: SlippageFactors, model: SlippageModel = SlippageModel.HYBRID) -> SlippagePrediction:
        """Predict slippage for a given order"""
        try:
            # Detect market condition
            market_condition = self._detect_market_condition(factors)
            
            # Calculate base slippage using selected model
            base_slippage = self._calculate_base_slippage(order_size, factors, model)
            
            # Apply market condition adjustment
            condition_multiplier = self.market_condition_adjustments.get(market_condition, 1.0)
            adjusted_slippage = base_slippage * condition_multiplier
            
            # Apply order type adjustment
            order_type_multiplier = self._get_order_type_multiplier(order_type)
            predicted_slippage = adjusted_slippage * order_type_multiplier
            
            # Calculate confidence based on data quality and market conditions
            confidence = self._calculate_prediction_confidence(factors, market_condition)
            
            # Calculate bounds
            uncertainty = predicted_slippage * (1 - confidence)
            min_slippage = max(0, predicted_slippage - uncertainty)
            max_slippage = predicted_slippage + uncertainty
            
            # Calculate expected cost
            expected_cost = self._calculate_expected_cost(order_size, predicted_slippage, factors)
            
            # Generate recommendations
            recommendations = self._generate_recommendations(predicted_slippage, factors, market_condition)
            
            return SlippagePrediction(
                symbol=symbol,
                timestamp=datetime.now(),
                predicted_slippage=predicted_slippage,
                confidence=confidence,
                model_used=model,
                market_condition=market_condition,
                factors=factors,
                min_slippage=min_slippage,
                max_slippage=max_slippage,
                expected_cost=expected_cost,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Error predicting slippage: {e}")
            # Return conservative estimate
            return SlippagePrediction(
                symbol=symbol,
                timestamp=datetime.now(),
                predicted_slippage=50.0,  # 50 basis points
                confidence=0.3,
                model_used=model,
                market_condition=MarketCondition.NORMAL,
                factors=factors,
                min_slippage=25.0,
                max_slippage=100.0,
                expected_cost=order_size * 0.005,
                recommendations=["Use conservative execution strategy"]
            )
    
    def update_historical_data(self, historical_slippage: HistoricalSlippage):
        """Update historical slippage data for model improvement"""
        try:
            self.historical_data.append(historical_slippage)
            
            # Periodically recalibrate models
            if len(self.historical_data) % 100 == 0:
                self._recalibrate_models()
                
        except Exception as e:
            logger.error(f"Error updating historical data: {e}")
    
    def evaluate_model_performance(self, model: SlippageModel) -> SlippageModelPerformance:
        """Evaluate performance of a specific model"""
        try:
            if not self.historical_data:
                return SlippageModelPerformance(
                    model=model,
                    mae=0.0, rmse=0.0, mape=0.0,
                    accuracy_within_5bp=0.0, accuracy_within_10bp=0.0,
                    bias=0.0, r_squared=0.0, sample_size=0
                )
            
            # Get predictions and actuals for this model
            predictions = []
            actuals = []
            
            for data_point in self.historical_data:
                # Recalculate prediction using historical factors
                predicted = self._calculate_base_slippage(
                    data_point.order_size, data_point.factors, model
                )
                predictions.append(predicted)
                actuals.append(data_point.actual_slippage)
            
            predictions = np.array(predictions)
            actuals = np.array(actuals)
            
            # Calculate metrics
            errors = predictions - actuals
            mae = np.mean(np.abs(errors))
            rmse = np.sqrt(np.mean(errors**2))
            mape = np.mean(np.abs(errors / actuals)) * 100 if np.all(actuals != 0) else 0
            
            accuracy_5bp = np.mean(np.abs(errors) <= 5.0) * 100
            accuracy_10bp = np.mean(np.abs(errors) <= 10.0) * 100
            
            bias = np.mean(errors)
            
            # R-squared
            ss_res = np.sum(errors**2)
            ss_tot = np.sum((actuals - np.mean(actuals))**2)
            r_squared = 1 - (ss_res / ss_tot) if ss_tot > 0 else 0
            
            return SlippageModelPerformance(
                model=model,
                mae=mae,
                rmse=rmse,
                mape=mape,
                accuracy_within_5bp=accuracy_5bp,
                accuracy_within_10bp=accuracy_10bp,
                bias=bias,
                r_squared=r_squared,
                sample_size=len(predictions)
            )
            
        except Exception as e:
            logger.error(f"Error evaluating model performance: {e}")
            return SlippageModelPerformance(
                model=model,
                mae=0.0, rmse=0.0, mape=0.0,
                accuracy_within_5bp=0.0, accuracy_within_10bp=0.0,
                bias=0.0, r_squared=0.0, sample_size=0
            )
    
    def get_optimal_execution_strategy(self, symbol: str, total_size: float, 
                                     factors: SlippageFactors) -> Dict[str, Any]:
        """Get optimal execution strategy to minimize slippage"""
        try:
            # Test different order types and sizes
            strategies = []
            
            # Market order (immediate execution)
            market_prediction = self.predict_slippage(
                symbol, total_size, OrderType.MARKET, factors
            )
            strategies.append({
                'type': 'market',
                'slippage': market_prediction.predicted_slippage,
                'cost': market_prediction.expected_cost,
                'time': 0,  # Immediate
                'risk': 'low'  # Execution risk
            })
            
            # TWAP strategy (split over time)
            twap_slices = [5, 10, 20]
            for slices in twap_slices:
                slice_size = total_size / slices
                slice_prediction = self.predict_slippage(
                    symbol, slice_size, OrderType.TWAP, factors
                )
                total_slippage = slice_prediction.predicted_slippage * slices * 0.8  # Reduction factor
                
                strategies.append({
                    'type': f'twap_{slices}',
                    'slippage': total_slippage,
                    'cost': total_slippage * total_size / 10000,
                    'time': slices * 5,  # 5 minutes per slice
                    'risk': 'medium'
                })
            
            # VWAP strategy
            vwap_prediction = self.predict_slippage(
                symbol, total_size * 0.1, OrderType.VWAP, factors  # 10% participation
            )
            vwap_slippage = vwap_prediction.predicted_slippage * 0.7  # VWAP typically reduces slippage
            
            strategies.append({
                'type': 'vwap',
                'slippage': vwap_slippage,
                'cost': vwap_slippage * total_size / 10000,
                'time': 60,  # 1 hour
                'risk': 'medium'
            })
            
            # Iceberg strategy
            iceberg_prediction = self.predict_slippage(
                symbol, total_size * 0.05, OrderType.ICEBERG, factors  # 5% visible
            )
            iceberg_slippage = iceberg_prediction.predicted_slippage * 0.9
            
            strategies.append({
                'type': 'iceberg',
                'slippage': iceberg_slippage,
                'cost': iceberg_slippage * total_size / 10000,
                'time': 30,  # 30 minutes
                'risk': 'low'
            })
            
            # Find optimal strategy (minimize cost)
            optimal_strategy = min(strategies, key=lambda x: x['cost'])
            
            return {
                'recommended_strategy': optimal_strategy,
                'all_strategies': strategies,
                'market_condition': self._detect_market_condition(factors),
                'urgency_score': self._calculate_urgency_score(factors)
            }
            
        except Exception as e:
            logger.error(f"Error getting optimal execution strategy: {e}")
            return {
                'recommended_strategy': {
                    'type': 'market',
                    'slippage': 50.0,
                    'cost': total_size * 0.005,
                    'time': 0,
                    'risk': 'medium'
                },
                'all_strategies': [],
                'market_condition': MarketCondition.NORMAL,
                'urgency_score': 0.5
            }
    
    def predict_intraday_slippage_pattern(self, symbol: str, factors: SlippageFactors) -> Dict[str, float]:
        """Predict slippage patterns throughout the trading day"""
        try:
            # Define time periods (in hours from market open)
            time_periods = {
                'market_open': 0.5,    # First 30 minutes
                'morning': 2.0,        # 9:30-11:30
                'midday': 4.0,         # 11:30-1:30
                'afternoon': 6.0,      # 1:30-3:30
                'market_close': 7.5    # Last 30 minutes
            }
            
            slippage_pattern = {}
            
            for period, time_factor in time_periods.items():
                # Adjust factors for time of day
                adjusted_factors = SlippageFactors(
                    order_size=factors.order_size,
                    average_daily_volume=factors.average_daily_volume,
                    bid_ask_spread=factors.bid_ask_spread * self._get_time_spread_multiplier(time_factor),
                    market_volatility=factors.market_volatility * self._get_time_volatility_multiplier(time_factor),
                    time_of_day=time_factor / 8.0,  # Normalize to 0-1
                    market_impact=factors.market_impact,
                    liquidity_score=factors.liquidity_score * self._get_time_liquidity_multiplier(time_factor),
                    momentum=factors.momentum,
                    order_book_depth=factors.order_book_depth * self._get_time_depth_multiplier(time_factor),
                    recent_volume=factors.recent_volume
                )
                
                prediction = self.predict_slippage(
                    symbol, factors.order_size, OrderType.MARKET, adjusted_factors
                )
                
                slippage_pattern[period] = prediction.predicted_slippage
            
            return slippage_pattern
            
        except Exception as e:
            logger.error(f"Error predicting intraday slippage pattern: {e}")
            return {
                'market_open': 60.0,
                'morning': 40.0,
                'midday': 30.0,
                'afternoon': 35.0,
                'market_close': 70.0
            }
    
    # Helper methods
    
    def _calculate_base_slippage(self, order_size: float, factors: SlippageFactors, 
                               model: SlippageModel) -> float:
        """Calculate base slippage using specified model"""
        try:
            # Normalize order size by average daily volume
            participation_rate = order_size / factors.average_daily_volume if factors.average_daily_volume > 0 else 0.01
            
            if model == SlippageModel.LINEAR:
                # Linear model: slippage = alpha * participation_rate + beta * spread
                params = self.model_parameters[model]
                base_slippage = (params['alpha'] * participation_rate * 10000 + 
                               params['beta'] * factors.bid_ask_spread * 10000)
                
            elif model == SlippageModel.SQUARE_ROOT:
                # Square root model: slippage = alpha * sqrt(participation_rate) + beta * spread
                params = self.model_parameters[model]
                base_slippage = (params['alpha'] * np.sqrt(participation_rate) * 10000 + 
                               params['beta'] * factors.bid_ask_spread * 10000)
                
            elif model == SlippageModel.LOGARITHMIC:
                # Logarithmic model: slippage = alpha * log(1 + participation_rate) + beta * spread
                params = self.model_parameters[model]
                base_slippage = (params['alpha'] * np.log(1 + participation_rate) * 10000 + 
                               params['beta'] * factors.bid_ask_spread * 10000)
                
            elif model == SlippageModel.HYBRID:
                # Hybrid model: weighted combination of other models
                linear_slippage = self._calculate_base_slippage(order_size, factors, SlippageModel.LINEAR)
                sqrt_slippage = self._calculate_base_slippage(order_size, factors, SlippageModel.SQUARE_ROOT)
                log_slippage = self._calculate_base_slippage(order_size, factors, SlippageModel.LOGARITHMIC)
                
                weights = self.model_parameters[model]['weights']
                base_slippage = (weights[0] * linear_slippage + 
                               weights[1] * sqrt_slippage + 
                               weights[2] * log_slippage)
            else:
                # Default to linear model
                base_slippage = participation_rate * 5000 + factors.bid_ask_spread * 10000
            
            # Apply additional factors
            volatility_adjustment = 1 + factors.market_volatility * 2
            liquidity_adjustment = 2 - factors.liquidity_score  # Lower liquidity = higher slippage
            momentum_adjustment = 1 + abs(factors.momentum) * 0.5
            
            adjusted_slippage = (base_slippage * volatility_adjustment * 
                               liquidity_adjustment * momentum_adjustment)
            
            return max(1.0, adjusted_slippage)  # Minimum 1 basis point
            
        except Exception as e:
            logger.error(f"Error calculating base slippage: {e}")
            return 25.0  # Default 25 basis points
    
    def _detect_market_condition(self, factors: SlippageFactors) -> MarketCondition:
        """Detect current market condition"""
        try:
            # Check time of day
            if factors.time_of_day < 0.1 or factors.time_of_day > 0.9:
                if factors.time_of_day < 0.1:
                    return MarketCondition.OPENING
                else:
                    return MarketCondition.CLOSING
            
            # Check volatility
            if factors.market_volatility > 0.03:  # High volatility
                return MarketCondition.VOLATILE
            
            # Check liquidity
            if factors.liquidity_score < 0.3:  # Low liquidity
                return MarketCondition.ILLIQUID
            
            # Check for stressed conditions
            if (factors.market_volatility > 0.025 and factors.liquidity_score < 0.5 and 
                factors.bid_ask_spread > 0.002):
                return MarketCondition.STRESSED
            
            return MarketCondition.NORMAL
            
        except Exception as e:
            logger.error(f"Error detecting market condition: {e}")
            return MarketCondition.NORMAL
    
    def _get_order_type_multiplier(self, order_type: OrderType) -> float:
        """Get slippage multiplier for different order types"""
        multipliers = {
            OrderType.MARKET: 1.0,      # Full slippage
            OrderType.LIMIT: 0.3,       # Reduced slippage but execution risk
            OrderType.STOP: 1.2,        # Higher slippage due to urgency
            OrderType.ICEBERG: 0.8,     # Reduced market impact
            OrderType.TWAP: 0.7,        # Time-weighted average
            OrderType.VWAP: 0.6         # Volume-weighted average
        }
        return multipliers.get(order_type, 1.0)
    
    def _calculate_prediction_confidence(self, factors: SlippageFactors, 
                                       market_condition: MarketCondition) -> float:
        """Calculate confidence in slippage prediction"""
        try:
            # Base confidence from data quality
            data_quality = min(1.0, len(self.historical_data) / 1000)  # More data = higher confidence
            
            # Market condition confidence
            condition_confidence = {
                MarketCondition.NORMAL: 0.9,
                MarketCondition.VOLATILE: 0.6,
                MarketCondition.ILLIQUID: 0.5,
                MarketCondition.STRESSED: 0.4,
                MarketCondition.OPENING: 0.7,
                MarketCondition.CLOSING: 0.7
            }.get(market_condition, 0.8)
            
            # Liquidity confidence
            liquidity_confidence = factors.liquidity_score
            
            # Overall confidence
            confidence = (data_quality * 0.4 + condition_confidence * 0.4 + 
                         liquidity_confidence * 0.2)
            
            return max(0.1, min(1.0, confidence))
            
        except Exception as e:
            logger.error(f"Error calculating prediction confidence: {e}")
            return 0.5
    
    def _calculate_expected_cost(self, order_size: float, slippage_bp: float, 
                               factors: SlippageFactors) -> float:
        """Calculate expected cost in dollars"""
        try:
            # Assume average price for cost calculation
            avg_price = 100.0  # Default price, should be passed as parameter
            
            # Convert basis points to decimal
            slippage_decimal = slippage_bp / 10000
            
            # Calculate cost
            cost = order_size * avg_price * slippage_decimal
            
            return cost
            
        except Exception as e:
            logger.error(f"Error calculating expected cost: {e}")
            return order_size * 0.005  # Default 0.5% cost
    
    def _generate_recommendations(self, predicted_slippage: float, factors: SlippageFactors,
                                market_condition: MarketCondition) -> List[str]:
        """Generate execution recommendations"""
        recommendations = []
        
        try:
            # High slippage recommendations
            if predicted_slippage > 50:
                recommendations.append("Consider splitting order into smaller sizes")
                recommendations.append("Use TWAP or VWAP execution strategy")
                
            # Low liquidity recommendations
            if factors.liquidity_score < 0.4:
                recommendations.append("Wait for higher liquidity periods")
                recommendations.append("Use iceberg orders to hide order size")
                
            # High volatility recommendations
            if factors.market_volatility > 0.025:
                recommendations.append("Consider limit orders to control execution price")
                recommendations.append("Monitor market closely during execution")
                
            # Market condition specific recommendations
            if market_condition == MarketCondition.OPENING:
                recommendations.append("Wait 15-30 minutes after market open for better liquidity")
            elif market_condition == MarketCondition.CLOSING:
                recommendations.append("Execute before 3:45 PM to avoid closing volatility")
            elif market_condition == MarketCondition.STRESSED:
                recommendations.append("Consider postponing non-urgent trades")
                
            # Order size recommendations
            participation_rate = factors.order_size / factors.average_daily_volume
            if participation_rate > 0.1:  # More than 10% of daily volume
                recommendations.append("Order size is large relative to daily volume")
                recommendations.append("Consider executing over multiple days")
                
            if not recommendations:
                recommendations.append("Market conditions are favorable for execution")
                
        except Exception as e:
            logger.error(f"Error generating recommendations: {e}")
            recommendations = ["Use standard execution practices"]
        
        return recommendations
    
    def _recalibrate_models(self):
        """Recalibrate model parameters based on historical data"""
        try:
            if len(self.historical_data) < 50:
                return
            
            # Simple recalibration - adjust parameters based on recent performance
            recent_data = list(self.historical_data)[-50:]
            
            # Calculate average error for each model
            for model in [SlippageModel.LINEAR, SlippageModel.SQUARE_ROOT, SlippageModel.LOGARITHMIC]:
                errors = []
                for data_point in recent_data:
                    predicted = self._calculate_base_slippage(
                        data_point.order_size, data_point.factors, model
                    )
                    error = predicted - data_point.actual_slippage
                    errors.append(error)
                
                avg_error = np.mean(errors)
                
                # Adjust parameters to reduce bias
                if model in self.model_parameters:
                    if avg_error > 5:  # Over-predicting
                        self.model_parameters[model]['alpha'] *= 0.95
                        self.model_parameters[model]['beta'] *= 0.95
                    elif avg_error < -5:  # Under-predicting
                        self.model_parameters[model]['alpha'] *= 1.05
                        self.model_parameters[model]['beta'] *= 1.05
            
            logger.info("Model parameters recalibrated")
            
        except Exception as e:
            logger.error(f"Error recalibrating models: {e}")
    
    def _calculate_urgency_score(self, factors: SlippageFactors) -> float:
        """Calculate urgency score for execution"""
        try:
            # Higher urgency for:
            # - End of day
            # - High momentum
            # - Deteriorating liquidity
            
            time_urgency = max(0, (factors.time_of_day - 0.8) * 5) if factors.time_of_day > 0.8 else 0
            momentum_urgency = abs(factors.momentum) * 0.5
            liquidity_urgency = max(0, (0.5 - factors.liquidity_score) * 2)
            
            urgency = (time_urgency + momentum_urgency + liquidity_urgency) / 3
            
            return max(0, min(1, urgency))
            
        except Exception as e:
            logger.error(f"Error calculating urgency score: {e}")
            return 0.5
    
    def _get_time_spread_multiplier(self, time_factor: float) -> float:
        """Get spread multiplier based on time of day"""
        # Higher spreads at open and close
        if time_factor < 1 or time_factor > 7:
            return 1.5
        elif time_factor < 2 or time_factor > 6:
            return 1.2
        else:
            return 1.0
    
    def _get_time_volatility_multiplier(self, time_factor: float) -> float:
        """Get volatility multiplier based on time of day"""
        # Higher volatility at open and close
        if time_factor < 0.5 or time_factor > 7.5:
            return 1.8
        elif time_factor < 1.5 or time_factor > 6.5:
            return 1.3
        else:
            return 1.0
    
    def _get_time_liquidity_multiplier(self, time_factor: float) -> float:
        """Get liquidity multiplier based on time of day"""
        # Lower liquidity at open, close, and lunch
        if time_factor < 1 or time_factor > 7:
            return 0.7
        elif 3.5 < time_factor < 4.5:  # Lunch time
            return 0.8
        else:
            return 1.0
    
    def _get_time_depth_multiplier(self, time_factor: float) -> float:
        """Get order book depth multiplier based on time of day"""
        # Lower depth at open and close
        if time_factor < 1 or time_factor > 7:
            return 0.6
        elif time_factor < 2 or time_factor > 6:
            return 0.8
        else:
            return 1.0