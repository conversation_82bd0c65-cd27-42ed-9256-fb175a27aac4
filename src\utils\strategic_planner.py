#!/usr/bin/env python3
"""
Strategic Planner
Advanced strategic planning and execution system for trading decisions.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
import logging
from datetime import datetime, timed<PERSON>ta
from collections import defaultdict
import statistics
import asyncio

logger = logging.getLogger(__name__)

class StrategyType(Enum):
    """Types of trading strategies."""
    MOMENTUM = "momentum"
    MEAN_REVERSION = "mean_reversion"
    BREAKOUT = "breakout"
    TREND_FOLLOWING = "trend_following"
    ARBITRAGE = "arbitrage"
    PAIRS_TRADING = "pairs_trading"
    MARKET_MAKING = "market_making"
    SWING_TRADING = "swing_trading"
    SCALPING = "scalping"
    POSITION_TRADING = "position_trading"

class TimeHorizon(Enum):
    """Time horizons for strategic planning."""
    ULTRA_SHORT = "ultra_short"  # seconds to minutes
    SHORT = "short"  # minutes to hours
    MEDIUM = "medium"  # hours to days
    LONG = "long"  # days to weeks
    ULTRA_LONG = "ultra_long"  # weeks to months

class RiskLevel(Enum):
    """Risk levels for strategies."""
    VERY_LOW = "very_low"
    LOW = "low"
    MODERATE = "moderate"
    HIGH = "high"
    VERY_HIGH = "very_high"

class StrategyStatus(Enum):
    """Status of strategic plans."""
    PLANNING = "planning"
    ACTIVE = "active"
    PAUSED = "paused"
    COMPLETED = "completed"
    CANCELLED = "cancelled"
    UNDER_REVIEW = "under_review"

class MarketCondition(Enum):
    """Market conditions for strategy selection."""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    SIDEWAYS = "sideways"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    UNCERTAIN = "uncertain"

@dataclass
class StrategicObjective:
    """A strategic objective with measurable goals."""
    objective_id: str
    name: str
    description: str
    target_return: float
    max_drawdown: float
    time_horizon: TimeHorizon
    risk_level: RiskLevel
    priority: int
    success_criteria: Dict[str, Any]
    constraints: Dict[str, Any]
    created_at: datetime
    target_date: Optional[datetime] = None
    metadata: Dict[str, Any] = None

@dataclass
class StrategicPlan:
    """A comprehensive strategic plan."""
    plan_id: str
    name: str
    description: str
    objectives: List[StrategicObjective]
    strategies: List[StrategyType]
    market_conditions: List[MarketCondition]
    resource_allocation: Dict[str, float]
    risk_budget: float
    expected_return: float
    confidence_level: float
    time_horizon: TimeHorizon
    status: StrategyStatus
    milestones: List[Dict[str, Any]]
    contingency_plans: List[Dict[str, Any]]
    performance_metrics: Dict[str, Any]
    review_schedule: List[datetime]
    created_at: datetime
    updated_at: datetime
    metadata: Dict[str, Any] = None

@dataclass
class StrategyRecommendation:
    """A strategic recommendation with rationale."""
    recommendation_id: str
    strategy_type: StrategyType
    confidence: float
    expected_return: float
    risk_score: float
    time_horizon: TimeHorizon
    market_conditions: List[MarketCondition]
    rationale: str
    supporting_evidence: List[str]
    risk_factors: List[str]
    implementation_steps: List[str]
    resource_requirements: Dict[str, Any]
    success_probability: float
    alternative_strategies: List[str]
    created_at: datetime
    metadata: Dict[str, Any] = None

class StrategicPlanner:
    """Advanced strategic planning system for trading operations."""
    
    def __init__(self):
        self.strategy_weights = {
            StrategyType.MOMENTUM: 1.0,
            StrategyType.MEAN_REVERSION: 0.9,
            StrategyType.BREAKOUT: 1.1,
            StrategyType.TREND_FOLLOWING: 1.0,
            StrategyType.ARBITRAGE: 0.8,
            StrategyType.PAIRS_TRADING: 0.7,
            StrategyType.MARKET_MAKING: 0.6,
            StrategyType.SWING_TRADING: 0.9,
            StrategyType.SCALPING: 1.2,
            StrategyType.POSITION_TRADING: 0.8
        }
        
        self.market_strategy_mapping = {
            MarketCondition.TRENDING_UP: [StrategyType.MOMENTUM, StrategyType.TREND_FOLLOWING, StrategyType.BREAKOUT],
            MarketCondition.TRENDING_DOWN: [StrategyType.MEAN_REVERSION, StrategyType.PAIRS_TRADING],
            MarketCondition.SIDEWAYS: [StrategyType.MEAN_REVERSION, StrategyType.MARKET_MAKING, StrategyType.PAIRS_TRADING],
            MarketCondition.HIGH_VOLATILITY: [StrategyType.SCALPING, StrategyType.BREAKOUT, StrategyType.MOMENTUM],
            MarketCondition.LOW_VOLATILITY: [StrategyType.MARKET_MAKING, StrategyType.SWING_TRADING],
            MarketCondition.UNCERTAIN: [StrategyType.PAIRS_TRADING, StrategyType.ARBITRAGE]
        }
        
        self.risk_strategy_mapping = {
            RiskLevel.VERY_LOW: [StrategyType.ARBITRAGE, StrategyType.MARKET_MAKING],
            RiskLevel.LOW: [StrategyType.PAIRS_TRADING, StrategyType.MEAN_REVERSION],
            RiskLevel.MODERATE: [StrategyType.SWING_TRADING, StrategyType.TREND_FOLLOWING],
            RiskLevel.HIGH: [StrategyType.MOMENTUM, StrategyType.BREAKOUT],
            RiskLevel.VERY_HIGH: [StrategyType.SCALPING, StrategyType.MOMENTUM]
        }
        
        self.active_plans = []
        self.completed_plans = []
        self.strategy_performance = defaultdict(list)
    
    async def create_strategic_plan(self, objectives: List[StrategicObjective],
                                  market_analysis: Dict[str, Any],
                                  resource_constraints: Dict[str, Any]) -> StrategicPlan:
        """Create a comprehensive strategic plan."""
        try:
            # Analyze objectives
            primary_objective = self._identify_primary_objective(objectives)
            
            # Determine market conditions
            market_conditions = self._assess_market_conditions(market_analysis)
            
            # Select optimal strategies
            strategies = await self._select_strategies(objectives, market_conditions, resource_constraints)
            
            # Calculate resource allocation
            resource_allocation = self._calculate_resource_allocation(strategies, resource_constraints)
            
            # Estimate expected returns and risks
            expected_return = self._estimate_expected_return(strategies, market_conditions)
            risk_budget = self._calculate_risk_budget(objectives, strategies)
            
            # Calculate confidence level
            confidence_level = self._calculate_confidence_level(strategies, market_conditions, market_analysis)
            
            # Create milestones
            milestones = self._create_milestones(objectives, primary_objective.time_horizon)
            
            # Develop contingency plans
            contingency_plans = await self._develop_contingency_plans(strategies, market_conditions)
            
            # Set up review schedule
            review_schedule = self._create_review_schedule(primary_objective.time_horizon)
            
            # Create the plan
            plan = StrategicPlan(
                plan_id=f"plan_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                name=f"Strategic Plan - {primary_objective.name}",
                description=f"Comprehensive strategic plan targeting {primary_objective.target_return:.2%} return",
                objectives=objectives,
                strategies=strategies,
                market_conditions=market_conditions,
                resource_allocation=resource_allocation,
                risk_budget=risk_budget,
                expected_return=expected_return,
                confidence_level=confidence_level,
                time_horizon=primary_objective.time_horizon,
                status=StrategyStatus.PLANNING,
                milestones=milestones,
                contingency_plans=contingency_plans,
                performance_metrics={},
                review_schedule=review_schedule,
                created_at=datetime.now(),
                updated_at=datetime.now(),
                metadata={
                    'market_analysis_summary': self._summarize_market_analysis(market_analysis),
                    'resource_utilization': sum(resource_allocation.values()),
                    'strategy_count': len(strategies)
                }
            )
            
            self.active_plans.append(plan)
            return plan
            
        except Exception as e:
            logger.error(f"Error creating strategic plan: {e}")
            return self._create_fallback_plan(objectives)
    
    async def generate_strategy_recommendations(self, market_analysis: Dict[str, Any],
                                             portfolio_state: Dict[str, Any],
                                             risk_tolerance: RiskLevel) -> List[StrategyRecommendation]:
        """Generate strategic recommendations based on current conditions."""
        recommendations = []
        
        try:
            # Assess current market conditions
            market_conditions = self._assess_market_conditions(market_analysis)
            
            # Get suitable strategies for current conditions
            suitable_strategies = self._get_suitable_strategies(market_conditions, risk_tolerance)
            
            # Generate recommendations for each strategy
            for strategy in suitable_strategies:
                recommendation = await self._create_strategy_recommendation(
                    strategy, market_analysis, portfolio_state, market_conditions, risk_tolerance
                )
                recommendations.append(recommendation)
            
            # Sort by confidence and expected return
            recommendations.sort(key=lambda x: (x.confidence, x.expected_return), reverse=True)
            
            return recommendations[:5]  # Return top 5 recommendations
            
        except Exception as e:
            logger.error(f"Error generating strategy recommendations: {e}")
            return []
    
    async def optimize_strategy_allocation(self, strategies: List[StrategyType],
                                         market_conditions: List[MarketCondition],
                                         risk_budget: float) -> Dict[StrategyType, float]:
        """Optimize allocation across multiple strategies."""
        try:
            # Calculate strategy scores
            strategy_scores = {}
            for strategy in strategies:
                score = self._calculate_strategy_score(strategy, market_conditions)
                strategy_scores[strategy] = score
            
            # Normalize scores
            total_score = sum(strategy_scores.values())
            if total_score > 0:
                normalized_scores = {k: v / total_score for k, v in strategy_scores.items()}
            else:
                # Equal allocation if no clear preference
                normalized_scores = {k: 1.0 / len(strategies) for k in strategies}
            
            # Apply risk constraints
            risk_adjusted_allocation = self._apply_risk_constraints(normalized_scores, risk_budget)
            
            # Ensure allocations sum to 1.0
            total_allocation = sum(risk_adjusted_allocation.values())
            if total_allocation > 0:
                final_allocation = {k: v / total_allocation for k, v in risk_adjusted_allocation.items()}
            else:
                final_allocation = {k: 1.0 / len(strategies) for k in strategies}
            
            return final_allocation
            
        except Exception as e:
            logger.error(f"Error optimizing strategy allocation: {e}")
            # Return equal allocation as fallback
            return {strategy: 1.0 / len(strategies) for strategy in strategies}
    
    async def evaluate_plan_performance(self, plan_id: str, current_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate the performance of a strategic plan."""
        try:
            plan = self._find_plan_by_id(plan_id)
            if not plan:
                return {'error': 'Plan not found'}
            
            # Calculate performance metrics
            performance_evaluation = {
                'plan_id': plan_id,
                'evaluation_date': datetime.now(),
                'time_elapsed': (datetime.now() - plan.created_at).days,
                'objectives_progress': {},
                'strategy_performance': {},
                'risk_metrics': {},
                'overall_score': 0.0,
                'recommendations': []
            }
            
            # Evaluate objective progress
            for objective in plan.objectives:
                progress = self._evaluate_objective_progress(objective, current_metrics)
                performance_evaluation['objectives_progress'][objective.objective_id] = progress
            
            # Evaluate strategy performance
            for strategy in plan.strategies:
                strategy_perf = self._evaluate_strategy_performance(strategy, current_metrics)
                performance_evaluation['strategy_performance'][strategy.value] = strategy_perf
            
            # Calculate risk metrics
            risk_metrics = self._calculate_risk_metrics(plan, current_metrics)
            performance_evaluation['risk_metrics'] = risk_metrics
            
            # Calculate overall score
            overall_score = self._calculate_overall_score(performance_evaluation)
            performance_evaluation['overall_score'] = overall_score
            
            # Generate recommendations
            recommendations = self._generate_performance_recommendations(plan, performance_evaluation)
            performance_evaluation['recommendations'] = recommendations
            
            # Update plan performance metrics
            plan.performance_metrics = performance_evaluation
            plan.updated_at = datetime.now()
            
            return performance_evaluation
            
        except Exception as e:
            logger.error(f"Error evaluating plan performance: {e}")
            return {'error': str(e)}
    
    def _identify_primary_objective(self, objectives: List[StrategicObjective]) -> StrategicObjective:
        """Identify the primary objective based on priority and target return."""
        if not objectives:
            # Create default objective
            return StrategicObjective(
                objective_id="default_001",
                name="Default Growth",
                description="Default growth objective",
                target_return=0.10,
                max_drawdown=0.05,
                time_horizon=TimeHorizon.MEDIUM,
                risk_level=RiskLevel.MODERATE,
                priority=1,
                success_criteria={'min_return': 0.08},
                constraints={'max_risk': 0.05},
                created_at=datetime.now()
            )
        
        # Sort by priority (lower number = higher priority) and target return
        sorted_objectives = sorted(objectives, key=lambda x: (x.priority, -x.target_return))
        return sorted_objectives[0]
    
    def _assess_market_conditions(self, market_analysis: Dict[str, Any]) -> List[MarketCondition]:
        """Assess current market conditions from analysis."""
        conditions = []
        
        try:
            # Extract relevant metrics
            trend = market_analysis.get('trend', 'neutral')
            volatility = market_analysis.get('volatility', 0.5)
            sentiment = market_analysis.get('sentiment', 'neutral')
            
            # Determine trend condition
            if trend == 'bullish' or sentiment == 'bullish':
                conditions.append(MarketCondition.TRENDING_UP)
            elif trend == 'bearish' or sentiment == 'bearish':
                conditions.append(MarketCondition.TRENDING_DOWN)
            else:
                conditions.append(MarketCondition.SIDEWAYS)
            
            # Determine volatility condition
            if volatility > 0.7:
                conditions.append(MarketCondition.HIGH_VOLATILITY)
            elif volatility < 0.3:
                conditions.append(MarketCondition.LOW_VOLATILITY)
            
            # Add uncertainty if conflicting signals
            if len(set([trend, sentiment])) > 1:
                conditions.append(MarketCondition.UNCERTAIN)
        
        except Exception as e:
            logger.error(f"Error assessing market conditions: {e}")
            conditions = [MarketCondition.UNCERTAIN]
        
        return conditions if conditions else [MarketCondition.UNCERTAIN]
    
    async def _select_strategies(self, objectives: List[StrategicObjective],
                               market_conditions: List[MarketCondition],
                               resource_constraints: Dict[str, Any]) -> List[StrategyType]:
        """Select optimal strategies based on objectives and conditions."""
        strategy_scores = defaultdict(float)
        
        try:
            # Score strategies based on market conditions
            for condition in market_conditions:
                suitable_strategies = self.market_strategy_mapping.get(condition, [])
                for strategy in suitable_strategies:
                    strategy_scores[strategy] += 1.0
            
            # Score strategies based on objectives
            for objective in objectives:
                suitable_strategies = self.risk_strategy_mapping.get(objective.risk_level, [])
                for strategy in suitable_strategies:
                    strategy_scores[strategy] += objective.priority * 0.5
            
            # Apply strategy weights
            for strategy in strategy_scores:
                strategy_scores[strategy] *= self.strategy_weights.get(strategy, 1.0)
            
            # Select top strategies
            sorted_strategies = sorted(strategy_scores.items(), key=lambda x: x[1], reverse=True)
            
            # Limit based on resource constraints
            max_strategies = resource_constraints.get('max_strategies', 3)
            selected_strategies = [strategy for strategy, score in sorted_strategies[:max_strategies]]
            
            return selected_strategies if selected_strategies else [StrategyType.TREND_FOLLOWING]
            
        except Exception as e:
            logger.error(f"Error selecting strategies: {e}")
            return [StrategyType.TREND_FOLLOWING]
    
    def _calculate_resource_allocation(self, strategies: List[StrategyType],
                                     resource_constraints: Dict[str, Any]) -> Dict[str, float]:
        """Calculate resource allocation for strategies."""
        try:
            total_capital = resource_constraints.get('total_capital', 100000)
            max_risk_per_strategy = resource_constraints.get('max_risk_per_strategy', 0.02)
            
            # Equal allocation by default
            allocation_per_strategy = 1.0 / len(strategies) if strategies else 1.0
            
            allocation = {
                'capital_allocation': {strategy.value: allocation_per_strategy for strategy in strategies},
                'risk_allocation': {strategy.value: max_risk_per_strategy for strategy in strategies},
                'total_capital_used': total_capital,
                'total_risk_budget': len(strategies) * max_risk_per_strategy
            }
            
            return allocation
            
        except Exception as e:
            logger.error(f"Error calculating resource allocation: {e}")
            return {'capital_allocation': {}, 'risk_allocation': {}, 'total_capital_used': 0, 'total_risk_budget': 0}
    
    def _estimate_expected_return(self, strategies: List[StrategyType],
                                market_conditions: List[MarketCondition]) -> float:
        """Estimate expected return for strategy combination."""
        try:
            if not strategies:
                return 0.0
            
            # Base returns for each strategy type
            base_returns = {
                StrategyType.MOMENTUM: 0.12,
                StrategyType.MEAN_REVERSION: 0.08,
                StrategyType.BREAKOUT: 0.15,
                StrategyType.TREND_FOLLOWING: 0.10,
                StrategyType.ARBITRAGE: 0.05,
                StrategyType.PAIRS_TRADING: 0.07,
                StrategyType.MARKET_MAKING: 0.06,
                StrategyType.SWING_TRADING: 0.09,
                StrategyType.SCALPING: 0.08,
                StrategyType.POSITION_TRADING: 0.11
            }
            
            # Market condition multipliers
            condition_multipliers = {
                MarketCondition.TRENDING_UP: 1.2,
                MarketCondition.TRENDING_DOWN: 0.8,
                MarketCondition.SIDEWAYS: 0.9,
                MarketCondition.HIGH_VOLATILITY: 1.1,
                MarketCondition.LOW_VOLATILITY: 0.9,
                MarketCondition.UNCERTAIN: 0.7
            }
            
            # Calculate weighted average return
            total_return = 0.0
            for strategy in strategies:
                base_return = base_returns.get(strategy, 0.08)
                
                # Apply market condition adjustments
                adjusted_return = base_return
                for condition in market_conditions:
                    multiplier = condition_multipliers.get(condition, 1.0)
                    adjusted_return *= multiplier
                
                total_return += adjusted_return
            
            # Average across strategies
            expected_return = total_return / len(strategies)
            
            # Cap at reasonable bounds
            return max(0.0, min(expected_return, 0.5))
            
        except Exception as e:
            logger.error(f"Error estimating expected return: {e}")
            return 0.08
    
    def _calculate_risk_budget(self, objectives: List[StrategicObjective],
                             strategies: List[StrategyType]) -> float:
        """Calculate appropriate risk budget."""
        try:
            if not objectives:
                return 0.02
            
            # Use the most conservative max drawdown from objectives
            max_drawdowns = [obj.max_drawdown for obj in objectives]
            conservative_drawdown = min(max_drawdowns)
            
            # Adjust based on number of strategies (diversification)
            diversification_factor = 1.0 - (0.1 * (len(strategies) - 1))
            diversification_factor = max(0.5, diversification_factor)
            
            risk_budget = conservative_drawdown * diversification_factor
            
            # Ensure reasonable bounds
            return max(0.01, min(risk_budget, 0.10))
            
        except Exception as e:
            logger.error(f"Error calculating risk budget: {e}")
            return 0.02
    
    def _calculate_confidence_level(self, strategies: List[StrategyType],
                                  market_conditions: List[MarketCondition],
                                  market_analysis: Dict[str, Any]) -> float:
        """Calculate confidence level in the strategic plan."""
        try:
            confidence_factors = []
            
            # Strategy alignment with market conditions
            alignment_score = 0.0
            for condition in market_conditions:
                suitable_strategies = self.market_strategy_mapping.get(condition, [])
                aligned_strategies = [s for s in strategies if s in suitable_strategies]
                if strategies:
                    alignment_score += len(aligned_strategies) / len(strategies)
            
            if market_conditions:
                alignment_score /= len(market_conditions)
            
            confidence_factors.append(alignment_score)
            
            # Market analysis quality
            analysis_quality = market_analysis.get('confidence', 0.5)
            confidence_factors.append(analysis_quality)
            
            # Strategy diversification
            diversification_score = min(1.0, len(strategies) / 3.0)
            confidence_factors.append(diversification_score)
            
            # Historical performance (simulated)
            historical_performance = 0.7  # Placeholder
            confidence_factors.append(historical_performance)
            
            # Calculate weighted average
            weights = [0.3, 0.3, 0.2, 0.2]
            confidence = sum(f * w for f, w in zip(confidence_factors, weights))
            
            return max(0.0, min(confidence, 1.0))
            
        except Exception as e:
            logger.error(f"Error calculating confidence level: {e}")
            return 0.5
    
    def _create_milestones(self, objectives: List[StrategicObjective],
                         time_horizon: TimeHorizon) -> List[Dict[str, Any]]:
        """Create milestones for the strategic plan."""
        milestones = []
        
        try:
            # Determine milestone intervals based on time horizon
            if time_horizon == TimeHorizon.ULTRA_SHORT:
                intervals = [0.25, 0.5, 0.75, 1.0]  # 25%, 50%, 75%, 100%
            elif time_horizon == TimeHorizon.SHORT:
                intervals = [0.33, 0.67, 1.0]  # 33%, 67%, 100%
            elif time_horizon == TimeHorizon.MEDIUM:
                intervals = [0.25, 0.5, 0.75, 1.0]
            elif time_horizon == TimeHorizon.LONG:
                intervals = [0.2, 0.4, 0.6, 0.8, 1.0]
            else:  # ULTRA_LONG
                intervals = [0.1, 0.3, 0.5, 0.7, 0.9, 1.0]
            
            # Create milestones
            for i, interval in enumerate(intervals):
                milestone = {
                    'milestone_id': f"milestone_{i+1}",
                    'name': f"Milestone {i+1}",
                    'target_progress': interval,
                    'target_date': datetime.now() + timedelta(days=30 * interval),
                    'success_criteria': {
                        'min_return_progress': interval * 0.8,
                        'max_drawdown_limit': objectives[0].max_drawdown if objectives else 0.05
                    },
                    'status': 'pending'
                }
                milestones.append(milestone)
        
        except Exception as e:
            logger.error(f"Error creating milestones: {e}")
        
        return milestones
    
    async def _develop_contingency_plans(self, strategies: List[StrategyType],
                                       market_conditions: List[MarketCondition]) -> List[Dict[str, Any]]:
        """Develop contingency plans for different scenarios."""
        contingency_plans = []
        
        try:
            # Market downturn contingency
            contingency_plans.append({
                'scenario': 'market_downturn',
                'trigger_conditions': ['market_drop_10_percent', 'volatility_spike'],
                'actions': [
                    'Reduce position sizes by 50%',
                    'Increase cash allocation',
                    'Activate defensive strategies',
                    'Implement stop-loss protocols'
                ],
                'alternative_strategies': [StrategyType.MEAN_REVERSION, StrategyType.PAIRS_TRADING],
                'risk_adjustments': {'max_risk_per_trade': 0.01}
            })
            
            # High volatility contingency
            contingency_plans.append({
                'scenario': 'high_volatility',
                'trigger_conditions': ['volatility_above_threshold', 'rapid_price_movements'],
                'actions': [
                    'Switch to shorter timeframes',
                    'Increase stop-loss frequency',
                    'Reduce leverage',
                    'Focus on liquid markets'
                ],
                'alternative_strategies': [StrategyType.SCALPING, StrategyType.MARKET_MAKING],
                'risk_adjustments': {'position_size_multiplier': 0.5}
            })
            
            # Strategy underperformance contingency
            contingency_plans.append({
                'scenario': 'strategy_underperformance',
                'trigger_conditions': ['negative_returns_30_days', 'drawdown_exceeds_limit'],
                'actions': [
                    'Review and adjust strategy parameters',
                    'Reduce allocation to underperforming strategies',
                    'Implement additional risk controls',
                    'Consider strategy replacement'
                ],
                'alternative_strategies': [StrategyType.TREND_FOLLOWING, StrategyType.SWING_TRADING],
                'risk_adjustments': {'strategy_allocation_cap': 0.2}
            })
        
        except Exception as e:
            logger.error(f"Error developing contingency plans: {e}")
        
        return contingency_plans
    
    def _create_review_schedule(self, time_horizon: TimeHorizon) -> List[datetime]:
        """Create review schedule based on time horizon."""
        reviews = []
        
        try:
            current_time = datetime.now()
            
            if time_horizon == TimeHorizon.ULTRA_SHORT:
                # Review every hour for ultra-short strategies
                for i in range(1, 25):  # 24 hours
                    reviews.append(current_time + timedelta(hours=i))
            elif time_horizon == TimeHorizon.SHORT:
                # Review every 6 hours
                for i in range(1, 5):  # 4 reviews per day
                    reviews.append(current_time + timedelta(hours=i*6))
            elif time_horizon == TimeHorizon.MEDIUM:
                # Daily reviews
                for i in range(1, 8):  # 7 days
                    reviews.append(current_time + timedelta(days=i))
            elif time_horizon == TimeHorizon.LONG:
                # Weekly reviews
                for i in range(1, 5):  # 4 weeks
                    reviews.append(current_time + timedelta(weeks=i))
            else:  # ULTRA_LONG
                # Monthly reviews
                for i in range(1, 7):  # 6 months
                    reviews.append(current_time + timedelta(days=i*30))
        
        except Exception as e:
            logger.error(f"Error creating review schedule: {e}")
        
        return reviews
    
    def _summarize_market_analysis(self, market_analysis: Dict[str, Any]) -> str:
        """Summarize market analysis for metadata."""
        try:
            trend = market_analysis.get('trend', 'neutral')
            volatility = market_analysis.get('volatility', 0.5)
            sentiment = market_analysis.get('sentiment', 'neutral')
            
            return f"Trend: {trend}, Volatility: {volatility:.2f}, Sentiment: {sentiment}"
        except Exception as e:
            logger.error(f"Error summarizing market analysis: {e}")
            return "Market analysis summary unavailable"
    
    def _create_fallback_plan(self, objectives: List[StrategicObjective]) -> StrategicPlan:
        """Create a fallback plan when main planning fails."""
        return StrategicPlan(
            plan_id=f"fallback_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            name="Fallback Strategic Plan",
            description="Conservative fallback plan with basic strategies",
            objectives=objectives or [],
            strategies=[StrategyType.TREND_FOLLOWING],
            market_conditions=[MarketCondition.UNCERTAIN],
            resource_allocation={'capital_allocation': {'trend_following': 1.0}},
            risk_budget=0.02,
            expected_return=0.08,
            confidence_level=0.3,
            time_horizon=TimeHorizon.MEDIUM,
            status=StrategyStatus.PLANNING,
            milestones=[],
            contingency_plans=[],
            performance_metrics={},
            review_schedule=[datetime.now() + timedelta(days=1)],
            created_at=datetime.now(),
            updated_at=datetime.now(),
            metadata={'fallback': True}
        )
    
    def _get_suitable_strategies(self, market_conditions: List[MarketCondition],
                               risk_tolerance: RiskLevel) -> List[StrategyType]:
        """Get strategies suitable for current conditions and risk tolerance."""
        suitable_strategies = set()
        
        try:
            # Add strategies based on market conditions
            for condition in market_conditions:
                strategies = self.market_strategy_mapping.get(condition, [])
                suitable_strategies.update(strategies)
            
            # Filter by risk tolerance
            risk_appropriate_strategies = self.risk_strategy_mapping.get(risk_tolerance, [])
            
            # Intersection of market-suitable and risk-appropriate strategies
            final_strategies = list(suitable_strategies.intersection(set(risk_appropriate_strategies)))
            
            # If no intersection, use risk-appropriate strategies
            if not final_strategies:
                final_strategies = risk_appropriate_strategies
            
            # If still empty, use default strategies
            if not final_strategies:
                final_strategies = [StrategyType.TREND_FOLLOWING, StrategyType.MEAN_REVERSION]
            
            return final_strategies[:5]  # Limit to top 5
            
        except Exception as e:
            logger.error(f"Error getting suitable strategies: {e}")
            return [StrategyType.TREND_FOLLOWING]
    
    async def _create_strategy_recommendation(self, strategy: StrategyType,
                                            market_analysis: Dict[str, Any],
                                            portfolio_state: Dict[str, Any],
                                            market_conditions: List[MarketCondition],
                                            risk_tolerance: RiskLevel) -> StrategyRecommendation:
        """Create a detailed strategy recommendation."""
        try:
            # Calculate confidence based on market alignment
            confidence = self._calculate_strategy_confidence(strategy, market_conditions, market_analysis)
            
            # Estimate expected return
            expected_return = self._estimate_strategy_return(strategy, market_conditions)
            
            # Calculate risk score
            risk_score = self._calculate_strategy_risk(strategy, market_analysis, risk_tolerance)
            
            # Determine time horizon
            time_horizon = self._determine_strategy_time_horizon(strategy)
            
            # Generate rationale
            rationale = self._generate_strategy_rationale(strategy, market_conditions, market_analysis)
            
            # Identify supporting evidence
            supporting_evidence = self._identify_supporting_evidence(strategy, market_analysis)
            
            # Identify risk factors
            risk_factors = self._identify_strategy_risk_factors(strategy, market_analysis)
            
            # Create implementation steps
            implementation_steps = self._create_implementation_steps(strategy)
            
            # Calculate resource requirements
            resource_requirements = self._calculate_resource_requirements(strategy, portfolio_state)
            
            # Calculate success probability
            success_probability = self._calculate_success_probability(strategy, market_conditions, confidence)
            
            # Identify alternative strategies
            alternative_strategies = self._identify_alternative_strategies(strategy, market_conditions)
            
            recommendation = StrategyRecommendation(
                recommendation_id=f"rec_{strategy.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
                strategy_type=strategy,
                confidence=confidence,
                expected_return=expected_return,
                risk_score=risk_score,
                time_horizon=time_horizon,
                market_conditions=market_conditions,
                rationale=rationale,
                supporting_evidence=supporting_evidence,
                risk_factors=risk_factors,
                implementation_steps=implementation_steps,
                resource_requirements=resource_requirements,
                success_probability=success_probability,
                alternative_strategies=alternative_strategies,
                created_at=datetime.now(),
                metadata={
                    'market_alignment_score': self._calculate_market_alignment(strategy, market_conditions),
                    'risk_adjusted_return': expected_return / max(risk_score, 0.01)
                }
            )
            
            return recommendation
            
        except Exception as e:
            logger.error(f"Error creating strategy recommendation: {e}")
            return self._create_default_recommendation(strategy)
    
    def _calculate_strategy_score(self, strategy: StrategyType,
                                market_conditions: List[MarketCondition]) -> float:
        """Calculate overall score for a strategy given market conditions."""
        try:
            score = 0.0
            
            # Base score from strategy weight
            score += self.strategy_weights.get(strategy, 1.0)
            
            # Bonus for market condition alignment
            for condition in market_conditions:
                suitable_strategies = self.market_strategy_mapping.get(condition, [])
                if strategy in suitable_strategies:
                    score += 1.0
            
            # Historical performance bonus (simulated)
            historical_performance = self.strategy_performance.get(strategy, [0.5])
            avg_performance = statistics.mean(historical_performance)
            score += avg_performance
            
            return score
            
        except Exception as e:
            logger.error(f"Error calculating strategy score: {e}")
            return 1.0
    
    def _apply_risk_constraints(self, allocations: Dict[StrategyType, float],
                              risk_budget: float) -> Dict[StrategyType, float]:
        """Apply risk constraints to strategy allocations."""
        try:
            # Risk multipliers for each strategy
            risk_multipliers = {
                StrategyType.MOMENTUM: 1.2,
                StrategyType.MEAN_REVERSION: 0.8,
                StrategyType.BREAKOUT: 1.3,
                StrategyType.TREND_FOLLOWING: 1.0,
                StrategyType.ARBITRAGE: 0.5,
                StrategyType.PAIRS_TRADING: 0.7,
                StrategyType.MARKET_MAKING: 0.6,
                StrategyType.SWING_TRADING: 0.9,
                StrategyType.SCALPING: 1.4,
                StrategyType.POSITION_TRADING: 0.8
            }
            
            # Calculate risk-adjusted allocations
            risk_adjusted = {}
            total_risk = 0.0
            
            for strategy, allocation in allocations.items():
                risk_multiplier = risk_multipliers.get(strategy, 1.0)
                strategy_risk = allocation * risk_multiplier
                total_risk += strategy_risk
            
            # Scale down if total risk exceeds budget
            if total_risk > risk_budget:
                scale_factor = risk_budget / total_risk
                for strategy, allocation in allocations.items():
                    risk_adjusted[strategy] = allocation * scale_factor
            else:
                risk_adjusted = allocations.copy()
            
            return risk_adjusted
            
        except Exception as e:
            logger.error(f"Error applying risk constraints: {e}")
            return allocations
    
    def _find_plan_by_id(self, plan_id: str) -> Optional[StrategicPlan]:
        """Find a strategic plan by ID."""
        for plan in self.active_plans + self.completed_plans:
            if plan.plan_id == plan_id:
                return plan
        return None
    
    def _evaluate_objective_progress(self, objective: StrategicObjective,
                                   current_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate progress towards an objective."""
        try:
            current_return = current_metrics.get('total_return', 0.0)
            current_drawdown = current_metrics.get('max_drawdown', 0.0)
            
            progress = {
                'objective_id': objective.objective_id,
                'target_return': objective.target_return,
                'current_return': current_return,
                'return_progress': current_return / objective.target_return if objective.target_return != 0 else 0,
                'drawdown_limit': objective.max_drawdown,
                'current_drawdown': current_drawdown,
                'drawdown_compliance': current_drawdown <= objective.max_drawdown,
                'overall_progress': min(1.0, current_return / objective.target_return) if objective.target_return > 0 else 0
            }
            
            return progress
            
        except Exception as e:
            logger.error(f"Error evaluating objective progress: {e}")
            return {'error': str(e)}
    
    def _evaluate_strategy_performance(self, strategy: StrategyType,
                                     current_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Evaluate performance of a specific strategy."""
        try:
            # Simulated strategy-specific metrics
            strategy_return = current_metrics.get(f'{strategy.value}_return', 0.0)
            strategy_sharpe = current_metrics.get(f'{strategy.value}_sharpe', 0.0)
            strategy_drawdown = current_metrics.get(f'{strategy.value}_drawdown', 0.0)
            
            performance = {
                'strategy': strategy.value,
                'return': strategy_return,
                'sharpe_ratio': strategy_sharpe,
                'max_drawdown': strategy_drawdown,
                'win_rate': current_metrics.get(f'{strategy.value}_win_rate', 0.5),
                'profit_factor': current_metrics.get(f'{strategy.value}_profit_factor', 1.0),
                'performance_score': self._calculate_strategy_performance_score(strategy_return, strategy_sharpe, strategy_drawdown)
            }
            
            return performance
            
        except Exception as e:
            logger.error(f"Error evaluating strategy performance: {e}")
            return {'error': str(e)}
    
    def _calculate_risk_metrics(self, plan: StrategicPlan,
                              current_metrics: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate risk metrics for the plan."""
        try:
            risk_metrics = {
                'var_95': current_metrics.get('var_95', 0.0),
                'expected_shortfall': current_metrics.get('expected_shortfall', 0.0),
                'volatility': current_metrics.get('volatility', 0.0),
                'beta': current_metrics.get('beta', 1.0),
                'correlation_to_market': current_metrics.get('market_correlation', 0.0),
                'risk_budget_utilization': current_metrics.get('max_drawdown', 0.0) / plan.risk_budget,
                'risk_adjusted_return': current_metrics.get('total_return', 0.0) / max(current_metrics.get('volatility', 0.01), 0.01)
            }
            
            return risk_metrics
            
        except Exception as e:
            logger.error(f"Error calculating risk metrics: {e}")
            return {}
    
    def _calculate_overall_score(self, performance_evaluation: Dict[str, Any]) -> float:
        """Calculate overall performance score."""
        try:
            scores = []
            
            # Objective progress scores
            objectives_progress = performance_evaluation.get('objectives_progress', {})
            for obj_progress in objectives_progress.values():
                if isinstance(obj_progress, dict) and 'overall_progress' in obj_progress:
                    scores.append(obj_progress['overall_progress'])
            
            # Strategy performance scores
            strategy_performance = performance_evaluation.get('strategy_performance', {})
            for strategy_perf in strategy_performance.values():
                if isinstance(strategy_perf, dict) and 'performance_score' in strategy_perf:
                    scores.append(strategy_perf['performance_score'])
            
            # Risk metrics score
            risk_metrics = performance_evaluation.get('risk_metrics', {})
            risk_score = 1.0 - min(1.0, risk_metrics.get('risk_budget_utilization', 0.5))
            scores.append(risk_score)
            
            # Calculate weighted average
            if scores:
                overall_score = statistics.mean(scores)
            else:
                overall_score = 0.5
            
            return max(0.0, min(overall_score, 1.0))
            
        except Exception as e:
            logger.error(f"Error calculating overall score: {e}")
            return 0.5
    
    def _generate_performance_recommendations(self, plan: StrategicPlan,
                                            performance_evaluation: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on performance evaluation."""
        recommendations = []
        
        try:
            overall_score = performance_evaluation.get('overall_score', 0.5)
            
            if overall_score < 0.3:
                recommendations.append("Consider major strategy revision due to poor performance")
                recommendations.append("Implement additional risk controls")
                recommendations.append("Review and adjust position sizing")
            elif overall_score < 0.6:
                recommendations.append("Monitor performance closely and consider minor adjustments")
                recommendations.append("Evaluate individual strategy contributions")
            else:
                recommendations.append("Performance is on track - continue current approach")
                recommendations.append("Consider gradual position size increases if risk allows")
            
            # Risk-specific recommendations
            risk_metrics = performance_evaluation.get('risk_metrics', {})
            risk_utilization = risk_metrics.get('risk_budget_utilization', 0.5)
            
            if risk_utilization > 0.8:
                recommendations.append("Risk budget utilization is high - consider reducing exposure")
            elif risk_utilization < 0.3:
                recommendations.append("Risk budget is underutilized - consider increasing exposure")
            
            # Strategy-specific recommendations
            strategy_performance = performance_evaluation.get('strategy_performance', {})
            for strategy, perf in strategy_performance.items():
                if isinstance(perf, dict):
                    perf_score = perf.get('performance_score', 0.5)
                    if perf_score < 0.3:
                        recommendations.append(f"Consider reducing allocation to {strategy} strategy")
                    elif perf_score > 0.8:
                        recommendations.append(f"Consider increasing allocation to {strategy} strategy")
        
        except Exception as e:
            logger.error(f"Error generating performance recommendations: {e}")
        
        return recommendations[:5]  # Limit to top 5 recommendations
    
    def _calculate_strategy_confidence(self, strategy: StrategyType,
                                     market_conditions: List[MarketCondition],
                                     market_analysis: Dict[str, Any]) -> float:
        """Calculate confidence in a strategy recommendation."""
        try:
            confidence_factors = []
            
            # Market condition alignment
            alignment_score = 0.0
            for condition in market_conditions:
                suitable_strategies = self.market_strategy_mapping.get(condition, [])
                if strategy in suitable_strategies:
                    alignment_score += 1.0
            
            if market_conditions:
                alignment_score /= len(market_conditions)
            
            confidence_factors.append(alignment_score)
            
            # Market analysis confidence
            analysis_confidence = market_analysis.get('confidence', 0.5)
            confidence_factors.append(analysis_confidence)
            
            # Historical performance (simulated)
            historical_performance = statistics.mean(self.strategy_performance.get(strategy, [0.6]))
            confidence_factors.append(historical_performance)
            
            # Strategy complexity (simpler strategies get higher confidence)
            complexity_scores = {
                StrategyType.TREND_FOLLOWING: 0.8,
                StrategyType.MEAN_REVERSION: 0.7,
                StrategyType.MOMENTUM: 0.7,
                StrategyType.BREAKOUT: 0.6,
                StrategyType.SWING_TRADING: 0.7,
                StrategyType.POSITION_TRADING: 0.8,
                StrategyType.SCALPING: 0.5,
                StrategyType.MARKET_MAKING: 0.4,
                StrategyType.PAIRS_TRADING: 0.5,
                StrategyType.ARBITRAGE: 0.6
            }
            
            complexity_score = complexity_scores.get(strategy, 0.6)
            confidence_factors.append(complexity_score)
            
            # Calculate weighted average
            weights = [0.3, 0.3, 0.2, 0.2]
            confidence = sum(f * w for f, w in zip(confidence_factors, weights))
            
            return max(0.0, min(confidence, 1.0))
            
        except Exception as e:
            logger.error(f"Error calculating strategy confidence: {e}")
            return 0.5
    
    def _estimate_strategy_return(self, strategy: StrategyType,
                                market_conditions: List[MarketCondition]) -> float:
        """Estimate expected return for a specific strategy."""
        try:
            # Base returns for each strategy
            base_returns = {
                StrategyType.MOMENTUM: 0.12,
                StrategyType.MEAN_REVERSION: 0.08,
                StrategyType.BREAKOUT: 0.15,
                StrategyType.TREND_FOLLOWING: 0.10,
                StrategyType.ARBITRAGE: 0.05,
                StrategyType.PAIRS_TRADING: 0.07,
                StrategyType.MARKET_MAKING: 0.06,
                StrategyType.SWING_TRADING: 0.09,
                StrategyType.SCALPING: 0.08,
                StrategyType.POSITION_TRADING: 0.11
            }
            
            base_return = base_returns.get(strategy, 0.08)
            
            # Market condition adjustments
            condition_multipliers = {
                MarketCondition.TRENDING_UP: 1.2,
                MarketCondition.TRENDING_DOWN: 0.8,
                MarketCondition.SIDEWAYS: 0.9,
                MarketCondition.HIGH_VOLATILITY: 1.1,
                MarketCondition.LOW_VOLATILITY: 0.9,
                MarketCondition.UNCERTAIN: 0.7
            }
            
            adjusted_return = base_return
            for condition in market_conditions:
                multiplier = condition_multipliers.get(condition, 1.0)
                adjusted_return *= multiplier
            
            return max(0.0, min(adjusted_return, 0.5))
            
        except Exception as e:
            logger.error(f"Error estimating strategy return: {e}")
            return 0.08
    
    def _calculate_strategy_risk(self, strategy: StrategyType,
                               market_analysis: Dict[str, Any],
                               risk_tolerance: RiskLevel) -> float:
        """Calculate risk score for a strategy."""
        try:
            # Base risk scores for each strategy
            base_risks = {
                StrategyType.MOMENTUM: 0.7,
                StrategyType.MEAN_REVERSION: 0.4,
                StrategyType.BREAKOUT: 0.8,
                StrategyType.TREND_FOLLOWING: 0.5,
                StrategyType.ARBITRAGE: 0.2,
                StrategyType.PAIRS_TRADING: 0.3,
                StrategyType.MARKET_MAKING: 0.3,
                StrategyType.SWING_TRADING: 0.4,
                StrategyType.SCALPING: 0.9,
                StrategyType.POSITION_TRADING: 0.4
            }
            
            base_risk = base_risks.get(strategy, 0.5)
            
            # Market volatility adjustment
            market_volatility = market_analysis.get('volatility', 0.5)
            volatility_multiplier = 1.0 + market_volatility
            
            # Risk tolerance adjustment
            tolerance_multipliers = {
                RiskLevel.VERY_LOW: 0.5,
                RiskLevel.LOW: 0.7,
                RiskLevel.MODERATE: 1.0,
                RiskLevel.HIGH: 1.3,
                RiskLevel.VERY_HIGH: 1.5
            }
            
            tolerance_multiplier = tolerance_multipliers.get(risk_tolerance, 1.0)
            
            risk_score = base_risk * volatility_multiplier * tolerance_multiplier
            
            return max(0.0, min(risk_score, 1.0))
            
        except Exception as e:
            logger.error(f"Error calculating strategy risk: {e}")
            return 0.5
    
    def _determine_strategy_time_horizon(self, strategy: StrategyType) -> TimeHorizon:
        """Determine appropriate time horizon for a strategy."""
        strategy_horizons = {
            StrategyType.SCALPING: TimeHorizon.ULTRA_SHORT,
            StrategyType.MARKET_MAKING: TimeHorizon.ULTRA_SHORT,
            StrategyType.MOMENTUM: TimeHorizon.SHORT,
            StrategyType.BREAKOUT: TimeHorizon.SHORT,
            StrategyType.MEAN_REVERSION: TimeHorizon.MEDIUM,
            StrategyType.SWING_TRADING: TimeHorizon.MEDIUM,
            StrategyType.TREND_FOLLOWING: TimeHorizon.LONG,
            StrategyType.POSITION_TRADING: TimeHorizon.LONG,
            StrategyType.PAIRS_TRADING: TimeHorizon.MEDIUM,
            StrategyType.ARBITRAGE: TimeHorizon.SHORT
        }
        
        return strategy_horizons.get(strategy, TimeHorizon.MEDIUM)
    
    def _generate_strategy_rationale(self, strategy: StrategyType,
                                   market_conditions: List[MarketCondition],
                                   market_analysis: Dict[str, Any]) -> str:
        """Generate rationale for strategy recommendation."""
        try:
            rationale_parts = []
            
            # Strategy description
            strategy_descriptions = {
                StrategyType.MOMENTUM: "Momentum strategy capitalizes on continuing price trends",
                StrategyType.MEAN_REVERSION: "Mean reversion strategy profits from price corrections",
                StrategyType.BREAKOUT: "Breakout strategy captures moves beyond key levels",
                StrategyType.TREND_FOLLOWING: "Trend following strategy rides established trends",
                StrategyType.ARBITRAGE: "Arbitrage strategy exploits price discrepancies",
                StrategyType.PAIRS_TRADING: "Pairs trading strategy profits from relative price movements",
                StrategyType.MARKET_MAKING: "Market making strategy provides liquidity for spreads",
                StrategyType.SWING_TRADING: "Swing trading strategy captures medium-term price swings",
                StrategyType.SCALPING: "Scalping strategy profits from small, frequent price movements",
                StrategyType.POSITION_TRADING: "Position trading strategy holds for long-term trends"
            }
            
            description = strategy_descriptions.get(strategy, "Strategy aims to generate consistent returns")
            rationale_parts.append(description)
            
            # Market condition alignment
            suitable_conditions = []
            for condition in market_conditions:
                suitable_strategies = self.market_strategy_mapping.get(condition, [])
                if strategy in suitable_strategies:
                    suitable_conditions.append(condition.value.replace('_', ' '))
            
            if suitable_conditions:
                rationale_parts.append(f"Well-suited for current {', '.join(suitable_conditions)} conditions")
            
            # Market analysis insights
            trend = market_analysis.get('trend', 'neutral')
            volatility = market_analysis.get('volatility', 0.5)
            
            if trend != 'neutral':
                rationale_parts.append(f"Aligns with {trend} market trend")
            
            if volatility > 0.7:
                rationale_parts.append("Benefits from high volatility environment")
            elif volatility < 0.3:
                rationale_parts.append("Suitable for low volatility conditions")
            
            return ". ".join(rationale_parts) + "."
            
        except Exception as e:
            logger.error(f"Error generating strategy rationale: {e}")
            return f"Strategy recommendation based on current market analysis and {strategy.value} approach."
    
    def _identify_supporting_evidence(self, strategy: StrategyType,
                                    market_analysis: Dict[str, Any]) -> List[str]:
        """Identify supporting evidence for strategy recommendation."""
        evidence = []
        
        try:
            # Technical indicators
            if 'technical_indicators' in market_analysis:
                indicators = market_analysis['technical_indicators']
                if strategy in [StrategyType.MOMENTUM, StrategyType.TREND_FOLLOWING]:
                    if indicators.get('rsi', 50) > 60:
                        evidence.append("RSI indicates strong momentum")
                    if indicators.get('macd_signal') == 'bullish':
                        evidence.append("MACD shows bullish crossover")
                
                elif strategy == StrategyType.MEAN_REVERSION:
                    if indicators.get('rsi', 50) > 70 or indicators.get('rsi', 50) < 30:
                        evidence.append("RSI indicates overbought/oversold conditions")
                    if indicators.get('bollinger_position') in ['upper', 'lower']:
                        evidence.append("Price at Bollinger Band extremes")
            
            # Volume analysis
            if 'volume_analysis' in market_analysis:
                volume = market_analysis['volume_analysis']
                if volume.get('trend') == 'increasing':
                    evidence.append("Increasing volume supports price movement")
                if volume.get('accumulation_distribution') == 'accumulation':
                    evidence.append("Volume shows accumulation pattern")
            
            # Market sentiment
            sentiment = market_analysis.get('sentiment', 'neutral')
            if sentiment == 'bullish' and strategy in [StrategyType.MOMENTUM, StrategyType.TREND_FOLLOWING]:
                evidence.append("Bullish market sentiment supports strategy")
            elif sentiment == 'bearish' and strategy == StrategyType.MEAN_REVERSION:
                evidence.append("Bearish sentiment creates reversion opportunities")
            
            # Historical performance
            if strategy in self.strategy_performance:
                avg_performance = statistics.mean(self.strategy_performance[strategy])
                if avg_performance > 0.6:
                    evidence.append("Strong historical performance record")
        
        except Exception as e:
            logger.error(f"Error identifying supporting evidence: {e}")
        
        return evidence[:5]  # Limit to top 5 pieces of evidence
    
    def _identify_strategy_risk_factors(self, strategy: StrategyType,
                                      market_analysis: Dict[str, Any]) -> List[str]:
        """Identify risk factors for strategy."""
        risk_factors = []
        
        try:
            # Strategy-specific risks
            strategy_risks = {
                StrategyType.MOMENTUM: ["Risk of trend reversal", "Susceptible to false breakouts"],
                StrategyType.MEAN_REVERSION: ["Risk of continued trending", "Timing challenges"],
                StrategyType.BREAKOUT: ["False breakout risk", "High volatility exposure"],
                StrategyType.TREND_FOLLOWING: ["Late entry risk", "Whipsaw in sideways markets"],
                StrategyType.ARBITRAGE: ["Execution risk", "Limited opportunity windows"],
                StrategyType.PAIRS_TRADING: ["Correlation breakdown risk", "Sector-specific risks"],
                StrategyType.MARKET_MAKING: ["Inventory risk", "Adverse selection risk"],
                StrategyType.SWING_TRADING: ["Overnight risk", "Gap risk"],
                StrategyType.SCALPING: ["High transaction costs", "Technology dependency"],
                StrategyType.POSITION_TRADING: ["Long-term market risk", "Fundamental change risk"]
            }
            
            base_risks = strategy_risks.get(strategy, ["General market risk"])
            risk_factors.extend(base_risks)
            
            # Market condition risks
            volatility = market_analysis.get('volatility', 0.5)
            if volatility > 0.8:
                risk_factors.append("High volatility increases execution risk")
            
            uncertainty = market_analysis.get('uncertainty', 0.5)
            if uncertainty > 0.7:
                risk_factors.append("High market uncertainty")
            
            # Liquidity risks
            liquidity = market_analysis.get('liquidity', 0.5)
            if liquidity < 0.3:
                risk_factors.append("Low liquidity conditions")
        
        except Exception as e:
            logger.error(f"Error identifying risk factors: {e}")
        
        return risk_factors[:5]  # Limit to top 5 risk factors
    
    def _create_implementation_steps(self, strategy: StrategyType) -> List[str]:
        """Create implementation steps for strategy."""
        implementation_steps = {
            StrategyType.MOMENTUM: [
                "Identify strong trending assets",
                "Set up momentum indicators (RSI, MACD)",
                "Define entry criteria for momentum confirmation",
                "Implement trailing stop-loss system",
                "Monitor for trend exhaustion signals"
            ],
            StrategyType.MEAN_REVERSION: [
                "Identify overbought/oversold conditions",
                "Set up mean reversion indicators (Bollinger Bands, RSI)",
                "Define reversal confirmation criteria",
                "Implement position sizing based on deviation",
                "Set profit targets near mean levels"
            ],
            StrategyType.BREAKOUT: [
                "Identify key support/resistance levels",
                "Set up breakout detection system",
                "Define volume confirmation criteria",
                "Implement false breakout filters",
                "Set initial stop-loss below breakout level"
            ],
            StrategyType.TREND_FOLLOWING: [
                "Identify established trends",
                "Set up trend-following indicators (Moving Averages)",
                "Define trend confirmation criteria",
                "Implement position scaling system",
                "Monitor for trend reversal signals"
            ],
            StrategyType.ARBITRAGE: [
                "Identify arbitrage opportunities",
                "Set up real-time price monitoring",
                "Implement automated execution system",
                "Monitor execution latency",
                "Track profit margins and costs"
            ],
            StrategyType.PAIRS_TRADING: [
                "Select correlated asset pairs",
                "Calculate historical correlation and spread",
                "Define spread deviation thresholds",
                "Implement pair position management",
                "Monitor correlation stability"
            ],
            StrategyType.MARKET_MAKING: [
                "Set up bid-ask spread management",
                "Implement inventory control system",
                "Define liquidity provision parameters",
                "Monitor adverse selection risk",
                "Adjust spreads based on volatility"
            ],
            StrategyType.SWING_TRADING: [
                "Identify swing high/low patterns",
                "Set up swing detection indicators",
                "Define entry/exit criteria",
                "Implement risk management for overnight positions",
                "Monitor for pattern completion"
            ],
            StrategyType.SCALPING: [
                "Set up high-frequency data feeds",
                "Implement low-latency execution system",
                "Define micro-movement criteria",
                "Optimize transaction cost management",
                "Monitor execution quality metrics"
            ],
            StrategyType.POSITION_TRADING: [
                "Conduct fundamental analysis",
                "Identify long-term trends",
                "Define position sizing criteria",
                "Implement long-term risk management",
                "Set up periodic review schedule"
            ]
        }
        
        return implementation_steps.get(strategy, [
            "Define strategy parameters",
            "Set up monitoring system",
            "Implement risk management",
            "Execute initial positions",
            "Monitor and adjust as needed"
        ])
    
    def _calculate_resource_requirements(self, strategy: StrategyType,
                                       portfolio_state: Dict[str, Any]) -> Dict[str, Any]:
        """Calculate resource requirements for strategy."""
        try:
            # Base resource requirements
            base_requirements = {
                StrategyType.MOMENTUM: {'capital': 0.2, 'risk_budget': 0.03, 'monitoring_intensity': 'high'},
                StrategyType.MEAN_REVERSION: {'capital': 0.15, 'risk_budget': 0.02, 'monitoring_intensity': 'medium'},
                StrategyType.BREAKOUT: {'capital': 0.25, 'risk_budget': 0.04, 'monitoring_intensity': 'high'},
                StrategyType.TREND_FOLLOWING: {'capital': 0.3, 'risk_budget': 0.025, 'monitoring_intensity': 'medium'},
                StrategyType.ARBITRAGE: {'capital': 0.1, 'risk_budget': 0.01, 'monitoring_intensity': 'very_high'},
                StrategyType.PAIRS_TRADING: {'capital': 0.2, 'risk_budget': 0.015, 'monitoring_intensity': 'medium'},
                StrategyType.MARKET_MAKING: {'capital': 0.15, 'risk_budget': 0.02, 'monitoring_intensity': 'very_high'},
                StrategyType.SWING_TRADING: {'capital': 0.2, 'risk_budget': 0.025, 'monitoring_intensity': 'low'},
                StrategyType.SCALPING: {'capital': 0.1, 'risk_budget': 0.01, 'monitoring_intensity': 'very_high'},
                StrategyType.POSITION_TRADING: {'capital': 0.4, 'risk_budget': 0.02, 'monitoring_intensity': 'low'}
            }
            
            requirements = base_requirements.get(strategy, {
                'capital': 0.2, 'risk_budget': 0.02, 'monitoring_intensity': 'medium'
            })
            
            # Adjust based on portfolio state
            available_capital = portfolio_state.get('available_capital', 100000)
            current_risk = portfolio_state.get('current_risk', 0.0)
            
            requirements['absolute_capital'] = requirements['capital'] * available_capital
            requirements['absolute_risk'] = requirements['risk_budget']
            requirements['feasible'] = (current_risk + requirements['risk_budget']) <= 0.1
            
            return requirements
            
        except Exception as e:
            logger.error(f"Error calculating resource requirements: {e}")
            return {'capital': 0.2, 'risk_budget': 0.02, 'monitoring_intensity': 'medium', 'feasible': True}
    
    def _calculate_success_probability(self, strategy: StrategyType,
                                     market_conditions: List[MarketCondition],
                                     confidence: float) -> float:
        """Calculate probability of strategy success."""
        try:
            # Base success probabilities
            base_probabilities = {
                StrategyType.MOMENTUM: 0.65,
                StrategyType.MEAN_REVERSION: 0.60,
                StrategyType.BREAKOUT: 0.55,
                StrategyType.TREND_FOLLOWING: 0.70,
                StrategyType.ARBITRAGE: 0.85,
                StrategyType.PAIRS_TRADING: 0.65,
                StrategyType.MARKET_MAKING: 0.75,
                StrategyType.SWING_TRADING: 0.60,
                StrategyType.SCALPING: 0.55,
                StrategyType.POSITION_TRADING: 0.65
            }
            
            base_probability = base_probabilities.get(strategy, 0.6)
            
            # Adjust for market conditions
            condition_adjustments = 0.0
            for condition in market_conditions:
                suitable_strategies = self.market_strategy_mapping.get(condition, [])
                if strategy in suitable_strategies:
                    condition_adjustments += 0.1
                else:
                    condition_adjustments -= 0.05
            
            # Adjust for confidence
            confidence_adjustment = (confidence - 0.5) * 0.2
            
            # Calculate final probability
            success_probability = base_probability + condition_adjustments + confidence_adjustment
            
            return max(0.0, min(success_probability, 1.0))
            
        except Exception as e:
            logger.error(f"Error calculating success probability: {e}")
            return 0.6
    
    def _identify_alternative_strategies(self, strategy: StrategyType,
                                       market_conditions: List[MarketCondition]) -> List[str]:
        """Identify alternative strategies."""
        alternatives = []
        
        try:
            # Get all suitable strategies for current conditions
            suitable_strategies = set()
            for condition in market_conditions:
                strategies = self.market_strategy_mapping.get(condition, [])
                suitable_strategies.update(strategies)
            
            # Remove the current strategy
            suitable_strategies.discard(strategy)
            
            # Convert to string list
            alternatives = [s.value for s in suitable_strategies]
            
            # If no alternatives from conditions, use complementary strategies
            if not alternatives:
                complementary_strategies = {
                    StrategyType.MOMENTUM: [StrategyType.MEAN_REVERSION, StrategyType.TREND_FOLLOWING],
                    StrategyType.MEAN_REVERSION: [StrategyType.MOMENTUM, StrategyType.SWING_TRADING],
                    StrategyType.BREAKOUT: [StrategyType.TREND_FOLLOWING, StrategyType.MOMENTUM],
                    StrategyType.TREND_FOLLOWING: [StrategyType.MOMENTUM, StrategyType.POSITION_TRADING],
                    StrategyType.ARBITRAGE: [StrategyType.PAIRS_TRADING, StrategyType.MARKET_MAKING],
                    StrategyType.PAIRS_TRADING: [StrategyType.ARBITRAGE, StrategyType.MEAN_REVERSION],
                    StrategyType.MARKET_MAKING: [StrategyType.ARBITRAGE, StrategyType.SCALPING],
                    StrategyType.SWING_TRADING: [StrategyType.MEAN_REVERSION, StrategyType.POSITION_TRADING],
                    StrategyType.SCALPING: [StrategyType.MARKET_MAKING, StrategyType.MOMENTUM],
                    StrategyType.POSITION_TRADING: [StrategyType.TREND_FOLLOWING, StrategyType.SWING_TRADING]
                }
                
                complementary = complementary_strategies.get(strategy, [])
                alternatives = [s.value for s in complementary]
        
        except Exception as e:
            logger.error(f"Error identifying alternative strategies: {e}")
        
        return alternatives[:3]  # Limit to top 3 alternatives
    
    def _calculate_market_alignment(self, strategy: StrategyType,
                                  market_conditions: List[MarketCondition]) -> float:
        """Calculate market alignment score for strategy."""
        try:
            alignment_score = 0.0
            
            for condition in market_conditions:
                suitable_strategies = self.market_strategy_mapping.get(condition, [])
                if strategy in suitable_strategies:
                    alignment_score += 1.0
            
            # Normalize by number of conditions
            if market_conditions:
                alignment_score /= len(market_conditions)
            
            return alignment_score
            
        except Exception as e:
            logger.error(f"Error calculating market alignment: {e}")
            return 0.5
    
    def _create_default_recommendation(self, strategy: StrategyType) -> StrategyRecommendation:
        """Create a default recommendation when detailed analysis fails."""
        return StrategyRecommendation(
            recommendation_id=f"default_{strategy.value}_{datetime.now().strftime('%Y%m%d_%H%M%S')}",
            strategy_type=strategy,
            confidence=0.5,
            expected_return=0.08,
            risk_score=0.5,
            time_horizon=TimeHorizon.MEDIUM,
            market_conditions=[MarketCondition.UNCERTAIN],
            rationale=f"Default recommendation for {strategy.value} strategy",
            supporting_evidence=["General market analysis"],
            risk_factors=["Market uncertainty"],
            implementation_steps=["Set up basic strategy framework"],
            resource_requirements={'capital': 0.2, 'risk_budget': 0.02},
            success_probability=0.6,
            alternative_strategies=["trend_following", "mean_reversion"],
            created_at=datetime.now(),
            metadata={'default': True}
        )
    
    def _calculate_strategy_performance_score(self, return_value: float,
                                            sharpe_ratio: float,
                                            max_drawdown: float) -> float:
        """Calculate performance score for a strategy."""
        try:
            # Normalize components
            return_score = min(1.0, max(0.0, return_value / 0.2))  # Normalize to 20% max return
            sharpe_score = min(1.0, max(0.0, sharpe_ratio / 3.0))  # Normalize to 3.0 max Sharpe
            drawdown_score = max(0.0, 1.0 - (max_drawdown / 0.1))  # Penalize drawdown over 10%
            
            # Weighted average
            performance_score = (0.4 * return_score + 0.3 * sharpe_score + 0.3 * drawdown_score)
            
            return max(0.0, min(performance_score, 1.0))
            
        except Exception as e:
            logger.error(f"Error calculating strategy performance score: {e}")
            return 0.5
    
    def get_active_plans(self) -> List[StrategicPlan]:
        """Get all active strategic plans."""
        return [plan for plan in self.active_plans if plan.status == StrategyStatus.ACTIVE]
    
    def get_plan_summary(self, plan_id: str) -> Dict[str, Any]:
        """Get summary of a strategic plan."""
        plan = self._find_plan_by_id(plan_id)
        if not plan:
            return {'error': 'Plan not found'}
        
        return {
            'plan_id': plan.plan_id,
            'name': plan.name,
            'status': plan.status.value,
            'expected_return': plan.expected_return,
            'confidence_level': plan.confidence_level,
            'strategies': [s.value for s in plan.strategies],
            'time_horizon': plan.time_horizon.value,
            'created_at': plan.created_at.isoformat(),
            'objectives_count': len(plan.objectives),
            'milestones_count': len(plan.milestones),
            'next_review': plan.review_schedule[0].isoformat() if plan.review_schedule else None
        }
    
    async def update_plan_status(self, plan_id: str, new_status: StrategyStatus) -> bool:
        """Update the status of a strategic plan."""
        try:
            plan = self._find_plan_by_id(plan_id)
            if not plan:
                return False
            
            plan.status = new_status
            plan.updated_at = datetime.now()
            
            # Move to completed plans if status is completed or cancelled
            if new_status in [StrategyStatus.COMPLETED, StrategyStatus.CANCELLED]:
                if plan in self.active_plans:
                    self.active_plans.remove(plan)
                    self.completed_plans.append(plan)
            
            return True
            
        except Exception as e:
            logger.error(f"Error updating plan status: {e}")
            return False
    
    def get_strategy_performance_history(self, strategy: StrategyType) -> List[float]:
        """Get performance history for a strategy."""
        return self.strategy_performance.get(strategy, [])
    
    def update_strategy_performance(self, strategy: StrategyType, performance: float):
        """Update performance record for a strategy."""
        self.strategy_performance[strategy].append(performance)
        
        # Keep only last 100 records
        if len(self.strategy_performance[strategy]) > 100:
            self.strategy_performance[strategy] = self.strategy_performance[strategy][-100:]