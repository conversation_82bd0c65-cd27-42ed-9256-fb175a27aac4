#!/usr/bin/env python3
"""
Advanced Technical Indicators
Comprehensive collection of technical analysis indicators.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class IndicatorType(Enum):
    """Types of technical indicators."""
    TREND = "trend"
    MOMENTUM = "momentum"
    VOLATILITY = "volatility"
    VOLUME = "volume"
    OSCILLATOR = "oscillator"
    OVERLAY = "overlay"

@dataclass
class IndicatorResult:
    """Result of a technical indicator calculation."""
    name: str
    values: np.ndarray
    parameters: Dict
    indicator_type: IndicatorType
    timestamp: pd.Timestamp
    metadata: Dict = None

class AdvancedIndicators:
    """Advanced technical indicators calculator."""
    
    def __init__(self):
        self.indicators = {}
        self.cache = {}
    
    def calculate_all_indicators(self, data: pd.DataFrame, 
                               symbol: str = None) -> Dict[str, IndicatorResult]:
        """Calculate all available indicators for given data."""
        try:
            if data.empty or len(data) < 20:
                logger.warning(f"Insufficient data for indicator calculation: {len(data)} rows")
                return {}
            
            results = {}
            
            # Extract OHLCV data
            high = data['high'].values if 'high' in data.columns else data['close'].values
            low = data['low'].values if 'low' in data.columns else data['close'].values
            close = data['close'].values
            volume = data['volume'].values if 'volume' in data.columns else np.ones(len(close))
            
            # Trend Indicators
            results.update(self._calculate_trend_indicators(high, low, close, volume))
            
            # Momentum Indicators
            results.update(self._calculate_momentum_indicators(high, low, close, volume))
            
            # Volatility Indicators
            results.update(self._calculate_volatility_indicators(high, low, close))
            
            # Volume Indicators
            results.update(self._calculate_volume_indicators(close, volume))
            
            # Oscillators
            results.update(self._calculate_oscillators(high, low, close))
            
            # Custom Indicators
            results.update(self._calculate_custom_indicators(high, low, close, volume))
            
            return results
            
        except Exception as e:
            logger.error(f"Error calculating indicators: {e}")
            return {}
    
    def _calculate_trend_indicators(self, high: np.ndarray, low: np.ndarray, 
                                  close: np.ndarray, volume: np.ndarray) -> Dict[str, IndicatorResult]:
        """Calculate trend-following indicators."""
        indicators = {}
        
        try:
            # Simple Moving Averages
            for period in [5, 10, 20, 50, 100, 200]:
                if len(close) >= period:
                    sma = self._sma(close, period)
                    indicators[f'sma_{period}'] = IndicatorResult(
                        name=f'SMA_{period}',
                        values=sma,
                        parameters={'period': period},
                        indicator_type=IndicatorType.TREND,
                        timestamp=pd.Timestamp.now()
                    )
            
            # Exponential Moving Averages
            for period in [12, 26, 50, 100, 200]:
                if len(close) >= period:
                    ema = self._ema(close, period)
                    indicators[f'ema_{period}'] = IndicatorResult(
                        name=f'EMA_{period}',
                        values=ema,
                        parameters={'period': period},
                        indicator_type=IndicatorType.TREND,
                        timestamp=pd.Timestamp.now()
                    )
            
            # MACD
            if len(close) >= 34:
                macd_line, signal_line, histogram = self._macd(close)
                indicators['macd'] = IndicatorResult(
                    name='MACD',
                    values=np.column_stack([macd_line, signal_line, histogram]),
                    parameters={'fast': 12, 'slow': 26, 'signal': 9},
                    indicator_type=IndicatorType.TREND,
                    timestamp=pd.Timestamp.now()
                )
            
            # Parabolic SAR
            if len(high) >= 10:
                sar = self._parabolic_sar(high, low)
                indicators['sar'] = IndicatorResult(
                    name='Parabolic_SAR',
                    values=sar,
                    parameters={'acceleration': 0.02, 'maximum': 0.2},
                    indicator_type=IndicatorType.TREND,
                    timestamp=pd.Timestamp.now()
                )
            
            # Ichimoku Cloud
            if len(high) >= 52:
                tenkan, kijun, senkou_a, senkou_b, chikou = self._ichimoku(high, low, close)
                indicators['ichimoku'] = IndicatorResult(
                    name='Ichimoku',
                    values=np.column_stack([tenkan, kijun, senkou_a, senkou_b, chikou]),
                    parameters={'tenkan': 9, 'kijun': 26, 'senkou': 52},
                    indicator_type=IndicatorType.TREND,
                    timestamp=pd.Timestamp.now()
                )
            
        except Exception as e:
            logger.error(f"Error calculating trend indicators: {e}")
        
        return indicators
    
    def _calculate_momentum_indicators(self, high: np.ndarray, low: np.ndarray,
                                     close: np.ndarray, volume: np.ndarray) -> Dict[str, IndicatorResult]:
        """Calculate momentum indicators."""
        indicators = {}
        
        try:
            # RSI
            if len(close) >= 14:
                rsi = self._rsi(close, 14)
                indicators['rsi'] = IndicatorResult(
                    name='RSI',
                    values=rsi,
                    parameters={'period': 14},
                    indicator_type=IndicatorType.MOMENTUM,
                    timestamp=pd.Timestamp.now()
                )
            
            # Stochastic Oscillator
            if len(high) >= 14:
                stoch_k, stoch_d = self._stochastic(high, low, close)
                indicators['stochastic'] = IndicatorResult(
                    name='Stochastic',
                    values=np.column_stack([stoch_k, stoch_d]),
                    parameters={'k_period': 14, 'd_period': 3},
                    indicator_type=IndicatorType.MOMENTUM,
                    timestamp=pd.Timestamp.now()
                )
            
            # Williams %R
            if len(high) >= 14:
                williams_r = self._williams_r(high, low, close, 14)
                indicators['williams_r'] = IndicatorResult(
                    name='Williams_R',
                    values=williams_r,
                    parameters={'period': 14},
                    indicator_type=IndicatorType.MOMENTUM,
                    timestamp=pd.Timestamp.now()
                )
            
            # Rate of Change
            if len(close) >= 12:
                roc = self._rate_of_change(close, 12)
                indicators['roc'] = IndicatorResult(
                    name='Rate_of_Change',
                    values=roc,
                    parameters={'period': 12},
                    indicator_type=IndicatorType.MOMENTUM,
                    timestamp=pd.Timestamp.now()
                )
            
            # Momentum
            if len(close) >= 10:
                momentum = self._momentum(close, 10)
                indicators['momentum'] = IndicatorResult(
                    name='Momentum',
                    values=momentum,
                    parameters={'period': 10},
                    indicator_type=IndicatorType.MOMENTUM,
                    timestamp=pd.Timestamp.now()
                )
            
        except Exception as e:
            logger.error(f"Error calculating momentum indicators: {e}")
        
        return indicators
    
    def _calculate_volatility_indicators(self, high: np.ndarray, low: np.ndarray,
                                       close: np.ndarray) -> Dict[str, IndicatorResult]:
        """Calculate volatility indicators."""
        indicators = {}
        
        try:
            # Bollinger Bands
            if len(close) >= 20:
                bb_upper, bb_middle, bb_lower = self._bollinger_bands(close, 20, 2)
                indicators['bollinger_bands'] = IndicatorResult(
                    name='Bollinger_Bands',
                    values=np.column_stack([bb_upper, bb_middle, bb_lower]),
                    parameters={'period': 20, 'std_dev': 2},
                    indicator_type=IndicatorType.VOLATILITY,
                    timestamp=pd.Timestamp.now()
                )
            
            # Average True Range
            if len(high) >= 14:
                atr = self._atr(high, low, close, 14)
                indicators['atr'] = IndicatorResult(
                    name='ATR',
                    values=atr,
                    parameters={'period': 14},
                    indicator_type=IndicatorType.VOLATILITY,
                    timestamp=pd.Timestamp.now()
                )
            
            # Keltner Channels
            if len(high) >= 20:
                kc_upper, kc_middle, kc_lower = self._keltner_channels(high, low, close)
                indicators['keltner_channels'] = IndicatorResult(
                    name='Keltner_Channels',
                    values=np.column_stack([kc_upper, kc_middle, kc_lower]),
                    parameters={'period': 20, 'multiplier': 2},
                    indicator_type=IndicatorType.VOLATILITY,
                    timestamp=pd.Timestamp.now()
                )
            
            # Donchian Channels
            if len(high) >= 20:
                dc_upper, dc_middle, dc_lower = self._donchian_channels(high, low, 20)
                indicators['donchian_channels'] = IndicatorResult(
                    name='Donchian_Channels',
                    values=np.column_stack([dc_upper, dc_middle, dc_lower]),
                    parameters={'period': 20},
                    indicator_type=IndicatorType.VOLATILITY,
                    timestamp=pd.Timestamp.now()
                )
            
        except Exception as e:
            logger.error(f"Error calculating volatility indicators: {e}")
        
        return indicators
    
    def _calculate_volume_indicators(self, close: np.ndarray, volume: np.ndarray) -> Dict[str, IndicatorResult]:
        """Calculate volume-based indicators."""
        indicators = {}
        
        try:
            # On-Balance Volume
            if len(close) >= 2:
                obv = self._obv(close, volume)
                indicators['obv'] = IndicatorResult(
                    name='OBV',
                    values=obv,
                    parameters={},
                    indicator_type=IndicatorType.VOLUME,
                    timestamp=pd.Timestamp.now()
                )
            
            # Volume Moving Average
            if len(volume) >= 20:
                volume_ma = self._sma(volume, 20)
                indicators['volume_ma'] = IndicatorResult(
                    name='Volume_MA',
                    values=volume_ma,
                    parameters={'period': 20},
                    indicator_type=IndicatorType.VOLUME,
                    timestamp=pd.Timestamp.now()
                )
            
            # Volume Rate of Change
            if len(volume) >= 12:
                volume_roc = self._rate_of_change(volume, 12)
                indicators['volume_roc'] = IndicatorResult(
                    name='Volume_ROC',
                    values=volume_roc,
                    parameters={'period': 12},
                    indicator_type=IndicatorType.VOLUME,
                    timestamp=pd.Timestamp.now()
                )
            
        except Exception as e:
            logger.error(f"Error calculating volume indicators: {e}")
        
        return indicators
    
    def _calculate_oscillators(self, high: np.ndarray, low: np.ndarray,
                             close: np.ndarray) -> Dict[str, IndicatorResult]:
        """Calculate oscillator indicators."""
        indicators = {}
        
        try:
            # Commodity Channel Index
            if len(high) >= 20:
                cci = self._cci(high, low, close, 20)
                indicators['cci'] = IndicatorResult(
                    name='CCI',
                    values=cci,
                    parameters={'period': 20},
                    indicator_type=IndicatorType.OSCILLATOR,
                    timestamp=pd.Timestamp.now()
                )
            
            # Average Directional Index
            if len(high) >= 14:
                adx = self._adx(high, low, close, 14)
                indicators['adx'] = IndicatorResult(
                    name='ADX',
                    values=adx,
                    parameters={'period': 14},
                    indicator_type=IndicatorType.OSCILLATOR,
                    timestamp=pd.Timestamp.now()
                )
            
        except Exception as e:
            logger.error(f"Error calculating oscillators: {e}")
        
        return indicators
    
    def _calculate_custom_indicators(self, high: np.ndarray, low: np.ndarray,
                                   close: np.ndarray, volume: np.ndarray) -> Dict[str, IndicatorResult]:
        """Calculate custom indicators."""
        indicators = {}
        
        try:
            # Price Action Strength
            if len(close) >= 20:
                pas = self._price_action_strength(high, low, close)
                indicators['price_action_strength'] = IndicatorResult(
                    name='Price_Action_Strength',
                    values=pas,
                    parameters={'period': 20},
                    indicator_type=IndicatorType.MOMENTUM,
                    timestamp=pd.Timestamp.now()
                )
            
            # Trend Strength Index
            if len(close) >= 25:
                tsi = self._trend_strength_index(close)
                indicators['trend_strength_index'] = IndicatorResult(
                    name='Trend_Strength_Index',
                    values=tsi,
                    parameters={'period': 25},
                    indicator_type=IndicatorType.TREND,
                    timestamp=pd.Timestamp.now()
                )
            
        except Exception as e:
            logger.error(f"Error calculating custom indicators: {e}")
        
        return indicators
    
    # Core indicator calculation methods
    def _sma(self, data: np.ndarray, period: int) -> np.ndarray:
        """Simple Moving Average."""
        return pd.Series(data).rolling(window=period).mean().values
    
    def _ema(self, data: np.ndarray, period: int) -> np.ndarray:
        """Exponential Moving Average."""
        return pd.Series(data).ewm(span=period).mean().values
    
    def _rsi(self, data: np.ndarray, period: int = 14) -> np.ndarray:
        """Relative Strength Index."""
        delta = pd.Series(data).diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss
        rsi = 100 - (100 / (1 + rs))
        return rsi.values
    
    def _macd(self, data: np.ndarray, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """MACD indicator."""
        ema_fast = self._ema(data, fast)
        ema_slow = self._ema(data, slow)
        macd_line = ema_fast - ema_slow
        signal_line = self._ema(macd_line, signal)
        histogram = macd_line - signal_line
        return macd_line, signal_line, histogram
    
    def _bollinger_bands(self, data: np.ndarray, period: int = 20, std_dev: float = 2) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Bollinger Bands."""
        sma = self._sma(data, period)
        std = pd.Series(data).rolling(window=period).std().values
        upper = sma + (std * std_dev)
        lower = sma - (std * std_dev)
        return upper, sma, lower
    
    def _stochastic(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, 
                   k_period: int = 14, d_period: int = 3) -> Tuple[np.ndarray, np.ndarray]:
        """Stochastic Oscillator."""
        high_series = pd.Series(high)
        low_series = pd.Series(low)
        close_series = pd.Series(close)
        
        lowest_low = low_series.rolling(window=k_period).min()
        highest_high = high_series.rolling(window=k_period).max()
        
        k_percent = 100 * ((close_series - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        
        return k_percent.values, d_percent.values
    
    def _atr(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Average True Range."""
        high_series = pd.Series(high)
        low_series = pd.Series(low)
        close_series = pd.Series(close)
        
        tr1 = high_series - low_series
        tr2 = abs(high_series - close_series.shift())
        tr3 = abs(low_series - close_series.shift())
        
        true_range = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
        atr = true_range.rolling(window=period).mean()
        
        return atr.values
    
    def _williams_r(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Williams %R."""
        high_series = pd.Series(high)
        low_series = pd.Series(low)
        close_series = pd.Series(close)
        
        highest_high = high_series.rolling(window=period).max()
        lowest_low = low_series.rolling(window=period).min()
        
        williams_r = -100 * ((highest_high - close_series) / (highest_high - lowest_low))
        
        return williams_r.values
    
    def _obv(self, close: np.ndarray, volume: np.ndarray) -> np.ndarray:
        """On-Balance Volume."""
        obv = np.zeros(len(close))
        obv[0] = volume[0]
        
        for i in range(1, len(close)):
            if close[i] > close[i-1]:
                obv[i] = obv[i-1] + volume[i]
            elif close[i] < close[i-1]:
                obv[i] = obv[i-1] - volume[i]
            else:
                obv[i] = obv[i-1]
        
        return obv
    
    def _cci(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 20) -> np.ndarray:
        """Commodity Channel Index."""
        high_series = pd.Series(high)
        low_series = pd.Series(low)
        close_series = pd.Series(close)
        
        typical_price = (high_series + low_series + close_series) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mean_deviation = typical_price.rolling(window=period).apply(lambda x: abs(x - x.mean()).mean())
        
        cci = (typical_price - sma_tp) / (0.015 * mean_deviation)
        
        return cci.values
    
    def _adx(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 14) -> np.ndarray:
        """Average Directional Index."""
        high_series = pd.Series(high)
        low_series = pd.Series(low)
        close_series = pd.Series(close)
        
        plus_dm = high_series.diff()
        minus_dm = low_series.diff()
        
        plus_dm[plus_dm < 0] = 0
        minus_dm[minus_dm > 0] = 0
        minus_dm = abs(minus_dm)
        
        atr = self._atr(high, low, close, period)
        plus_di = 100 * (plus_dm.rolling(window=period).mean() / atr)
        minus_di = 100 * (minus_dm.rolling(window=period).mean() / atr)
        
        dx = 100 * abs(plus_di - minus_di) / (plus_di + minus_di)
        adx = dx.rolling(window=period).mean()
        
        return adx.values
    
    def _parabolic_sar(self, high: np.ndarray, low: np.ndarray, acceleration: float = 0.02, maximum: float = 0.2) -> np.ndarray:
        """Parabolic SAR."""
        sar = np.zeros(len(high))
        trend = np.ones(len(high))
        af = acceleration
        ep = high[0]
        
        sar[0] = low[0]
        
        for i in range(1, len(high)):
            if trend[i-1] == 1:
                sar[i] = sar[i-1] + af * (ep - sar[i-1])
                if high[i] > ep:
                    ep = high[i]
                    af = min(af + acceleration, maximum)
                if low[i] <= sar[i]:
                    trend[i] = -1
                    sar[i] = ep
                    ep = low[i]
                    af = acceleration
                else:
                    trend[i] = 1
            else:
                sar[i] = sar[i-1] + af * (ep - sar[i-1])
                if low[i] < ep:
                    ep = low[i]
                    af = min(af + acceleration, maximum)
                if high[i] >= sar[i]:
                    trend[i] = 1
                    sar[i] = ep
                    ep = high[i]
                    af = acceleration
                else:
                    trend[i] = -1
        
        return sar
    
    def _ichimoku(self, high: np.ndarray, low: np.ndarray, close: np.ndarray) -> Tuple[np.ndarray, np.ndarray, np.ndarray, np.ndarray, np.ndarray]:
        """Ichimoku Cloud."""
        high_series = pd.Series(high)
        low_series = pd.Series(low)
        close_series = pd.Series(close)
        
        # Tenkan-sen (9-period)
        tenkan_high = high_series.rolling(window=9).max()
        tenkan_low = low_series.rolling(window=9).min()
        tenkan = (tenkan_high + tenkan_low) / 2
        
        # Kijun-sen (26-period)
        kijun_high = high_series.rolling(window=26).max()
        kijun_low = low_series.rolling(window=26).min()
        kijun = (kijun_high + kijun_low) / 2
        
        # Senkou Span A
        senkou_a = ((tenkan + kijun) / 2).shift(26)
        
        # Senkou Span B
        senkou_high = high_series.rolling(window=52).max()
        senkou_low = low_series.rolling(window=52).min()
        senkou_b = ((senkou_high + senkou_low) / 2).shift(26)
        
        # Chikou Span
        chikou = close_series.shift(-26)
        
        return tenkan.values, kijun.values, senkou_a.values, senkou_b.values, chikou.values
    
    def _keltner_channels(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 20, multiplier: float = 2) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Keltner Channels."""
        middle = self._ema(close, period)
        atr = self._atr(high, low, close, period)
        upper = middle + (multiplier * atr)
        lower = middle - (multiplier * atr)
        return upper, middle, lower
    
    def _donchian_channels(self, high: np.ndarray, low: np.ndarray, period: int = 20) -> Tuple[np.ndarray, np.ndarray, np.ndarray]:
        """Donchian Channels."""
        high_series = pd.Series(high)
        low_series = pd.Series(low)
        
        upper = high_series.rolling(window=period).max().values
        lower = low_series.rolling(window=period).min().values
        middle = (upper + lower) / 2
        
        return upper, middle, lower
    
    def _rate_of_change(self, data: np.ndarray, period: int) -> np.ndarray:
        """Rate of Change."""
        data_series = pd.Series(data)
        roc = ((data_series - data_series.shift(period)) / data_series.shift(period)) * 100
        return roc.values
    
    def _momentum(self, data: np.ndarray, period: int) -> np.ndarray:
        """Momentum."""
        data_series = pd.Series(data)
        momentum = data_series - data_series.shift(period)
        return momentum.values
    
    def _price_action_strength(self, high: np.ndarray, low: np.ndarray, close: np.ndarray, period: int = 20) -> np.ndarray:
        """Custom Price Action Strength indicator."""
        high_series = pd.Series(high)
        low_series = pd.Series(low)
        close_series = pd.Series(close)
        
        # Calculate price range and position within range
        price_range = high_series - low_series
        close_position = (close_series - low_series) / price_range
        
        # Smooth the position
        pas = close_position.rolling(window=period).mean() * 100
        
        return pas.values
    
    def _trend_strength_index(self, close: np.ndarray, period: int = 25) -> np.ndarray:
        """Custom Trend Strength Index."""
        close_series = pd.Series(close)
        
        # Calculate price momentum
        momentum = close_series.diff()
        
        # Calculate positive and negative momentum
        positive_momentum = momentum.where(momentum > 0, 0).rolling(window=period).sum()
        negative_momentum = abs(momentum.where(momentum < 0, 0)).rolling(window=period).sum()
        
        # Calculate trend strength
        total_momentum = positive_momentum + negative_momentum
        tsi = ((positive_momentum - negative_momentum) / total_momentum) * 100
        
        return tsi.values
    
    def get_indicator_summary(self, indicators: Dict[str, IndicatorResult]) -> Dict[str, Any]:
        """Get summary of all calculated indicators."""
        summary = {
            'total_indicators': len(indicators),
            'by_type': {},
            'latest_values': {},
            'signals': []
        }
        
        # Group by type
        for name, indicator in indicators.items():
            indicator_type = indicator.indicator_type.value
            if indicator_type not in summary['by_type']:
                summary['by_type'][indicator_type] = []
            summary['by_type'][indicator_type].append(name)
            
            # Get latest value
            if len(indicator.values) > 0:
                if indicator.values.ndim == 1:
                    latest_value = indicator.values[-1] if not np.isnan(indicator.values[-1]) else None
                else:
                    latest_value = indicator.values[-1].tolist() if not np.isnan(indicator.values[-1]).any() else None
                summary['latest_values'][name] = latest_value
        
        return summary