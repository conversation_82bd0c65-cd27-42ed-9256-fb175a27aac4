#!/usr/bin/env python3
"""
Elliott Wave Analysis
Advanced Elliott Wave pattern recognition and analysis.
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Union, Any
from dataclasses import dataclass
from enum import Enum
import logging
from scipy import signal
from scipy.stats import linregress

logger = logging.getLogger(__name__)

class WaveType(Enum):
    """Elliott Wave types."""
    IMPULSE = "impulse"
    CORRECTIVE = "corrective"
    DIAGONAL = "diagonal"
    TRIANGLE = "triangle"
    FLAT = "flat"
    ZIGZAG = "zigzag"
    COMPLEX = "complex"

class WaveDegree(Enum):
    """Wave degree classification."""
    GRAND_SUPERCYCLE = "grand_supercycle"
    SUPERCYCLE = "supercycle"
    CYCLE = "cycle"
    PRIMARY = "primary"
    INTERMEDIATE = "intermediate"
    MINOR = "minor"
    MINUTE = "minute"
    MINUETTE = "minuette"
    SUBMINUETTE = "subminuette"

class WaveDirection(Enum):
    """Wave direction."""
    UP = "up"
    DOWN = "down"
    SIDEWAYS = "sideways"

@dataclass
class WavePoint:
    """Elliott Wave pivot point."""
    index: int
    price: float
    wave_type: str
    degree: WaveDegree
    confidence: float

@dataclass
class WavePattern:
    """Elliott Wave pattern."""
    name: str
    wave_type: WaveType
    direction: WaveDirection
    degree: WaveDegree
    start_index: int
    end_index: int
    waves: List[WavePoint]
    confidence: float
    target_levels: List[float]
    invalidation_level: float
    metadata: Dict = None

class ElliottWaveAnalyzer:
    """Elliott Wave analysis system."""
    
    def __init__(self):
        self.waves = []
        self.patterns = []
        self.pivot_points = []
        self.min_wave_length = 5
        self.fibonacci_ratios = [0.236, 0.382, 0.5, 0.618, 0.786, 1.0, 1.272, 1.618, 2.618]
    
    def analyze_waves(self, data: pd.DataFrame, symbol: str = None) -> Dict[str, Any]:
        """Analyze Elliott Wave patterns in the given data."""
        try:
            if data.empty or len(data) < self.min_wave_length * 5:
                logger.warning(f"Insufficient data for wave analysis: {len(data)} rows")
                return {}
            
            # Extract price data
            high = data['high'].values if 'high' in data.columns else data['close'].values
            low = data['low'].values if 'low' in data.columns else data['close'].values
            close = data['close'].values
            
            # Find pivot points
            self.pivot_points = self._find_pivot_points(high, low, close)
            
            # Identify wave patterns
            impulse_waves = self._identify_impulse_waves()
            corrective_waves = self._identify_corrective_waves()
            
            # Combine all patterns
            all_patterns = impulse_waves + corrective_waves
            
            # Calculate wave projections
            projections = self._calculate_wave_projections(all_patterns)
            
            # Generate wave count
            wave_count = self._generate_wave_count()
            
            return {
                'patterns': all_patterns,
                'projections': projections,
                'wave_count': wave_count,
                'pivot_points': self.pivot_points,
                'current_position': self._determine_current_position(),
                'next_targets': self._calculate_next_targets()
            }
            
        except Exception as e:
            logger.error(f"Error analyzing waves: {e}")
            return {}
    
    def _find_pivot_points(self, high: np.ndarray, low: np.ndarray, 
                          close: np.ndarray) -> List[WavePoint]:
        """Find significant pivot points for wave analysis."""
        pivot_points = []
        
        try:
            # Find peaks and troughs
            peaks = signal.find_peaks(high, distance=self.min_wave_length)[0]
            troughs = signal.find_peaks(-low, distance=self.min_wave_length)[0]
            
            # Combine and sort all pivots
            all_pivots = []
            
            for peak in peaks:
                all_pivots.append({
                    'index': peak,
                    'price': high[peak],
                    'type': 'peak'
                })
            
            for trough in troughs:
                all_pivots.append({
                    'index': trough,
                    'price': low[trough],
                    'type': 'trough'
                })
            
            # Sort by index
            all_pivots.sort(key=lambda x: x['index'])
            
            # Filter significant pivots
            significant_pivots = self._filter_significant_pivots(all_pivots, close)
            
            # Convert to WavePoint objects
            for i, pivot in enumerate(significant_pivots):
                confidence = self._calculate_pivot_confidence(pivot, significant_pivots, i)
                
                pivot_points.append(WavePoint(
                    index=pivot['index'],
                    price=pivot['price'],
                    wave_type=pivot['type'],
                    degree=self._determine_wave_degree(pivot, significant_pivots),
                    confidence=confidence
                ))
            
        except Exception as e:
            logger.error(f"Error finding pivot points: {e}")
        
        return pivot_points
    
    def _filter_significant_pivots(self, pivots: List[Dict], close: np.ndarray) -> List[Dict]:
        """Filter out insignificant pivot points."""
        if len(pivots) < 3:
            return pivots
        
        significant = [pivots[0]]  # Always include first pivot
        
        for i in range(1, len(pivots) - 1):
            current = pivots[i]
            prev = significant[-1]
            next_pivot = pivots[i + 1]
            
            # Calculate price change significance
            price_change_prev = abs(current['price'] - prev['price']) / prev['price']
            price_change_next = abs(next_pivot['price'] - current['price']) / current['price']
            
            # Include pivot if price change is significant (> 2%)
            if price_change_prev > 0.02 or price_change_next > 0.02:
                significant.append(current)
        
        significant.append(pivots[-1])  # Always include last pivot
        
        return significant
    
    def _calculate_pivot_confidence(self, pivot: Dict, all_pivots: List[Dict], index: int) -> float:
        """Calculate confidence score for a pivot point."""
        try:
            base_confidence = 0.5
            
            # Increase confidence based on price significance
            if index > 0 and index < len(all_pivots) - 1:
                prev_pivot = all_pivots[index - 1]
                next_pivot = all_pivots[index + 1]
                
                price_change = max(
                    abs(pivot['price'] - prev_pivot['price']) / prev_pivot['price'],
                    abs(next_pivot['price'] - pivot['price']) / pivot['price']
                )
                
                confidence_boost = min(0.4, price_change * 10)
                base_confidence += confidence_boost
            
            return min(1.0, base_confidence)
            
        except Exception as e:
            logger.error(f"Error calculating pivot confidence: {e}")
            return 0.5
    
    def _determine_wave_degree(self, pivot: Dict, all_pivots: List[Dict]) -> WaveDegree:
        """Determine the degree of a wave based on its significance."""
        try:
            # Simple degree classification based on price movement
            max_price_change = 0
            
            for other_pivot in all_pivots:
                if other_pivot['index'] != pivot['index']:
                    price_change = abs(pivot['price'] - other_pivot['price']) / min(pivot['price'], other_pivot['price'])
                    max_price_change = max(max_price_change, price_change)
            
            if max_price_change > 0.5:
                return WaveDegree.PRIMARY
            elif max_price_change > 0.2:
                return WaveDegree.INTERMEDIATE
            elif max_price_change > 0.1:
                return WaveDegree.MINOR
            elif max_price_change > 0.05:
                return WaveDegree.MINUTE
            else:
                return WaveDegree.MINUETTE
                
        except Exception as e:
            logger.error(f"Error determining wave degree: {e}")
            return WaveDegree.MINUTE
    
    def _identify_impulse_waves(self) -> List[WavePattern]:
        """Identify 5-wave impulse patterns."""
        patterns = []
        
        try:
            if len(self.pivot_points) < 6:  # Need at least 6 points for 5-wave pattern
                return patterns
            
            # Look for 5-wave patterns
            for i in range(len(self.pivot_points) - 5):
                wave_points = self.pivot_points[i:i+6]
                
                # Check if it forms a valid 5-wave pattern
                if self._is_valid_impulse_pattern(wave_points):
                    direction = self._determine_pattern_direction(wave_points)
                    confidence = self._calculate_pattern_confidence(wave_points)
                    
                    pattern = WavePattern(
                        name="Impulse_5_Wave",
                        wave_type=WaveType.IMPULSE,
                        direction=direction,
                        degree=wave_points[0].degree,
                        start_index=wave_points[0].index,
                        end_index=wave_points[-1].index,
                        waves=wave_points,
                        confidence=confidence,
                        target_levels=self._calculate_impulse_targets(wave_points),
                        invalidation_level=self._calculate_invalidation_level(wave_points)
                    )
                    
                    patterns.append(pattern)
            
        except Exception as e:
            logger.error(f"Error identifying impulse waves: {e}")
        
        return patterns
    
    def _identify_corrective_waves(self) -> List[WavePattern]:
        """Identify corrective wave patterns."""
        patterns = []
        
        try:
            # Look for ABC corrective patterns
            patterns.extend(self._find_abc_corrections())
            
            # Look for triangle patterns
            patterns.extend(self._find_triangle_patterns())
            
            # Look for flat patterns
            patterns.extend(self._find_flat_patterns())
            
        except Exception as e:
            logger.error(f"Error identifying corrective waves: {e}")
        
        return patterns
    
    def _is_valid_impulse_pattern(self, wave_points: List[WavePoint]) -> bool:
        """Check if wave points form a valid 5-wave impulse pattern."""
        try:
            if len(wave_points) != 6:
                return False
            
            # Basic Elliott Wave rules for impulse patterns
            # Wave 2 cannot retrace more than 100% of wave 1
            wave1_size = abs(wave_points[1].price - wave_points[0].price)
            wave2_size = abs(wave_points[2].price - wave_points[1].price)
            
            if wave2_size > wave1_size:
                return False
            
            # Wave 4 cannot overlap with wave 1 (in most cases)
            if self._waves_overlap(wave_points[0], wave_points[1], wave_points[3], wave_points[4]):
                return False
            
            # Wave 3 cannot be the shortest wave
            wave3_size = abs(wave_points[3].price - wave_points[2].price)
            wave5_size = abs(wave_points[5].price - wave_points[4].price)
            
            if wave3_size < wave1_size and wave3_size < wave5_size:
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"Error validating impulse pattern: {e}")
            return False
    
    def _waves_overlap(self, wave1_start: WavePoint, wave1_end: WavePoint,
                      wave4_start: WavePoint, wave4_end: WavePoint) -> bool:
        """Check if wave 1 and wave 4 overlap."""
        try:
            wave1_high = max(wave1_start.price, wave1_end.price)
            wave1_low = min(wave1_start.price, wave1_end.price)
            wave4_high = max(wave4_start.price, wave4_end.price)
            wave4_low = min(wave4_start.price, wave4_end.price)
            
            # Check for overlap
            return not (wave4_high < wave1_low or wave4_low > wave1_high)
            
        except Exception as e:
            logger.error(f"Error checking wave overlap: {e}")
            return False
    
    def _determine_pattern_direction(self, wave_points: List[WavePoint]) -> WaveDirection:
        """Determine the overall direction of a wave pattern."""
        try:
            start_price = wave_points[0].price
            end_price = wave_points[-1].price
            
            price_change = (end_price - start_price) / start_price
            
            if price_change > 0.02:
                return WaveDirection.UP
            elif price_change < -0.02:
                return WaveDirection.DOWN
            else:
                return WaveDirection.SIDEWAYS
                
        except Exception as e:
            logger.error(f"Error determining pattern direction: {e}")
            return WaveDirection.SIDEWAYS
    
    def _calculate_pattern_confidence(self, wave_points: List[WavePoint]) -> float:
        """Calculate confidence score for a wave pattern."""
        try:
            # Base confidence from individual wave points
            avg_confidence = np.mean([point.confidence for point in wave_points])
            
            # Adjust based on Fibonacci relationships
            fib_score = self._calculate_fibonacci_score(wave_points)
            
            # Combine scores
            total_confidence = (avg_confidence * 0.6) + (fib_score * 0.4)
            
            return min(1.0, max(0.0, total_confidence))
            
        except Exception as e:
            logger.error(f"Error calculating pattern confidence: {e}")
            return 0.5
    
    def _calculate_fibonacci_score(self, wave_points: List[WavePoint]) -> float:
        """Calculate how well the pattern fits Fibonacci ratios."""
        try:
            if len(wave_points) < 3:
                return 0.5
            
            fib_matches = 0
            total_checks = 0
            
            # Check retracement ratios
            for i in range(1, len(wave_points) - 1):
                if i + 1 < len(wave_points):
                    retracement = self._calculate_retracement_ratio(
                        wave_points[i-1], wave_points[i], wave_points[i+1]
                    )
                    
                    # Check if retracement matches common Fibonacci levels
                    for fib_ratio in self.fibonacci_ratios:
                        if abs(retracement - fib_ratio) < 0.05:  # 5% tolerance
                            fib_matches += 1
                            break
                    
                    total_checks += 1
            
            return fib_matches / total_checks if total_checks > 0 else 0.5
            
        except Exception as e:
            logger.error(f"Error calculating Fibonacci score: {e}")
            return 0.5
    
    def _calculate_retracement_ratio(self, point_a: WavePoint, point_b: WavePoint, 
                                   point_c: WavePoint) -> float:
        """Calculate retracement ratio between three points."""
        try:
            ab_distance = abs(point_b.price - point_a.price)
            bc_distance = abs(point_c.price - point_b.price)
            
            if ab_distance == 0:
                return 0
            
            return bc_distance / ab_distance
            
        except Exception as e:
            logger.error(f"Error calculating retracement ratio: {e}")
            return 0
    
    def _calculate_impulse_targets(self, wave_points: List[WavePoint]) -> List[float]:
        """Calculate target levels for impulse waves."""
        targets = []
        
        try:
            if len(wave_points) >= 4:
                # Calculate wave 1 and 3 for projections
                wave1_size = abs(wave_points[1].price - wave_points[0].price)
                wave3_start = wave_points[2].price
                
                # Common Fibonacci extensions
                for ratio in [1.0, 1.272, 1.618, 2.618]:
                    if wave_points[1].price > wave_points[0].price:  # Uptrend
                        target = wave3_start + (wave1_size * ratio)
                    else:  # Downtrend
                        target = wave3_start - (wave1_size * ratio)
                    
                    targets.append(target)
            
        except Exception as e:
            logger.error(f"Error calculating impulse targets: {e}")
        
        return targets
    
    def _calculate_invalidation_level(self, wave_points: List[WavePoint]) -> float:
        """Calculate invalidation level for the pattern."""
        try:
            if len(wave_points) >= 2:
                # For impulse waves, invalidation is typically at the start of wave 1
                return wave_points[0].price
            return 0.0
            
        except Exception as e:
            logger.error(f"Error calculating invalidation level: {e}")
            return 0.0
    
    def _find_abc_corrections(self) -> List[WavePattern]:
        """Find ABC corrective patterns."""
        patterns = []
        
        try:
            if len(self.pivot_points) < 4:
                return patterns
            
            # Look for 3-wave ABC patterns
            for i in range(len(self.pivot_points) - 3):
                wave_points = self.pivot_points[i:i+4]
                
                if self._is_valid_abc_pattern(wave_points):
                    direction = self._determine_pattern_direction(wave_points)
                    confidence = self._calculate_pattern_confidence(wave_points)
                    
                    pattern = WavePattern(
                        name="ABC_Correction",
                        wave_type=WaveType.CORRECTIVE,
                        direction=direction,
                        degree=wave_points[0].degree,
                        start_index=wave_points[0].index,
                        end_index=wave_points[-1].index,
                        waves=wave_points,
                        confidence=confidence,
                        target_levels=self._calculate_abc_targets(wave_points),
                        invalidation_level=self._calculate_invalidation_level(wave_points)
                    )
                    
                    patterns.append(pattern)
            
        except Exception as e:
            logger.error(f"Error finding ABC corrections: {e}")
        
        return patterns
    
    def _is_valid_abc_pattern(self, wave_points: List[WavePoint]) -> bool:
        """Check if wave points form a valid ABC corrective pattern."""
        try:
            if len(wave_points) != 4:
                return False
            
            # Basic ABC pattern validation
            # Wave B should retrace part of wave A
            wave_a_size = abs(wave_points[1].price - wave_points[0].price)
            wave_b_size = abs(wave_points[2].price - wave_points[1].price)
            
            # Wave B typically retraces 38.2% to 78.6% of wave A
            retracement_ratio = wave_b_size / wave_a_size
            
            return 0.3 <= retracement_ratio <= 0.8
            
        except Exception as e:
            logger.error(f"Error validating ABC pattern: {e}")
            return False
    
    def _calculate_abc_targets(self, wave_points: List[WavePoint]) -> List[float]:
        """Calculate target levels for ABC corrections."""
        targets = []
        
        try:
            if len(wave_points) >= 3:
                wave_a_size = abs(wave_points[1].price - wave_points[0].price)
                wave_c_start = wave_points[2].price
                
                # Common Fibonacci projections for wave C
                for ratio in [0.618, 1.0, 1.272, 1.618]:
                    if wave_points[1].price < wave_points[0].price:  # Downward correction
                        target = wave_c_start - (wave_a_size * ratio)
                    else:  # Upward correction
                        target = wave_c_start + (wave_a_size * ratio)
                    
                    targets.append(target)
            
        except Exception as e:
            logger.error(f"Error calculating ABC targets: {e}")
        
        return targets
    
    def _find_triangle_patterns(self) -> List[WavePattern]:
        """Find triangle corrective patterns."""
        patterns = []
        
        try:
            # Look for 5-wave triangle patterns (ABCDE)
            if len(self.pivot_points) >= 6:
                for i in range(len(self.pivot_points) - 5):
                    wave_points = self.pivot_points[i:i+6]
                    
                    if self._is_triangle_pattern(wave_points):
                        pattern = WavePattern(
                            name="Triangle_Correction",
                            wave_type=WaveType.TRIANGLE,
                            direction=WaveDirection.SIDEWAYS,
                            degree=wave_points[0].degree,
                            start_index=wave_points[0].index,
                            end_index=wave_points[-1].index,
                            waves=wave_points,
                            confidence=0.7,
                            target_levels=[],
                            invalidation_level=0.0
                        )
                        
                        patterns.append(pattern)
            
        except Exception as e:
            logger.error(f"Error finding triangle patterns: {e}")
        
        return patterns
    
    def _is_triangle_pattern(self, wave_points: List[WavePoint]) -> bool:
        """Check if wave points form a triangle pattern."""
        try:
            if len(wave_points) != 6:
                return False
            
            # Check if waves are getting smaller (contracting triangle)
            wave_sizes = []
            for i in range(len(wave_points) - 1):
                size = abs(wave_points[i+1].price - wave_points[i].price)
                wave_sizes.append(size)
            
            # Check if generally contracting
            contracting = True
            for i in range(1, len(wave_sizes)):
                if wave_sizes[i] > wave_sizes[i-1] * 1.2:  # Allow some tolerance
                    contracting = False
                    break
            
            return contracting
            
        except Exception as e:
            logger.error(f"Error checking triangle pattern: {e}")
            return False
    
    def _find_flat_patterns(self) -> List[WavePattern]:
        """Find flat corrective patterns."""
        patterns = []
        
        try:
            # Look for ABC flat patterns where A and C are similar in size
            if len(self.pivot_points) >= 4:
                for i in range(len(self.pivot_points) - 3):
                    wave_points = self.pivot_points[i:i+4]
                    
                    if self._is_flat_pattern(wave_points):
                        pattern = WavePattern(
                            name="Flat_Correction",
                            wave_type=WaveType.FLAT,
                            direction=WaveDirection.SIDEWAYS,
                            degree=wave_points[0].degree,
                            start_index=wave_points[0].index,
                            end_index=wave_points[-1].index,
                            waves=wave_points,
                            confidence=0.65,
                            target_levels=[],
                            invalidation_level=0.0
                        )
                        
                        patterns.append(pattern)
            
        except Exception as e:
            logger.error(f"Error finding flat patterns: {e}")
        
        return patterns
    
    def _is_flat_pattern(self, wave_points: List[WavePoint]) -> bool:
        """Check if wave points form a flat pattern."""
        try:
            if len(wave_points) != 4:
                return False
            
            # In a flat, wave B retraces most of wave A (90%+)
            wave_a_size = abs(wave_points[1].price - wave_points[0].price)
            wave_b_size = abs(wave_points[2].price - wave_points[1].price)
            
            retracement_ratio = wave_b_size / wave_a_size
            
            return retracement_ratio >= 0.9
            
        except Exception as e:
            logger.error(f"Error checking flat pattern: {e}")
            return False
    
    def _calculate_wave_projections(self, patterns: List[WavePattern]) -> List[Dict]:
        """Calculate wave projections based on identified patterns."""
        projections = []
        
        try:
            for pattern in patterns:
                if pattern.wave_type == WaveType.IMPULSE:
                    # Project potential wave 5 targets
                    for target in pattern.target_levels:
                        projections.append({
                            'type': 'impulse_target',
                            'price': target,
                            'confidence': pattern.confidence,
                            'pattern_name': pattern.name
                        })
                
                elif pattern.wave_type == WaveType.CORRECTIVE:
                    # Project correction completion levels
                    for target in pattern.target_levels:
                        projections.append({
                            'type': 'correction_target',
                            'price': target,
                            'confidence': pattern.confidence * 0.8,  # Lower confidence for corrections
                            'pattern_name': pattern.name
                        })
            
        except Exception as e:
            logger.error(f"Error calculating wave projections: {e}")
        
        return projections
    
    def _generate_wave_count(self) -> Dict[str, Any]:
        """Generate current wave count analysis."""
        try:
            if not self.pivot_points:
                return {}
            
            # Simplified wave count - in practice this would be much more complex
            recent_points = self.pivot_points[-5:] if len(self.pivot_points) >= 5 else self.pivot_points
            
            return {
                'current_wave': self._determine_current_wave_position(),
                'wave_degree': recent_points[-1].degree.value if recent_points else 'unknown',
                'trend_direction': self._analyze_trend_direction(),
                'completion_percentage': self._estimate_wave_completion()
            }
            
        except Exception as e:
            logger.error(f"Error generating wave count: {e}")
            return {}
    
    def _determine_current_wave_position(self) -> str:
        """Determine current position in wave cycle."""
        try:
            if len(self.pivot_points) < 2:
                return "unknown"
            
            # Simplified logic - would be much more sophisticated in practice
            recent_trend = self.pivot_points[-1].price - self.pivot_points[-2].price
            
            if recent_trend > 0:
                return "potential_wave_3_or_5"
            else:
                return "potential_wave_2_or_4_or_A_or_C"
                
        except Exception as e:
            logger.error(f"Error determining current wave position: {e}")
            return "unknown"
    
    def _analyze_trend_direction(self) -> str:
        """Analyze overall trend direction."""
        try:
            if len(self.pivot_points) < 3:
                return "unknown"
            
            # Look at last 3 pivot points
            recent_points = self.pivot_points[-3:]
            
            if recent_points[-1].price > recent_points[0].price:
                return "uptrend"
            elif recent_points[-1].price < recent_points[0].price:
                return "downtrend"
            else:
                return "sideways"
                
        except Exception as e:
            logger.error(f"Error analyzing trend direction: {e}")
            return "unknown"
    
    def _estimate_wave_completion(self) -> float:
        """Estimate completion percentage of current wave."""
        try:
            # Simplified estimation - would use more sophisticated methods in practice
            return 0.5  # 50% as default estimate
            
        except Exception as e:
            logger.error(f"Error estimating wave completion: {e}")
            return 0.0
    
    def _determine_current_position(self) -> Dict[str, Any]:
        """Determine current market position in wave structure."""
        try:
            return {
                'wave_position': self._determine_current_wave_position(),
                'trend_status': self._analyze_trend_direction(),
                'next_expected': self._predict_next_wave_type()
            }
            
        except Exception as e:
            logger.error(f"Error determining current position: {e}")
            return {}
    
    def _predict_next_wave_type(self) -> str:
        """Predict the type of next expected wave."""
        try:
            current_position = self._determine_current_wave_position()
            
            if "wave_3" in current_position:
                return "corrective_wave_4"
            elif "wave_5" in current_position:
                return "corrective_ABC"
            elif "wave_2" in current_position or "wave_4" in current_position:
                return "impulse_continuation"
            else:
                return "unknown"
                
        except Exception as e:
            logger.error(f"Error predicting next wave type: {e}")
            return "unknown"
    
    def _calculate_next_targets(self) -> List[Dict]:
        """Calculate next potential target levels."""
        targets = []
        
        try:
            if len(self.pivot_points) >= 2:
                last_point = self.pivot_points[-1]
                prev_point = self.pivot_points[-2]
                
                # Calculate potential Fibonacci targets
                price_diff = abs(last_point.price - prev_point.price)
                
                for ratio in [1.272, 1.618, 2.618]:
                    if last_point.price > prev_point.price:
                        target_price = last_point.price + (price_diff * ratio)
                    else:
                        target_price = last_point.price - (price_diff * ratio)
                    
                    targets.append({
                        'price': target_price,
                        'type': f'fibonacci_{ratio}',
                        'confidence': 0.6
                    })
            
        except Exception as e:
            logger.error(f"Error calculating next targets: {e}")
        
        return targets
    
    def get_wave_summary(self, analysis_result: Dict[str, Any]) -> Dict[str, Any]:
        """Get summary of wave analysis."""
        try:
            patterns = analysis_result.get('patterns', [])
            projections = analysis_result.get('projections', [])
            wave_count = analysis_result.get('wave_count', {})
            
            return {
                'total_patterns': len(patterns),
                'impulse_patterns': len([p for p in patterns if p.wave_type == WaveType.IMPULSE]),
                'corrective_patterns': len([p for p in patterns if p.wave_type == WaveType.CORRECTIVE]),
                'high_confidence_patterns': len([p for p in patterns if p.confidence > 0.8]),
                'current_wave_count': wave_count,
                'next_targets': len(projections),
                'trend_direction': wave_count.get('trend_direction', 'unknown')
            }
            
        except Exception as e:
            logger.error(f"Error creating wave summary: {e}")
            return {}