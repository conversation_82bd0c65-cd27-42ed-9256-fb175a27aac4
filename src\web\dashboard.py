#!/usr/bin/env python3
"""
Noryon V2 - Real-time Trading Dashboard
Web interface for monitoring realistic trading simulation

This module provides:
- Real-time trading dashboard
- Live market data visualization
- Agent performance monitoring
- System health monitoring
- Interactive controls
- Data export capabilities
"""

import asyncio
import json
import logging
from datetime import datetime, timezone
from typing import Dict, List, Any, Optional
from decimal import Decimal

from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from fastapi.responses import HTMLResponse, JSONResponse
from fastapi.middleware.cors import CORSMiddleware
import uvicorn

from src.core.config import Config
from src.core.logger import get_logger
from src.services.realistic_trading_simulator import RealisticTradingSimulator

logger = get_logger(__name__)

class TradingDashboard:
    """
    Real-time web dashboard for trading simulation monitoring
    """
    
    def __init__(self, config: Config, simulator: RealisticTradingSimulator):
        self.config = config
        self.simulator = simulator
        self.logger = get_logger(__name__)
        
        # FastAPI app
        self.app = FastAPI(
            title="Noryon V2 Trading Dashboard",
            description="Real-time monitoring dashboard for AI trading simulation",
            version="2.0.0"
        )
        
        # CORS middleware
        self.app.add_middleware(
            CORSMiddleware,
            allow_origins=["*"],
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )
        
        # Templates and static files
        self.templates = Jinja2Templates(directory="src/web/templates")
        
        # WebSocket connections
        self.active_connections: List[WebSocket] = []
        
        # Dashboard state
        self.dashboard_state = {
            'connected_clients': 0,
            'last_update': None,
            'update_frequency': 1.0  # seconds
        }
        
        # Setup routes
        self._setup_routes()
        
        # Background tasks
        self.broadcast_task = None
    
    def _setup_routes(self):
        """Setup FastAPI routes"""
        
        @self.app.get("/", response_class=HTMLResponse)
        async def dashboard_home(request: Request):
            """Main dashboard page"""
            return self.templates.TemplateResponse(
                "dashboard.html", 
                {
                    "request": request,
                    "title": "Noryon V2 Trading Dashboard",
                    "simulation_id": self.simulator.simulation_id or "Not Started"
                }
            )
        
        @self.app.get("/api/status")
        async def get_status():
            """Get current simulation status"""
            try:
                return JSONResponse({
                    "status": "success",
                    "data": {
                        "simulation_running": self.simulator.running,
                        "simulation_id": self.simulator.simulation_id,
                        "system_status": self.simulator.system_status.__dict__,
                        "connected_clients": len(self.active_connections),
                        "last_update": self.dashboard_state['last_update']
                    }
                })
            except Exception as e:
                return JSONResponse({
                    "status": "error",
                    "message": str(e)
                }, status_code=500)
        
        @self.app.get("/api/dashboard-data")
        async def get_dashboard_data():
            """Get complete dashboard data"""
            try:
                return JSONResponse({
                    "status": "success",
                    "data": self.simulator.get_dashboard_data()
                })
            except Exception as e:
                return JSONResponse({
                    "status": "error",
                    "message": str(e)
                }, status_code=500)
        
        @self.app.get("/api/simulation-summary")
        async def get_simulation_summary():
            """Get comprehensive simulation summary"""
            try:
                return JSONResponse({
                    "status": "success",
                    "data": self.simulator.get_simulation_summary()
                })
            except Exception as e:
                return JSONResponse({
                    "status": "error",
                    "message": str(e)
                }, status_code=500)
        
        @self.app.post("/api/simulation/start")
        async def start_simulation():
            """Start the trading simulation"""
            try:
                if not self.simulator.running:
                    await self.simulator.start()
                    return JSONResponse({
                        "status": "success",
                        "message": "Simulation started successfully",
                        "simulation_id": self.simulator.simulation_id
                    })
                else:
                    return JSONResponse({
                        "status": "warning",
                        "message": "Simulation is already running"
                    })
            except Exception as e:
                return JSONResponse({
                    "status": "error",
                    "message": f"Failed to start simulation: {str(e)}"
                }, status_code=500)
        
        @self.app.post("/api/simulation/stop")
        async def stop_simulation():
            """Stop the trading simulation"""
            try:
                if self.simulator.running:
                    await self.simulator.stop()
                    return JSONResponse({
                        "status": "success",
                        "message": "Simulation stopped successfully"
                    })
                else:
                    return JSONResponse({
                        "status": "warning",
                        "message": "Simulation is not running"
                    })
            except Exception as e:
                return JSONResponse({
                    "status": "error",
                    "message": f"Failed to stop simulation: {str(e)}"
                }, status_code=500)
        
        @self.app.get("/api/agents")
        async def get_agents():
            """Get agent information"""
            try:
                return JSONResponse({
                    "status": "success",
                    "data": {
                        "agents": self.simulator.agent_coordinator.get_agent_summary(),
                        "coordination_stats": self.simulator.agent_coordinator.get_coordination_statistics()
                    }
                })
            except Exception as e:
                return JSONResponse({
                    "status": "error",
                    "message": str(e)
                }, status_code=500)
        
        @self.app.get("/api/market-data")
        async def get_market_data():
            """Get current market data"""
            try:
                market_stats = {}
                if hasattr(self.simulator.market_feed, 'get_market_statistics'):
                    market_stats = self.simulator.market_feed.get_market_statistics()
                
                return JSONResponse({
                    "status": "success",
                    "data": {
                        "current_prices": {k: float(v) for k, v in self.simulator.trading_env.current_prices.items()},
                        "market_statistics": market_stats,
                        "symbols": self.simulator.trading_env.symbols
                    }
                })
            except Exception as e:
                return JSONResponse({
                    "status": "error",
                    "message": str(e)
                }, status_code=500)
        
        @self.app.get("/api/orders")
        async def get_orders(limit: int = 100):
            """Get recent orders"""
            try:
                orders = list(self.simulator.trading_env.orders.values())[-limit:]
                order_data = []
                
                for order in orders:
                    order_data.append({
                        "id": order.id,
                        "symbol": order.symbol,
                        "side": order.side.value,
                        "type": order.type.value,
                        "quantity": float(order.quantity),
                        "price": float(order.price) if order.price else None,
                        "status": order.status.value,
                        "agent_id": order.agent_id,
                        "timestamp": order.timestamp.isoformat() if order.timestamp else None,
                        "filled_quantity": float(order.filled_quantity),
                        "remaining_quantity": float(order.remaining_quantity)
                    })
                
                return JSONResponse({
                    "status": "success",
                    "data": {
                        "orders": order_data,
                        "total_orders": len(self.simulator.trading_env.orders)
                    }
                })
            except Exception as e:
                return JSONResponse({
                    "status": "error",
                    "message": str(e)
                }, status_code=500)
        
        @self.app.get("/api/trades")
        async def get_trades(limit: int = 100):
            """Get recent trades"""
            try:
                trades = list(self.simulator.trading_env.trades.values())[-limit:]
                trade_data = []
                
                for trade in trades:
                    trade_data.append({
                        "id": trade.id,
                        "symbol": trade.symbol,
                        "side": trade.side.value,
                        "quantity": float(trade.quantity),
                        "price": float(trade.price),
                        "timestamp": trade.timestamp.isoformat(),
                        "buyer_agent_id": trade.buyer_agent_id,
                        "seller_agent_id": trade.seller_agent_id,
                        "order_id": trade.order_id
                    })
                
                return JSONResponse({
                    "status": "success",
                    "data": {
                        "trades": trade_data,
                        "total_trades": len(self.simulator.trading_env.trades)
                    }
                })
            except Exception as e:
                return JSONResponse({
                    "status": "error",
                    "message": str(e)
                }, status_code=500)
        
        @self.app.get("/api/portfolios")
        async def get_portfolios():
            """Get portfolio information"""
            try:
                portfolio_data = {}
                
                for agent_id, portfolio in self.simulator.trading_env.portfolios.items():
                    if portfolio:
                        portfolio_data[agent_id] = {
                            "cash_balance": float(portfolio.cash_balance),
                            "total_value": float(portfolio.total_value),
                            "realized_pnl": float(portfolio.realized_pnl),
                            "unrealized_pnl": float(portfolio.unrealized_pnl),
                            "positions": {
                                symbol: {
                                    "quantity": float(pos.quantity),
                                    "average_price": float(pos.average_price),
                                    "market_value": float(pos.market_value),
                                    "unrealized_pnl": float(pos.unrealized_pnl)
                                } for symbol, pos in portfolio.positions.items()
                            }
                        }
                
                return JSONResponse({
                    "status": "success",
                    "data": portfolio_data
                })
            except Exception as e:
                return JSONResponse({
                    "status": "error",
                    "message": str(e)
                }, status_code=500)
        
        @self.app.websocket("/ws")
        async def websocket_endpoint(websocket: WebSocket):
            """WebSocket endpoint for real-time updates"""
            await self.connect(websocket)
            try:
                while True:
                    # Keep connection alive and handle client messages
                    data = await websocket.receive_text()
                    message = json.loads(data)
                    
                    # Handle client requests
                    if message.get('type') == 'ping':
                        await websocket.send_text(json.dumps({
                            'type': 'pong',
                            'timestamp': datetime.now(timezone.utc).isoformat()
                        }))
                    elif message.get('type') == 'subscribe':
                        # Handle subscription requests
                        await websocket.send_text(json.dumps({
                            'type': 'subscription_confirmed',
                            'channels': message.get('channels', [])
                        }))
                    
            except WebSocketDisconnect:
                self.disconnect(websocket)
    
    async def connect(self, websocket: WebSocket):
        """Handle new WebSocket connection"""
        await websocket.accept()
        self.active_connections.append(websocket)
        self.dashboard_state['connected_clients'] = len(self.active_connections)
        
        self.logger.info(f"New dashboard client connected. Total: {len(self.active_connections)}")
        
        # Send initial data
        try:
            initial_data = {
                'type': 'initial_data',
                'data': self.simulator.get_dashboard_data(),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            await websocket.send_text(json.dumps(initial_data, default=str))
        except Exception as e:
            self.logger.error(f"Error sending initial data: {e}")
    
    def disconnect(self, websocket: WebSocket):
        """Handle WebSocket disconnection"""
        if websocket in self.active_connections:
            self.active_connections.remove(websocket)
        self.dashboard_state['connected_clients'] = len(self.active_connections)
        
        self.logger.info(f"Dashboard client disconnected. Total: {len(self.active_connections)}")
    
    async def broadcast_updates(self):
        """Broadcast real-time updates to all connected clients"""
        if not self.active_connections:
            return
        
        try:
            # Get current dashboard data
            dashboard_data = self.simulator.get_dashboard_data()
            
            # Prepare update message
            update_message = {
                'type': 'real_time_update',
                'data': dashboard_data,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            # Send to all connected clients
            disconnected = []
            for connection in self.active_connections:
                try:
                    await connection.send_text(json.dumps(update_message, default=str))
                except Exception as e:
                    self.logger.warning(f"Failed to send update to client: {e}")
                    disconnected.append(connection)
            
            # Remove disconnected clients
            for connection in disconnected:
                self.disconnect(connection)
            
            self.dashboard_state['last_update'] = datetime.now(timezone.utc).isoformat()
            
        except Exception as e:
            self.logger.error(f"Error broadcasting updates: {e}")
    
    async def start_broadcast_loop(self):
        """Start the real-time broadcast loop"""
        self.logger.info("Starting dashboard broadcast loop...")
        
        while True:
            try:
                await self.broadcast_updates()
                await asyncio.sleep(self.dashboard_state['update_frequency'])
            except Exception as e:
                self.logger.error(f"Error in broadcast loop: {e}")
                await asyncio.sleep(5)
    
    async def start_server(self, host: str = "0.0.0.0", port: int = 8080):
        """Start the dashboard server"""
        self.logger.info(f"🌐 Starting Noryon V2 Trading Dashboard on {host}:{port}")
        
        # Start broadcast loop
        self.broadcast_task = asyncio.create_task(self.start_broadcast_loop())
        
        # Configure and start server
        config = uvicorn.Config(
            app=self.app,
            host=host,
            port=port,
            log_level="info",
            access_log=True
        )
        
        server = uvicorn.Server(config)
        
        try:
            await server.serve()
        except Exception as e:
            self.logger.error(f"Dashboard server error: {e}")
        finally:
            if self.broadcast_task:
                self.broadcast_task.cancel()
    
    def get_dashboard_stats(self) -> Dict[str, Any]:
        """Get dashboard statistics"""
        return {
            'connected_clients': len(self.active_connections),
            'last_update': self.dashboard_state['last_update'],
            'update_frequency': self.dashboard_state['update_frequency'],
            'server_status': 'running'
        }

# HTML Template for the dashboard
DASHBOARD_HTML_TEMPLATE = """
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ title }}</title>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://cdn.tailwindcss.com"></script>
    <style>
        .card { @apply bg-white rounded-lg shadow-md p-6 mb-6; }
        .metric { @apply text-center; }
        .metric-value { @apply text-3xl font-bold text-blue-600; }
        .metric-label { @apply text-sm text-gray-600 mt-1; }
        .status-running { @apply bg-green-100 text-green-800 px-2 py-1 rounded-full text-xs; }
        .status-stopped { @apply bg-red-100 text-red-800 px-2 py-1 rounded-full text-xs; }
        .alert-warning { @apply bg-yellow-100 border-l-4 border-yellow-500 text-yellow-700 p-4 mb-4; }
        .alert-critical { @apply bg-red-100 border-l-4 border-red-500 text-red-700 p-4 mb-4; }
    </style>
</head>
<body class="bg-gray-100">
    <div class="container mx-auto px-4 py-8">
        <!-- Header -->
        <div class="card">
            <div class="flex justify-between items-center">
                <div>
                    <h1 class="text-3xl font-bold text-gray-800">{{ title }}</h1>
                    <p class="text-gray-600">Simulation ID: <span id="simulation-id">{{ simulation_id }}</span></p>
                </div>
                <div class="flex space-x-4">
                    <button id="start-btn" class="bg-green-500 hover:bg-green-700 text-white font-bold py-2 px-4 rounded">
                        Start Simulation
                    </button>
                    <button id="stop-btn" class="bg-red-500 hover:bg-red-700 text-white font-bold py-2 px-4 rounded">
                        Stop Simulation
                    </button>
                    <span id="status-badge" class="status-stopped">Stopped</span>
                </div>
            </div>
        </div>

        <!-- System Alerts -->
        <div id="alerts-container"></div>

        <!-- Key Metrics -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-6">
            <div class="card metric">
                <div id="total-pnl" class="metric-value">$0.00</div>
                <div class="metric-label">Total P&L</div>
            </div>
            <div class="card metric">
                <div id="portfolio-value" class="metric-value">$0.00</div>
                <div class="metric-label">Portfolio Value</div>
            </div>
            <div class="card metric">
                <div id="active-agents" class="metric-value">0</div>
                <div class="metric-label">Active Agents</div>
            </div>
            <div class="card metric">
                <div id="total-trades" class="metric-value">0</div>
                <div class="metric-label">Total Trades</div>
            </div>
        </div>

        <!-- Charts Row -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
            <div class="card">
                <h3 class="text-xl font-semibold mb-4">Portfolio Performance</h3>
                <canvas id="portfolio-chart" width="400" height="200"></canvas>
            </div>
            <div class="card">
                <h3 class="text-xl font-semibold mb-4">Market Prices</h3>
                <canvas id="price-chart" width="400" height="200"></canvas>
            </div>
        </div>

        <!-- Data Tables -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <!-- Recent Orders -->
            <div class="card">
                <h3 class="text-xl font-semibold mb-4">Recent Orders</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-2 text-left">Symbol</th>
                                <th class="px-4 py-2 text-left">Side</th>
                                <th class="px-4 py-2 text-left">Quantity</th>
                                <th class="px-4 py-2 text-left">Price</th>
                                <th class="px-4 py-2 text-left">Status</th>
                            </tr>
                        </thead>
                        <tbody id="orders-table">
                            <!-- Orders will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- Agent Performance -->
            <div class="card">
                <h3 class="text-xl font-semibold mb-4">Agent Performance</h3>
                <div class="overflow-x-auto">
                    <table class="min-w-full table-auto">
                        <thead>
                            <tr class="bg-gray-50">
                                <th class="px-4 py-2 text-left">Agent</th>
                                <th class="px-4 py-2 text-left">Type</th>
                                <th class="px-4 py-2 text-left">P&L</th>
                                <th class="px-4 py-2 text-left">Trades</th>
                                <th class="px-4 py-2 text-left">Win Rate</th>
                            </tr>
                        </thead>
                        <tbody id="agents-table">
                            <!-- Agents will be populated here -->
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- System Status -->
        <div class="card mt-6">
            <h3 class="text-xl font-semibold mb-4">System Status</h3>
            <div class="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div class="text-center">
                    <div class="text-sm text-gray-600">Trading Environment</div>
                    <div id="env-status" class="font-semibold">Stopped</div>
                </div>
                <div class="text-center">
                    <div class="text-sm text-gray-600">Order Engine</div>
                    <div id="engine-status" class="font-semibold">Stopped</div>
                </div>
                <div class="text-center">
                    <div class="text-sm text-gray-600">Market Feed</div>
                    <div id="feed-status" class="font-semibold">Stopped</div>
                </div>
                <div class="text-center">
                    <div class="text-sm text-gray-600">Agent Coordinator</div>
                    <div id="coordinator-status" class="font-semibold">Stopped</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // WebSocket connection
        let ws = null;
        let portfolioChart = null;
        let priceChart = null;

        // Initialize dashboard
        document.addEventListener('DOMContentLoaded', function() {
            initializeCharts();
            connectWebSocket();
            setupEventListeners();
            loadInitialData();
        });

        function connectWebSocket() {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const wsUrl = `${protocol}//${window.location.host}/ws`;
            
            ws = new WebSocket(wsUrl);
            
            ws.onopen = function(event) {
                console.log('WebSocket connected');
            };
            
            ws.onmessage = function(event) {
                const data = JSON.parse(event.data);
                if (data.type === 'real_time_update' || data.type === 'initial_data') {
                    updateDashboard(data.data);
                }
            };
            
            ws.onclose = function(event) {
                console.log('WebSocket disconnected');
                setTimeout(connectWebSocket, 5000); // Reconnect after 5 seconds
            };
            
            ws.onerror = function(error) {
                console.error('WebSocket error:', error);
            };
        }

        function setupEventListeners() {
            document.getElementById('start-btn').addEventListener('click', startSimulation);
            document.getElementById('stop-btn').addEventListener('click', stopSimulation);
        }

        async function startSimulation() {
            try {
                const response = await fetch('/api/simulation/start', { method: 'POST' });
                const result = await response.json();
                if (result.status === 'success') {
                    document.getElementById('status-badge').textContent = 'Running';
                    document.getElementById('status-badge').className = 'status-running';
                }
            } catch (error) {
                console.error('Error starting simulation:', error);
            }
        }

        async function stopSimulation() {
            try {
                const response = await fetch('/api/simulation/stop', { method: 'POST' });
                const result = await response.json();
                if (result.status === 'success') {
                    document.getElementById('status-badge').textContent = 'Stopped';
                    document.getElementById('status-badge').className = 'status-stopped';
                }
            } catch (error) {
                console.error('Error stopping simulation:', error);
            }
        }

        async function loadInitialData() {
            try {
                const response = await fetch('/api/dashboard-data');
                const result = await response.json();
                if (result.status === 'success') {
                    updateDashboard(result.data);
                }
            } catch (error) {
                console.error('Error loading initial data:', error);
            }
        }

        function updateDashboard(data) {
            // Update metrics
            if (data.dashboard && data.dashboard.portfolio_summary) {
                const summary = data.dashboard.portfolio_summary;
                document.getElementById('total-pnl').textContent = `$${summary.total_pnl?.toFixed(2) || '0.00'}`;
                document.getElementById('portfolio-value').textContent = `$${summary.total_value?.toFixed(2) || '0.00'}`;
                document.getElementById('active-agents').textContent = summary.active_agents || '0';
                document.getElementById('total-trades').textContent = summary.total_trades || '0';
            }

            // Update status
            if (data.status === 'running') {
                document.getElementById('status-badge').textContent = 'Running';
                document.getElementById('status-badge').className = 'status-running';
            } else {
                document.getElementById('status-badge').textContent = 'Stopped';
                document.getElementById('status-badge').className = 'status-stopped';
            }

            // Update system status
            if (data.metrics) {
                const systemStatus = {
                    'env-status': 'Running',
                    'engine-status': 'Running', 
                    'feed-status': 'Running',
                    'coordinator-status': 'Running'
                };
                
                Object.entries(systemStatus).forEach(([id, status]) => {
                    const element = document.getElementById(id);
                    if (element) element.textContent = status;
                });
            }

            // Update orders table
            if (data.dashboard && data.dashboard.order_flow) {
                updateOrdersTable(data.dashboard.order_flow);
            }

            // Update agents table
            if (data.dashboard && data.dashboard.agent_performance) {
                updateAgentsTable(data.dashboard.agent_performance);
            }

            // Update alerts
            if (data.dashboard && data.dashboard.system_alerts) {
                updateAlerts(data.dashboard.system_alerts);
            }
        }

        function updateOrdersTable(orders) {
            const tbody = document.getElementById('orders-table');
            tbody.innerHTML = '';
            
            orders.slice(-10).forEach(order => {
                const row = tbody.insertRow();
                row.innerHTML = `
                    <td class="px-4 py-2">${order.symbol}</td>
                    <td class="px-4 py-2">${order.side}</td>
                    <td class="px-4 py-2">${order.quantity}</td>
                    <td class="px-4 py-2">$${order.price?.toFixed(2) || 'Market'}</td>
                    <td class="px-4 py-2">
                        <span class="px-2 py-1 rounded-full text-xs ${
                            order.status === 'filled' ? 'bg-green-100 text-green-800' :
                            order.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                            'bg-gray-100 text-gray-800'
                        }">${order.status}</span>
                    </td>
                `;
            });
        }

        function updateAgentsTable(agents) {
            const tbody = document.getElementById('agents-table');
            tbody.innerHTML = '';
            
            agents.forEach(agent => {
                const row = tbody.insertRow();
                const winRate = agent.total_trades > 0 ? (agent.winning_trades / agent.total_trades * 100).toFixed(1) : '0.0';
                row.innerHTML = `
                    <td class="px-4 py-2">${agent.name}</td>
                    <td class="px-4 py-2">${agent.type}</td>
                    <td class="px-4 py-2 ${
                        agent.total_pnl >= 0 ? 'text-green-600' : 'text-red-600'
                    }">$${agent.total_pnl?.toFixed(2) || '0.00'}</td>
                    <td class="px-4 py-2">${agent.total_trades || 0}</td>
                    <td class="px-4 py-2">${winRate}%</td>
                `;
            });
        }

        function updateAlerts(alerts) {
            const container = document.getElementById('alerts-container');
            container.innerHTML = '';
            
            alerts.slice(-5).forEach(alert => {
                const div = document.createElement('div');
                div.className = alert.type === 'critical' ? 'alert-critical' : 'alert-warning';
                div.innerHTML = `
                    <strong>${alert.type.toUpperCase()}:</strong> ${alert.message}
                    <span class="float-right text-xs">${new Date(alert.timestamp).toLocaleTimeString()}</span>
                `;
                container.appendChild(div);
            });
        }

        function initializeCharts() {
            // Portfolio Performance Chart
            const portfolioCtx = document.getElementById('portfolio-chart').getContext('2d');
            portfolioChart = new Chart(portfolioCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: [{
                        label: 'Portfolio Value',
                        data: [],
                        borderColor: 'rgb(59, 130, 246)',
                        backgroundColor: 'rgba(59, 130, 246, 0.1)',
                        tension: 0.1
                    }]
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });

            // Price Chart
            const priceCtx = document.getElementById('price-chart').getContext('2d');
            priceChart = new Chart(priceCtx, {
                type: 'line',
                data: {
                    labels: [],
                    datasets: []
                },
                options: {
                    responsive: true,
                    scales: {
                        y: {
                            beginAtZero: false
                        }
                    }
                }
            });
        }
    </script>
</body>
</html>
"""

# Save the HTML template
def create_dashboard_template():
    """Create the dashboard HTML template file"""
    import os
    
    # Create templates directory
    templates_dir = "src/web/templates"
    os.makedirs(templates_dir, exist_ok=True)
    
    # Write template file
    with open(f"{templates_dir}/dashboard.html", "w") as f:
        f.write(DASHBOARD_HTML_TEMPLATE)
    
    print(f"Dashboard template created at {templates_dir}/dashboard.html")

if __name__ == "__main__":
    create_dashboard_template()