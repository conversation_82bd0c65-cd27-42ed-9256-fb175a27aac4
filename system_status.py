#!/usr/bin/env python3
"""
Noryon V2 System Status - Complete AI Model Integration Report
"""

import sys
import asyncio
import subprocess
from datetime import datetime
sys.path.append('src')

from services.ai_service import AIService
from core.config import Config

async def get_system_status():
    """Generate comprehensive system status report"""
    
    print("🚀 NORYON V2 TRADING SYSTEM - AI MODEL STATUS REPORT")
    print("=" * 70)
    print(f"📅 Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 70)
    
    # Initialize services
    ai_service = AIService()
    config = Config()
    
    # Define all available models with their capabilities - EXPANDED LIBRARY
    all_models = {
        # Core Trading Models
        "marco-o1:7b": {"tier": "standard", "specialty": "reasoning", "speed": "fast", "security": "public"},
        "hermes3:8b": {"tier": "standard", "specialty": "conversation", "speed": "fast", "security": "public"},
        "nemotron-mini:4b": {"tier": "fast", "specialty": "quick_response", "speed": "very_fast", "security": "public"},
        "granite3.3:8b": {"tier": "standard", "specialty": "general", "speed": "fast", "security": "public"},
        "falcon3:10b": {"tier": "standard", "specialty": "general", "speed": "medium", "security": "public"},
        
        # Premium Models
        # "qwen3:32b": {"tier": "premium", "specialty": "strategic", "speed": "medium", "security": "restricted"},  # REMOVED - Timeout issues
        "gemma3:27b": {"tier": "premium", "specialty": "analysis", "speed": "medium", "security": "restricted"},
        "mistral-small:24b": {"tier": "premium", "specialty": "execution", "speed": "medium", "security": "restricted"},
        "deepseek-r1:latest": {"tier": "premium", "specialty": "research", "speed": "slow", "security": "restricted"},
        # "magistral:24b": {"tier": "premium", "specialty": "strategic", "speed": "medium", "security": "classified"},  # REMOVED - Timeout issues
        
        # Ultra Premium Models
        "command-r:35b": {"tier": "ultra_premium", "specialty": "intelligence", "speed": "slow", "security": "classified"},
        "cogito:32b": {"tier": "ultra_premium", "specialty": "cognitive", "speed": "slow", "security": "classified"},
        "qwen2.5vl:32b": {"tier": "ultra_premium", "specialty": "vision", "speed": "slow", "security": "classified"},
        "exaone-deep:32b": {"tier": "ultra_premium", "specialty": "deep_research", "speed": "very_slow", "security": "classified"},
        # "phi4-reasoning:plus": {"tier": "advanced", "specialty": "reasoning", "speed": "medium", "security": "classified"},  # REMOVED - Timeout issues
        
        # Uncensored Intelligence Models (TOP SECRET)
        "huihui_ai/am-thinking-abliterate:latest": {"tier": "uncensored_premium", "specialty": "uncensored_analysis", "speed": "slow", "security": "top_secret"},
        "huihui_ai/magistral-abliterated:24b": {"tier": "uncensored_premium", "specialty": "uncensored_strategy", "speed": "medium", "security": "top_secret"},
        "huihui_ai/homunculus-abliterated:latest": {"tier": "uncensored_standard", "specialty": "uncensored_research", "speed": "medium", "security": "top_secret"},
        "huihui_ai/acereason-nemotron-abliterated:14b": {"tier": "uncensored_standard", "specialty": "specialized_reasoning", "speed": "fast", "security": "top_secret"},
        "goekdenizguelmez/JOSIEFIED-Qwen3:14b": {"tier": "specialized", "specialty": "enhanced_qwen", "speed": "medium", "security": "top_secret"}
    }
    
    # 1. Ollama Service Status
    print("\n🔧 OLLAMA SERVICE STATUS")
    print("-" * 30)
    
    try:
        result = subprocess.run(["ollama", "ps"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ Ollama service is running")
            running_models = result.stdout.strip()
            if running_models and "NAME" in running_models:
                print("🏃 Currently running models:")
                lines = running_models.split('\n')[1:]  # Skip header
                for line in lines:
                    if line.strip():
                        model_name = line.split()[0]
                        print(f"   • {model_name}")
            else:
                print("💤 No models currently running")
        else:
            print("❌ Ollama service not responding")
    except Exception as e:
        print(f"❌ Error checking Ollama status: {e}")
    
    # 2. Model Availability Matrix
    print("\n📋 MODEL AVAILABILITY MATRIX")
    print("-" * 35)
    
    unique_models = list(set(ai_service.models.values()))
    available_models = []
    unavailable_models = []
    
    for model in sorted(unique_models):
        try:
            available = await ai_service.check_model_availability(model)
            status = "✅" if available else "❌"
            print(f"{model:<30} {status}")
            
            if available:
                available_models.append(model)
            else:
                unavailable_models.append(model)
        except Exception as e:
            print(f"{model:<30} ❌ Error: {str(e)[:20]}...")
            unavailable_models.append(model)
    
    # 3. Agent Configuration Status
    print("\n🤖 AGENT CONFIGURATION STATUS")
    print("-" * 50)
    
    # Categorize agents by function
    agent_categories = {
        "Core Trading Agents": [
            "market_watcher", "strategy_researcher", "technical_analyst", 
            "news_analyst", "risk_officer", "trade_executor", 
            "compliance_auditor", "chief_analyst", "portfolio_manager"
        ],
        "Advanced Reasoning Agents": [
            "advanced_analyst", "reasoning_engine", "quick_responder", 
            "conversation_agent", "backup_analyst"
        ],
        "Intelligence Agents": [
            "intelligence_analyst", "cognitive_analyst", "vision_analyst", 
            "deep_researcher", "premium_strategist"
        ],
        "Uncensored Operations": [
            "uncensored_analyst", "uncensored_strategist", "uncensored_researcher", 
            "specialized_reasoning", "enhanced_qwen"
        ]
    }
    
    agent_status = {}
    for agent_type, model in ai_service.models.items():
        is_available = model in available_models
        status = "✅ Ready" if is_available else "❌ Model Missing"
        tier = "Unknown"
        security = "Public"
        
        # Determine tier
        for tier_name, tier_models in ai_service.model_tiers.items():
            if model in tier_models:
                tier = tier_name.upper()
                break
        
        # Determine security level
        for security_level, security_models in ai_service.security_levels.items():
            if model in security_models:
                security = security_level.upper()
                break
        
        agent_status[agent_type] = {
            "model": model,
            "available": is_available,
            "tier": tier,
            "security": security
        }
    
    # Display agents by category
    for category, agents in agent_categories.items():
        print(f"\n📂 {category}:")
        for agent in agents:
            if agent in agent_status:
                info = agent_status[agent]
                print(f"  {agent:<20} → {info['model']:<25} [{info['tier']:<12}] [{info['security']:<10}] {'✅ Ready' if info['available'] else '❌ Missing'}")
            else:
                print(f"  {agent:<20} → Not configured")
    
    # Display any uncategorized agents
    uncategorized = [agent for agent in ai_service.models.keys() 
                    if not any(agent in agents for agents in agent_categories.values())]
    if uncategorized:
        print(f"\n📂 Other Agents:")
        for agent in uncategorized:
            info = agent_status[agent]
            print(f"  {agent:<20} → {info['model']:<25} [{info['tier']:<12}] [{info['security']:<10}] {'✅ Ready' if info['available'] else '❌ Missing'}")
    
    # 4. Performance Tier Analysis
    print("\n⚡ PERFORMANCE TIER ANALYSIS")
    print("-" * 35)
    
    for tier_name, tier_models in ai_service.model_tiers.items():
        available_in_tier = [m for m in tier_models if m in available_models]
        total_in_tier = len(tier_models)
        available_count = len(available_in_tier)
        
        percentage = (available_count / total_in_tier * 100) if total_in_tier > 0 else 0
        status_icon = "✅" if percentage == 100 else "⚠️" if percentage >= 50 else "❌"
        
        print(f"{tier_name.upper():<12} {status_icon} {available_count}/{total_in_tier} models ({percentage:.0f}%)")
        
        if available_count < total_in_tier:
            missing = [m for m in tier_models if m not in available_models]
            print(f"             Missing: {', '.join(missing)}")
    
    # 5. Security Level Analysis
    print("\n🔒 SECURITY LEVEL ANALYSIS")
    print("-" * 35)
    
    for security_level, security_models in ai_service.security_levels.items():
        available_in_security = [m for m in security_models if m in available_models]
        total_in_security = len(security_models)
        available_count = len(available_in_security)
        
        percentage = (available_count / total_in_security * 100) if total_in_security > 0 else 0
        status_icon = "✅" if percentage == 100 else "⚠️" if percentage >= 50 else "❌"
        
        print(f"{security_level.upper():<12} {status_icon} {available_count}/{total_in_security} models ({percentage:.0f}%)")
        
        if available_count < total_in_security:
            missing = [m for m in security_models if m not in available_models]
            print(f"             Missing: {', '.join(missing)}")
    
    # 6. System Readiness Summary
    print("\n🎯 SYSTEM READINESS SUMMARY")
    print("-" * 35)
    
    total_agents = len(ai_service.models)
    ready_agents = sum(1 for status in agent_status.values() if status["available"])
    readiness_percentage = (ready_agents / total_agents * 100) if total_agents > 0 else 0
    
    print(f"Total AI Agents: {total_agents}")
    print(f"Ready Agents: {ready_agents}")
    print(f"Readiness: {readiness_percentage:.1f}%")
    
    if readiness_percentage == 100:
        print("\n🎉 SYSTEM STATUS: FULLY OPERATIONAL")
        print("   All AI agents are ready for trading operations!")
    elif readiness_percentage >= 80:
        print("\n⚠️  SYSTEM STATUS: MOSTLY OPERATIONAL")
        print("   Most agents ready, some models may need installation.")
    else:
        print("\n❌ SYSTEM STATUS: REQUIRES ATTENTION")
        print("   Multiple models missing, system functionality limited.")
    
    # 7. Installation Commands
    if unavailable_models:
        print("\n🔧 INSTALLATION COMMANDS FOR MISSING MODELS")
        print("-" * 50)
        print("Run these commands to install missing models:")
        print()
        for model in sorted(unavailable_models):
            print(f"ollama pull {model}")
    
    # 8. Next Steps
    print("\n📋 NEXT STEPS")
    print("-" * 15)
    
    if readiness_percentage == 100:
        print("1. ✅ Start the Noryon V2 trading system: python main.py")
        print("2. ✅ Begin paper trading to test strategies")
        print("3. ✅ Monitor agent performance and adjust parameters")
    else:
        print("1. 🔧 Install missing models using commands above")
        print("2. 🔄 Re-run this status check: python system_status.py")
        print("3. 🚀 Start system once all models are available")
    
    print("\n" + "=" * 70)
    print("📊 Report completed successfully!")
    print("=" * 70)

if __name__ == "__main__":
    asyncio.run(get_system_status())