"""Comprehensive Testing Framework for AI Agent Tools and Capabilities
Tests all agent tools, autonomous decision-making, collaboration, and learning systems.
"""

import asyncio
import json
import logging
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional
import sys
import os

# Add src to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from src.agents.agent_tools import (
    AgentToolkit, initialize_toolkit, MarketDataAnalyzer, TradingExecutor,
    RiskManager, AgentCommunicator, PerformanceMonitor, ToolCategory, ToolPriority
)
from src.agents.enhanced_base_agent import (
    EnhancedBaseAgent, DecisionType, LearningMode, Decision
)
from src.services.ai_service import AIService
from src.core.config import Config
from src.db.database_manager import DatabaseManager

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger("AgentCapabilityTester")

class MockDatabaseManager:
    """Mock database manager for testing"""
    
    def __init__(self):
        self.data = {}
    
    async def store_data(self, key: str, data: Any):
        self.data[key] = data
    
    async def get_data(self, key: str) -> Any:
        return self.data.get(key)

class MockConfig:
    """Mock configuration for testing"""
    
    def __init__(self):
        self.ollama_url = "http://localhost:11434"
        self.model_configs = {
            "test_agent": {
                "model": "marco-o1:7b",
                "max_tokens": 4096,
                "temperature": 0.3,
                "update_interval": 30
            }
        }
    
    def get_model_config(self, agent_name: str) -> Dict[str, Any]:
        return self.model_configs.get(agent_name, self.model_configs["test_agent"])

class TestAgent(EnhancedBaseAgent):
    """Test agent implementation for capability testing"""
    
    def __init__(self, agent_name: str, ai_service: AIService, config: Config, db_manager: DatabaseManager):
        super().__init__(agent_name, "marco-o1:7b", config, db_manager, ai_service)
        self.test_results = []
    
    async def _initialize_agent(self):
        """Initialize test agent"""
        # Use standard logging since AgentLogger doesn't have info method
        import logging
        logger = logging.getLogger(f"test_agent.{self.agent_name}")
        logger.info(f"Initializing test agent: {self.agent_name}")
        return True
    
    async def _start_agent_tasks(self):
        """Start agent background tasks"""
        pass
    
    async def _cleanup_agent(self):
        """Cleanup agent resources"""
        pass
    
    async def _handle_message(self, message: Dict[str, Any]):
        """Handle incoming messages"""
        return {"status": "processed", "message": message}
    
    async def _perform_periodic_analysis(self):
        """Perform periodic analysis"""
        return {"analysis": "test_analysis", "timestamp": datetime.utcnow()}
    
    def _prepare_analysis_prompt(self, data: Dict[str, Any]) -> str:
        """Prepare analysis prompt"""
        return f"Analyze the following data: {data}"
    
    def _prepare_signal_prompt(self, data: Dict[str, Any]) -> str:
        """Prepare signal prompt"""
        return f"Generate signal for: {data}"
    
    def _parse_analysis_response(self, response: str) -> Dict[str, Any]:
        """Parse analysis response"""
        return {"analysis": response, "confidence": 0.8}
    
    def _parse_signal_response(self, response: str) -> Dict[str, Any]:
        """Parse signal response"""
        return {"signal": response, "strength": 0.7}
    
    async def execute_specialized_task(self, task_data: Dict[str, Any]) -> Any:
        """Execute test-specific specialized tasks"""
        task_type = task_data.get("type", "general")
        
        if task_type == "market_analysis":
            return await self._test_market_analysis()
        elif task_type == "trading_decision":
            return await self._test_trading_decision()
        elif task_type == "risk_assessment":
            return await self._test_risk_assessment()
        else:
            return {"success": True, "message": "General task completed"}
    
    async def _test_market_analysis(self):
        """Test market analysis capabilities"""
        if not self.toolkit:
            return {"success": False, "message": "Toolkit not available"}
        
        result = await self.toolkit.execute_tool(
            self.agent_name, "market_data_analyzer",
            symbol="BTC/USD",
            timeframe="1h",
            analysis_type="comprehensive"
        )
        
        self.test_results.append({
            "test": "market_analysis",
            "success": result.success,
            "timestamp": datetime.utcnow()
        })
        
        return {"success": result.success, "data": result.data}
    
    async def _test_trading_decision(self):
        """Test trading decision making"""
        situation = {
            "symbol": "BTC/USD",
            "current_price": 50000,
            "trend": "bullish",
            "volume": "high"
        }
        
        decision = await self.make_autonomous_decision(situation, DecisionType.TRADING)
        
        self.test_results.append({
            "test": "trading_decision",
            "success": decision is not None,
            "decision": decision.action if decision else None,
            "timestamp": datetime.utcnow()
        })
        
        return {"success": decision is not None, "decision": decision}
    
    async def _test_risk_assessment(self):
        """Test risk assessment capabilities"""
        if not self.toolkit:
            return {"success": False, "message": "Toolkit not available"}
        
        portfolio = {
            "total_value": 100000,
            "positions": {"BTC": {"quantity": 1, "value": 50000}}
        }
        
        proposed_trade = {
            "symbol": "ETH/USD",
            "action": "buy",
            "quantity": 10,
            "price": 3000
        }
        
        result = await self.toolkit.execute_tool(
            self.agent_name, "risk_manager",
            portfolio=portfolio,
            proposed_trade=proposed_trade
        )
        
        self.test_results.append({
            "test": "risk_assessment",
            "success": result.success,
            "timestamp": datetime.utcnow()
        })
        
        return {"success": result.success, "data": result.data}

class AgentCapabilityTester:
    """Comprehensive tester for agent capabilities"""
    
    def __init__(self):
        self.logger = logging.getLogger("AgentCapabilityTester")
        self.test_results = {}
        self.ai_service = AIService()
        self.config = MockConfig()
        self.db_manager = MockDatabaseManager()
        self.toolkit = None
        self.test_agents = []
    
    async def setup(self):
        """Set up testing environment"""
        self.logger.info("🔧 Setting up agent capability testing environment...")
        
        try:
            # Initialize toolkit
            self.toolkit = initialize_toolkit(self.ai_service, self.db_manager, self.config)
            
            # Create test agents
            agent_names = ["test_market_watcher", "test_trader", "test_risk_manager"]
            
            for agent_name in agent_names:
                agent = TestAgent(agent_name, self.ai_service, self.config, self.db_manager)
                await agent.initialize()
                self.test_agents.append(agent)
            
            self.logger.info(f"✅ Setup complete: {len(self.test_agents)} test agents created")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Setup failed: {e}")
            return False
    
    async def test_individual_tools(self):
        """Test each tool individually"""
        self.logger.info("🔍 Testing individual agent tools...")
        
        tool_tests = {
            "market_data_analyzer": self._test_market_data_analyzer,
            "trading_executor": self._test_trading_executor,
            "risk_manager": self._test_risk_manager,
            "agent_communicator": self._test_agent_communicator,
            "performance_monitor": self._test_performance_monitor
        }
        
        results = {}
        
        for tool_name, test_func in tool_tests.items():
            try:
                self.logger.info(f"  Testing {tool_name}...")
                result = await test_func()
                results[tool_name] = result
                status = "✅ PASS" if result["success"] else "❌ FAIL"
                self.logger.info(f"  {tool_name}: {status} - {result['message']}")
            except Exception as e:
                results[tool_name] = {"success": False, "message": str(e), "error": True}
                self.logger.error(f"  {tool_name}: ❌ ERROR - {e}")
        
        self.test_results["individual_tools"] = results
        return results
    
    async def _test_market_data_analyzer(self):
        """Test market data analyzer tool"""
        try:
            result = await self.toolkit.execute_tool(
                "test_agent", "market_data_analyzer",
                symbol="BTC/USD",
                timeframe="1h",
                analysis_type="comprehensive"
            )
            
            return {
                "success": result.success,
                "message": result.message,
                "execution_time": result.execution_time,
                "data_points": result.data.get("data_points", 0) if result.data else 0
            }
        except Exception as e:
            return {"success": False, "message": f"Test failed: {e}"}
    
    async def _test_trading_executor(self):
        """Test trading executor tool"""
        try:
            result = await self.toolkit.execute_tool(
                "test_agent", "trading_executor",
                action="buy",
                symbol="BTC/USD",
                quantity=0.1,
                price=50000,
                order_type="limit"
            )
            
            return {
                "success": result.success,
                "message": result.message,
                "execution_time": result.execution_time,
                "trade_id": result.data.get("trade_id") if result.data else None
            }
        except Exception as e:
            return {"success": False, "message": f"Test failed: {e}"}
    
    async def _test_risk_manager(self):
        """Test risk manager tool"""
        try:
            portfolio = {
                "total_value": 100000,
                "positions": {"BTC": {"quantity": 1, "value": 50000}}
            }
            
            proposed_trade = {
                "symbol": "ETH/USD",
                "action": "buy",
                "quantity": 10,
                "price": 3000
            }
            
            result = await self.toolkit.execute_tool(
                "test_agent", "risk_manager",
                portfolio=portfolio,
                proposed_trade=proposed_trade
            )
            
            return {
                "success": result.success,
                "message": result.message,
                "execution_time": result.execution_time,
                "risk_score": result.data.get("risk_assessment", {}).get("risk_score") if result.data else None
            }
        except Exception as e:
            return {"success": False, "message": f"Test failed: {e}"}
    
    async def _test_agent_communicator(self):
        """Test agent communicator tool"""
        try:
            # Test message sending
            send_result = await self.toolkit.execute_tool(
                "test_agent", "agent_communicator",
                action="send_message",
                recipient="test_recipient",
                message="Test message",
                message_type="test"
            )
            
            # Test broadcasting
            broadcast_result = await self.toolkit.execute_tool(
                "test_agent", "agent_communicator",
                action="broadcast",
                message="Test broadcast",
                topic="test_topic"
            )
            
            # Test subscription
            subscribe_result = await self.toolkit.execute_tool(
                "test_agent", "agent_communicator",
                action="subscribe",
                topic="test_topic"
            )
            
            success = send_result.success and broadcast_result.success and subscribe_result.success
            
            return {
                "success": success,
                "message": "Communication tests completed",
                "send_success": send_result.success,
                "broadcast_success": broadcast_result.success,
                "subscribe_success": subscribe_result.success
            }
        except Exception as e:
            return {"success": False, "message": f"Test failed: {e}"}
    
    async def _test_performance_monitor(self):
        """Test performance monitor tool"""
        try:
            result = await self.toolkit.execute_tool(
                "test_agent", "performance_monitor",
                metric_type="comprehensive"
            )
            
            return {
                "success": result.success,
                "message": result.message,
                "execution_time": result.execution_time,
                "metrics_collected": len(result.data) if result.data else 0
            }
        except Exception as e:
            return {"success": False, "message": f"Test failed: {e}"}
    
    async def test_autonomous_decision_making(self):
        """Test autonomous decision-making capabilities"""
        self.logger.info("🧠 Testing autonomous decision-making...")
        
        results = {}
        
        for agent in self.test_agents:
            agent_results = []
            
            # Test different decision types
            decision_scenarios = [
                {
                    "type": DecisionType.TRADING,
                    "situation": {
                        "symbol": "BTC/USD",
                        "price": 50000,
                        "trend": "bullish",
                        "volume": "high"
                    }
                },
                {
                    "type": DecisionType.RISK_MANAGEMENT,
                    "situation": {
                        "portfolio_risk": 0.7,
                        "market_volatility": "high",
                        "open_positions": 5
                    }
                },
                {
                    "type": DecisionType.ANALYSIS,
                    "situation": {
                        "market_condition": "uncertain",
                        "news_sentiment": "negative",
                        "technical_indicators": "mixed"
                    }
                }
            ]
            
            for scenario in decision_scenarios:
                try:
                    decision = await agent.make_autonomous_decision(
                        scenario["situation"], scenario["type"]
                    )
                    
                    agent_results.append({
                        "scenario_type": scenario["type"].value,
                        "decision_made": decision is not None,
                        "action": decision.action if decision else None,
                        "confidence": decision.confidence if decision else 0,
                        "reasoning": decision.reasoning[:100] if decision else None
                    })
                    
                except Exception as e:
                    agent_results.append({
                        "scenario_type": scenario["type"].value,
                        "decision_made": False,
                        "error": str(e)
                    })
            
            results[agent.agent_name] = agent_results
        
        self.test_results["autonomous_decisions"] = results
        
        # Log summary
        total_scenarios = sum(len(agent_results) for agent_results in results.values())
        successful_decisions = sum(
            1 for agent_results in results.values() 
            for result in agent_results 
            if result.get("decision_made", False)
        )
        
        success_rate = successful_decisions / total_scenarios if total_scenarios > 0 else 0
        self.logger.info(f"  Decision-making success rate: {success_rate:.2%} ({successful_decisions}/{total_scenarios})")
        
        return results
    
    async def test_agent_collaboration(self):
        """Test agent collaboration capabilities"""
        self.logger.info("🤝 Testing agent collaboration...")
        
        if len(self.test_agents) < 2:
            return {"success": False, "message": "Need at least 2 agents for collaboration testing"}
        
        results = []
        
        # Test collaboration between agents
        initiator = self.test_agents[0]
        
        collaboration_tasks = [
            {
                "task": "Analyze market conditions for BTC/USD",
                "required_expertise": ["market_analysis", "technical_analysis"]
            },
            {
                "task": "Assess portfolio risk and recommend adjustments",
                "required_expertise": ["risk_assessment", "portfolio_management"]
            }
        ]
        
        for task in collaboration_tasks:
            try:
                result = await initiator.collaborate_with_peers(
                    task["task"], task["required_expertise"]
                )
                
                results.append({
                    "task": task["task"],
                    "success": result["success"],
                    "peers_contacted": result.get("peers_contacted", 0),
                    "collaboration_id": result.get("collaboration_id")
                })
                
            except Exception as e:
                results.append({
                    "task": task["task"],
                    "success": False,
                    "error": str(e)
                })
        
        self.test_results["collaboration"] = results
        
        # Log summary
        successful_collaborations = sum(1 for result in results if result["success"])
        self.logger.info(f"  Collaboration success rate: {successful_collaborations}/{len(results)}")
        
        return results
    
    async def test_learning_and_adaptation(self):
        """Test learning and adaptation capabilities"""
        self.logger.info("📚 Testing learning and adaptation...")
        
        results = {}
        
        for agent in self.test_agents:
            agent_results = {
                "initial_autonomy": agent.autonomy_level,
                "initial_threshold": agent.decision_threshold,
                "learning_records_before": len(agent.learning_records)
            }
            
            # Simulate some decisions and outcomes
            for i in range(5):
                situation = {
                    "test_scenario": i,
                    "market_condition": "volatile",
                    "confidence_test": True
                }
                
                decision = await agent.make_autonomous_decision(situation, DecisionType.ANALYSIS)
                
                if decision:
                    # Simulate execution and outcome
                    success = i % 3 != 0  # 2/3 success rate
                    decision.success_score = 1.0 if success else 0.0
                    decision.execution_status = "completed"
                    
                    if success:
                        agent.success_streak += 1
                        agent.failure_count = 0
                    else:
                        agent.success_streak = 0
                        agent.failure_count += 1
            
            # Test adaptation
            await agent.adapt_behavior()
            
            agent_results.update({
                "final_autonomy": agent.autonomy_level,
                "final_threshold": agent.decision_threshold,
                "learning_records_after": len(agent.learning_records),
                "autonomy_changed": abs(agent_results["initial_autonomy"] - agent.autonomy_level) > 0.01,
                "threshold_changed": abs(agent_results["initial_threshold"] - agent.decision_threshold) > 0.01,
                "learning_occurred": agent_results["learning_records_after"] > agent_results["learning_records_before"]
            })
            
            results[agent.agent_name] = agent_results
        
        self.test_results["learning_adaptation"] = results
        
        # Log summary
        agents_that_adapted = sum(
            1 for result in results.values() 
            if result["autonomy_changed"] or result["threshold_changed"]
        )
        agents_that_learned = sum(
            1 for result in results.values() 
            if result["learning_occurred"]
        )
        
        self.logger.info(f"  Agents that adapted behavior: {agents_that_adapted}/{len(results)}")
        self.logger.info(f"  Agents that recorded learning: {agents_that_learned}/{len(results)}")
        
        return results
    
    async def test_performance_monitoring(self):
        """Test performance monitoring and metrics collection"""
        self.logger.info("📊 Testing performance monitoring...")
        
        results = {}
        
        # Test toolkit usage statistics
        usage_stats = self.toolkit.get_usage_statistics()
        results["toolkit_usage"] = {
            "tools_used": len(usage_stats),
            "total_usage": sum(stat["usage_count"] for stat in usage_stats.values()),
            "unique_agents": len(set(
                agent for stat in usage_stats.values() 
                for agent in stat["agents"]
            ))
        }
        
        # Test agent status reporting
        agent_statuses = []
        for agent in self.test_agents:
            try:
                status = await agent.get_agent_status()
                agent_statuses.append({
                    "agent_name": status["agent_name"],
                    "state": status["state"],
                    "autonomy_level": status["autonomy_level"],
                    "decisions_made": status["decisions_made_today"],
                    "capabilities_count": status["capabilities_count"]
                })
            except Exception as e:
                agent_statuses.append({
                    "agent_name": agent.agent_name,
                    "error": str(e)
                })
        
        results["agent_statuses"] = agent_statuses
        
        # Test tool performance metrics
        tool_metrics = {}
        for tool_name in self.toolkit.get_available_tools():
            tool_info = self.toolkit.get_tool_info(tool_name)
            tool_metrics[tool_name] = {
                "usage_count": tool_info["usage_count"],
                "success_rate": tool_info["success_rate"],
                "avg_execution_time": tool_info["avg_execution_time"]
            }
        
        results["tool_metrics"] = tool_metrics
        
        self.test_results["performance_monitoring"] = results
        
        # Log summary
        active_agents = sum(1 for status in agent_statuses if status.get("state") == "active")
        total_decisions = sum(status.get("decisions_made", 0) for status in agent_statuses)
        
        self.logger.info(f"  Active agents: {active_agents}/{len(agent_statuses)}")
        self.logger.info(f"  Total decisions made: {total_decisions}")
        self.logger.info(f"  Tools with usage: {len([t for t in tool_metrics.values() if t['usage_count'] > 0])}/{len(tool_metrics)}")
        
        return results
    
    async def run_comprehensive_test(self):
        """Run all tests and generate comprehensive report"""
        self.logger.info("🚀 Starting comprehensive agent capability testing...")
        
        start_time = datetime.utcnow()
        
        # Setup
        if not await self.setup():
            return {"success": False, "message": "Setup failed"}
        
        # Run all test suites
        test_suites = [
            ("Individual Tools", self.test_individual_tools),
            ("Autonomous Decision Making", self.test_autonomous_decision_making),
            ("Agent Collaboration", self.test_agent_collaboration),
            ("Learning and Adaptation", self.test_learning_and_adaptation),
            ("Performance Monitoring", self.test_performance_monitoring)
        ]
        
        for suite_name, test_func in test_suites:
            self.logger.info(f"\n{'='*60}")
            self.logger.info(f"Running {suite_name} Tests")
            self.logger.info(f"{'='*60}")
            
            try:
                await test_func()
            except Exception as e:
                self.logger.error(f"Test suite '{suite_name}' failed: {e}")
                self.test_results[suite_name.lower().replace(' ', '_')] = {
                    "error": str(e),
                    "success": False
                }
        
        end_time = datetime.utcnow()
        test_duration = (end_time - start_time).total_seconds()
        
        # Generate final report
        report = self._generate_test_report(test_duration)
        
        self.logger.info(f"\n{'='*60}")
        self.logger.info("COMPREHENSIVE TEST REPORT")
        self.logger.info(f"{'='*60}")
        self.logger.info(f"Test Duration: {test_duration:.2f} seconds")
        self.logger.info(f"Overall Success Rate: {report['overall_success_rate']:.2%}")
        self.logger.info(f"Tests Passed: {report['tests_passed']}/{report['total_tests']}")
        
        for category, results in report['category_results'].items():
            status = "✅ PASS" if results['success'] else "❌ FAIL"
            self.logger.info(f"  {category}: {status} ({results['success_rate']:.2%})")
        
        if report['recommendations']:
            self.logger.info("\nRecommendations:")
            for rec in report['recommendations']:
                self.logger.info(f"  • {rec}")
        
        return report
    
    def _generate_test_report(self, test_duration: float) -> Dict[str, Any]:
        """Generate comprehensive test report"""
        category_results = {}
        total_tests = 0
        tests_passed = 0
        
        # Analyze individual tools
        if "individual_tools" in self.test_results:
            tools_results = self.test_results["individual_tools"]
            passed = sum(1 for result in tools_results.values() if result.get("success", False))
            total = len(tools_results)
            category_results["Individual Tools"] = {
                "success": passed == total,
                "success_rate": passed / total if total > 0 else 0,
                "passed": passed,
                "total": total
            }
            total_tests += total
            tests_passed += passed
        
        # Analyze autonomous decisions
        if "autonomous_decisions" in self.test_results:
            decisions_results = self.test_results["autonomous_decisions"]
            total_decisions = sum(len(agent_results) for agent_results in decisions_results.values())
            successful_decisions = sum(
                1 for agent_results in decisions_results.values()
                for result in agent_results
                if result.get("decision_made", False)
            )
            category_results["Autonomous Decisions"] = {
                "success": successful_decisions > total_decisions * 0.7,
                "success_rate": successful_decisions / total_decisions if total_decisions > 0 else 0,
                "passed": successful_decisions,
                "total": total_decisions
            }
            total_tests += total_decisions
            tests_passed += successful_decisions
        
        # Analyze collaboration
        if "collaboration" in self.test_results:
            collab_results = self.test_results["collaboration"]
            passed = sum(1 for result in collab_results if result.get("success", False))
            total = len(collab_results)
            category_results["Collaboration"] = {
                "success": passed > 0,
                "success_rate": passed / total if total > 0 else 0,
                "passed": passed,
                "total": total
            }
            total_tests += total
            tests_passed += passed
        
        # Analyze learning and adaptation
        if "learning_adaptation" in self.test_results:
            learning_results = self.test_results["learning_adaptation"]
            adapted_agents = sum(
                1 for result in learning_results.values()
                if result.get("autonomy_changed", False) or result.get("threshold_changed", False)
            )
            total_agents = len(learning_results)
            category_results["Learning & Adaptation"] = {
                "success": adapted_agents > 0,
                "success_rate": adapted_agents / total_agents if total_agents > 0 else 0,
                "passed": adapted_agents,
                "total": total_agents
            }
            total_tests += total_agents
            tests_passed += adapted_agents
        
        # Generate recommendations
        recommendations = []
        
        if category_results.get("Individual Tools", {}).get("success_rate", 0) < 0.8:
            recommendations.append("Review and improve individual tool implementations")
        
        if category_results.get("Autonomous Decisions", {}).get("success_rate", 0) < 0.6:
            recommendations.append("Enhance AI decision-making algorithms and confidence thresholds")
        
        if category_results.get("Collaboration", {}).get("success_rate", 0) < 0.5:
            recommendations.append("Improve agent communication and collaboration protocols")
        
        if category_results.get("Learning & Adaptation", {}).get("success_rate", 0) < 0.3:
            recommendations.append("Enhance learning algorithms and adaptation mechanisms")
        
        overall_success_rate = tests_passed / total_tests if total_tests > 0 else 0
        
        return {
            "test_duration": test_duration,
            "total_tests": total_tests,
            "tests_passed": tests_passed,
            "overall_success_rate": overall_success_rate,
            "category_results": category_results,
            "recommendations": recommendations,
            "detailed_results": self.test_results,
            "timestamp": datetime.utcnow().isoformat()
        }

async def main():
    """Main testing function"""
    tester = AgentCapabilityTester()
    
    try:
        report = await tester.run_comprehensive_test()
        
        # Save detailed report
        with open("agent_capability_test_report.json", "w") as f:
            json.dump(report, f, indent=2, default=str)
        
        print("\n" + "="*80)
        print("AGENT CAPABILITY TESTING COMPLETED")
        print("="*80)
        
        if report and 'overall_success_rate' in report:
            print(f"Overall Success Rate: {report['overall_success_rate']:.2%}")
            success_rate = report['overall_success_rate']
        else:
            print("Overall Success Rate: 0.00%")
            success_rate = 0.0
            
        print(f"Detailed report saved to: agent_capability_test_report.json")
        
        return success_rate > 0.7
        
    except Exception as e:
        logger.error(f"Testing failed: {e}")
        return False

if __name__ == "__main__":
    success = asyncio.run(main())
    exit(0 if success else 1)