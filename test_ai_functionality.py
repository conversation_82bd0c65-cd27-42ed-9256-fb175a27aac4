#!/usr/bin/env python3
"""
Test script to verify AI functionality with all configured models
"""

import sys
import asyncio
sys.path.append('src')

from services.ai_service import AIService

async def test_ai_functionality():
    """Test AI service functionality with different agents"""
    ai = AIService()
    
    print("🧠 Testing AI Functionality for Noryon V2")
    print("=" * 50)
    
    # Test cases for different agent types
    test_cases = [
        {
            "agent": "chief_analyst",
            "prompt": "Analyze current Bitcoin market conditions and provide a brief trading recommendation.",
            "priority": "critical"
        },
        {
            "agent": "market_watcher",
            "prompt": "Monitor BTC/USDT price action and identify any significant patterns.",
            "priority": "fast"
        },
        {
            "agent": "risk_officer",
            "prompt": "Assess the risk of a $1000 Bitcoin position at current market conditions.",
            "priority": "standard"
        },
        {
            "agent": "quick_responder",
            "prompt": "Quick market update for Ethereum.",
            "priority": "fast"
        }
    ]
    
    print(f"\n🧪 Running {len(test_cases)} AI functionality tests...\n")
    
    for i, test_case in enumerate(test_cases, 1):
        agent = test_case["agent"]
        prompt = test_case["prompt"]
        priority = test_case["priority"]
        
        print(f"Test {i}: {agent} ({priority} priority)")
        print(f"Prompt: {prompt}")
        print("-" * 60)
        
        try:
            # Test with fallback functionality
            response = await ai.generate_response_with_fallback(
                agent_type=agent,
                prompt=prompt,
                priority=priority,
                max_retries=1
            )
            
            print(f"✅ Response: {response[:200]}{'...' if len(response) > 200 else ''}")
            print(f"📊 Response length: {len(response)} characters")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        
        print("\n" + "=" * 60 + "\n")
    
    # Test model tier functionality
    print("🎯 Testing Model Tier System:")
    print("-" * 40)
    
    for tier_name, models in ai.model_tiers.items():
        print(f"{tier_name.upper()}: {', '.join(models)}")
    
    print("\n✅ AI functionality tests completed!")

if __name__ == "__main__":
    asyncio.run(test_ai_functionality())