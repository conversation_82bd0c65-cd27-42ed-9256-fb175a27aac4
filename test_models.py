#!/usr/bin/env python3
"""
Test script to verify Ollama model availability for Noryon V2
"""

import sys
import asyncio
sys.path.append('src')

from services.ai_service import AIService

async def test_model_availability():
    """Test all configured models for availability"""
    ai = AIService()
    
    print("🔍 Testing Ollama Model Availability for Noryon V2")
    print("=" * 50)
    
    # Get unique models from configuration
    models = list(ai.models.values())
    unique_models = list(set(models))
    
    print(f"\n📋 Total unique models to test: {len(unique_models)}")
    print("\n🧪 Model Availability Test Results:")
    print("-" * 40)
    
    available_count = 0
    unavailable_models = []
    
    for model in sorted(unique_models):
        try:
            available = await ai.check_model_availability(model)
            status = "✅ Available" if available else "❌ Not Available"
            print(f"{model:<25} {status}")
            
            if available:
                available_count += 1
            else:
                unavailable_models.append(model)
                
        except Exception as e:
            print(f"{model:<25} ❌ Error: {e}")
            unavailable_models.append(model)
    
    print("\n" + "=" * 50)
    print(f"📊 Summary: {available_count}/{len(unique_models)} models available")
    
    if unavailable_models:
        print(f"\n⚠️  Missing models: {', '.join(unavailable_models)}")
        print("\n💡 To install missing models, run:")
        for model in unavailable_models:
            print(f"   ollama pull {model}")
    else:
        print("\n🎉 All models are available and ready!")
    
    # Test agent assignments
    print("\n🤖 Agent-to-Model Assignments:")
    print("-" * 40)
    for agent, model in ai.models.items():
        available = model in [m for m in unique_models if m not in unavailable_models]
        status = "✅" if available else "❌"
        print(f"{agent:<20} → {model:<25} {status}")

if __name__ == "__main__":
    asyncio.run(test_model_availability())