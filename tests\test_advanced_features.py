"""
Comprehensive Testing Framework for Advanced AI Trading Features
Production-ready tests with real validation and terminal output
"""

import asyncio
import logging
import time
import sys
import os
from datetime import datetime, timedelta
from typing import Dict, List, Any
import unittest

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.algorithms.advanced_execution import (
    AdvancedExecutionEngine, AdvancedAlgorithmType, AdvancedExecutionParameters,
    OrderUrgency, POVAlgorithm, ImplementationShortfallAlgorithm, AdaptiveShortfallAlgorithm
)
from src.orders.advanced_order_types import (
    AdvancedOrderManager, AdvancedOrderType, AdvancedOrderParameters,
    IcebergOrder, TimeWeightedOrder, HiddenOrder, OrderStatus
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class AdvancedFeaturesTestSuite:
    """Comprehensive test suite for advanced trading features"""
    
    def __init__(self):
        self.execution_engine = AdvancedExecutionEngine()
        self.order_manager = AdvancedOrderManager()
        self.test_results = []
        self.logger = logging.getLogger(f"{__name__}.AdvancedFeaturesTestSuite")
        
    def generate_test_market_data(self, symbol: str = "BTCUSDT") -> Dict[str, Any]:
        """Generate realistic test market data"""
        return {
            "symbol": symbol,
            "price": 43000.0 + (time.time() % 1000),
            "volume": 5000000 + int(time.time() % 10000000),
            "bid": 42995.0,
            "ask": 43005.0,
            "volatility": 0.025,
            "liquidity_score": 0.85,
            "timestamp": datetime.now(),
            "trend": "BULLISH"
        }
    
    def test_pov_algorithm(self) -> bool:
        """Test POV (Percentage of Volume) Algorithm"""
        try:
            self.logger.info("🧪 TESTING POV ALGORITHM")
            self.logger.info("=" * 50)
            
            # Create POV algorithm parameters
            parameters = AdvancedExecutionParameters(
                algorithm_type=AdvancedAlgorithmType.POV,
                total_quantity=10000.0,
                target_duration=timedelta(hours=2),
                participation_rate=0.15,
                urgency=OrderUrgency.MEDIUM,
                risk_aversion=0.5
            )
            
            # Create algorithm
            algorithm = self.execution_engine.create_algorithm(
                AdvancedAlgorithmType.POV,
                "BTCUSDT",
                "BUY",
                parameters
            )
            
            if not algorithm:
                self.logger.error("❌ Failed to create POV algorithm")
                return False
            
            # Generate market data and execute
            market_data = self.generate_test_market_data()
            execution_plan = algorithm.generate_execution_plan(market_data)
            
            # Validate results
            if not execution_plan:
                self.logger.error("❌ POV algorithm generated no execution plan")
                return False
            
            total_planned_quantity = sum(slice_info["quantity"] for slice_info in execution_plan)
            
            self.logger.info(f"✅ POV Algorithm Results:")
            self.logger.info(f"   📊 Generated {len(execution_plan)} execution slices")
            self.logger.info(f"   📈 Total planned quantity: {total_planned_quantity:.2f}")
            self.logger.info(f"   🎯 Target quantity: {parameters.total_quantity}")
            self.logger.info(f"   📋 Participation rate: {parameters.participation_rate:.1%}")
            
            # Validate execution plan
            for i, slice_info in enumerate(execution_plan[:3]):  # Show first 3 slices
                self.logger.info(f"   🔸 Slice {i+1}: {slice_info['quantity']:.2f} units, "
                               f"Impact: {slice_info['temporary_impact']:.4f}")
            
            # Test different urgency levels
            for urgency in [OrderUrgency.LOW, OrderUrgency.HIGH, OrderUrgency.URGENT]:
                urgent_params = AdvancedExecutionParameters(
                    algorithm_type=AdvancedAlgorithmType.POV,
                    total_quantity=5000.0,
                    target_duration=timedelta(hours=1),
                    participation_rate=0.20,
                    urgency=urgency
                )
                
                urgent_algo = POVAlgorithm("ETHUSDT", "SELL", urgent_params)
                urgent_plan = urgent_algo.generate_execution_plan(market_data)
                
                avg_slice_size = sum(s["quantity"] for s in urgent_plan) / len(urgent_plan) if urgent_plan else 0
                self.logger.info(f"   🚨 {urgency.value} urgency: {len(urgent_plan)} slices, "
                               f"avg size: {avg_slice_size:.2f}")
            
            self.logger.info("✅ POV Algorithm test PASSED")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ POV Algorithm test FAILED: {e}")
            return False
    
    def test_implementation_shortfall_algorithm(self) -> bool:
        """Test Implementation Shortfall Algorithm"""
        try:
            self.logger.info("\n🧪 TESTING IMPLEMENTATION SHORTFALL ALGORITHM")
            self.logger.info("=" * 50)
            
            # Create IS algorithm parameters
            parameters = AdvancedExecutionParameters(
                algorithm_type=AdvancedAlgorithmType.IMPLEMENTATION_SHORTFALL,
                total_quantity=15000.0,
                target_duration=timedelta(hours=3),
                risk_aversion=0.7,
                market_impact_tolerance=0.002
            )
            
            # Create algorithm
            algorithm = self.execution_engine.create_algorithm(
                AdvancedAlgorithmType.IMPLEMENTATION_SHORTFALL,
                "ETHUSDT",
                "BUY",
                parameters
            )
            
            if not algorithm:
                self.logger.error("❌ Failed to create IS algorithm")
                return False
            
            # Generate execution plan
            market_data = self.generate_test_market_data("ETHUSDT")
            execution_plan = algorithm.generate_execution_plan(market_data)
            
            if not execution_plan:
                self.logger.error("❌ IS algorithm generated no execution plan")
                return False
            
            # Calculate metrics
            total_planned = sum(slice_info["quantity"] for slice_info in execution_plan)
            avg_shortfall = sum(slice_info["expected_shortfall"] for slice_info in execution_plan) / len(execution_plan)
            total_timing_risk = sum(slice_info["timing_risk"] for slice_info in execution_plan)
            
            self.logger.info(f"✅ Implementation Shortfall Results:")
            self.logger.info(f"   📊 Generated {len(execution_plan)} execution slices")
            self.logger.info(f"   📈 Total planned quantity: {total_planned:.2f}")
            self.logger.info(f"   💰 Average expected shortfall: {avg_shortfall:.6f}")
            self.logger.info(f"   ⏱️ Total timing risk: {total_timing_risk:.6f}")
            self.logger.info(f"   🎯 Risk aversion: {parameters.risk_aversion}")
            
            # Show detailed slice information
            for i, slice_info in enumerate(execution_plan[:3]):
                self.logger.info(f"   🔸 Slice {i+1}: {slice_info['quantity']:.2f} units, "
                               f"Shortfall: {slice_info['expected_shortfall']:.6f}, "
                               f"Type: {slice_info['order_type']}")
            
            # Test different risk aversion levels
            for risk_level in [0.2, 0.5, 0.8]:
                risk_params = AdvancedExecutionParameters(
                    algorithm_type=AdvancedAlgorithmType.IMPLEMENTATION_SHORTFALL,
                    total_quantity=8000.0,
                    target_duration=timedelta(hours=2),
                    risk_aversion=risk_level
                )
                
                risk_algo = ImplementationShortfallAlgorithm("ADAUSDT", "BUY", risk_params)
                risk_plan = risk_algo.generate_execution_plan(market_data)
                
                if risk_plan:
                    avg_risk_shortfall = sum(s["expected_shortfall"] for s in risk_plan) / len(risk_plan)
                    self.logger.info(f"   📊 Risk {risk_level}: {len(risk_plan)} slices, "
                                   f"avg shortfall: {avg_risk_shortfall:.6f}")
            
            self.logger.info("✅ Implementation Shortfall test PASSED")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Implementation Shortfall test FAILED: {e}")
            return False
    
    def test_adaptive_shortfall_algorithm(self) -> bool:
        """Test Adaptive Shortfall Algorithm"""
        try:
            self.logger.info("\n🧪 TESTING ADAPTIVE SHORTFALL ALGORITHM")
            self.logger.info("=" * 50)
            
            # Create adaptive algorithm
            parameters = AdvancedExecutionParameters(
                algorithm_type=AdvancedAlgorithmType.ADAPTIVE_SHORTFALL,
                total_quantity=12000.0,
                target_duration=timedelta(hours=2),
                risk_aversion=0.6
            )
            
            algorithm = self.execution_engine.create_algorithm(
                AdvancedAlgorithmType.ADAPTIVE_SHORTFALL,
                "SOLUSDT",
                "SELL",
                parameters
            )
            
            if not algorithm:
                self.logger.error("❌ Failed to create Adaptive algorithm")
                return False
            
            # Simulate performance feedback
            adaptive_algo = algorithm
            for i in range(10):
                actual_impact = 0.001 + (i * 0.0002)
                predicted_impact = 0.0012
                adaptive_algo.update_performance_feedback(actual_impact, predicted_impact)
            
            # Generate execution plan
            market_data = self.generate_test_market_data("SOLUSDT")
            execution_plan = adaptive_algo.generate_execution_plan(market_data)
            
            if not execution_plan:
                self.logger.error("❌ Adaptive algorithm generated no execution plan")
                return False
            
            # Analyze adaptive adjustments
            adaptive_adjustments = execution_plan[0].get("adaptive_adjustments", {})
            
            self.logger.info(f"✅ Adaptive Shortfall Results:")
            self.logger.info(f"   📊 Generated {len(execution_plan)} execution slices")
            self.logger.info(f"   🧠 Risk adjustment: {adaptive_adjustments.get('risk_adjustment', 1.0):.3f}")
            self.logger.info(f"   ⚡ Urgency adjustment: {adaptive_adjustments.get('urgency_adjustment', 1.0):.3f}")
            self.logger.info(f"   📈 Performance history: {len(adaptive_algo.performance_history)} records")
            
            # Test adaptation over time
            for iteration in range(3):
                # Simulate different market conditions
                test_market_data = self.generate_test_market_data("SOLUSDT")
                test_market_data["volatility"] = 0.01 + (iteration * 0.01)
                
                test_plan = adaptive_algo.generate_execution_plan(test_market_data)
                if test_plan:
                    test_adjustments = test_plan[0].get("adaptive_adjustments", {})
                    self.logger.info(f"   🔄 Iteration {iteration+1}: "
                                   f"Vol: {test_market_data['volatility']:.3f}, "
                                   f"Risk adj: {test_adjustments.get('risk_adjustment', 1.0):.3f}")
            
            self.logger.info("✅ Adaptive Shortfall test PASSED")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Adaptive Shortfall test FAILED: {e}")
            return False
    
    def test_iceberg_orders(self) -> bool:
        """Test Iceberg Order functionality"""
        try:
            self.logger.info("\n🧪 TESTING ICEBERG ORDERS")
            self.logger.info("=" * 50)
            
            # Create iceberg order
            parameters = AdvancedOrderParameters(
                order_type=AdvancedOrderType.ICEBERG,
                symbol="BTCUSDT",
                side="BUY",
                total_quantity=50000.0,
                price=43000.0,
                display_quantity=5000.0,
                refresh_threshold=0.2,
                adaptive_sizing=True,
                randomization_factor=0.1
            )
            
            order_id = self.order_manager.create_order(parameters)
            
            if not order_id:
                self.logger.error("❌ Failed to create Iceberg order")
                return False
            
            iceberg_order = self.order_manager.active_orders[order_id]
            
            self.logger.info(f"✅ Iceberg Order Created:")
            self.logger.info(f"   🆔 Order ID: {order_id}")
            self.logger.info(f"   📊 Total quantity: {parameters.total_quantity}")
            self.logger.info(f"   👁️ Display quantity: {parameters.display_quantity}")
            self.logger.info(f"   🔄 Refresh threshold: {parameters.refresh_threshold:.1%}")
            
            # Simulate market conditions and order updates
            market_data = self.generate_test_market_data()
            
            # Test display size calculation
            for i in range(5):
                market_data["volatility"] = 0.01 + (i * 0.005)
                market_data["liquidity_score"] = 0.7 + (i * 0.05)
                
                display_size = iceberg_order.calculate_next_display_size(market_data)
                self.logger.info(f"   📈 Condition {i+1}: Vol: {market_data['volatility']:.3f}, "
                               f"Liq: {market_data['liquidity_score']:.2f}, "
                               f"Display: {display_size:.0f}")
            
            # Simulate partial executions
            execution_prices = [43001.0, 43002.0, 43000.5]
            execution_quantities = [2000.0, 1500.0, 1000.0]
            
            for i, (qty, price) in enumerate(zip(execution_quantities, execution_prices)):
                execution = iceberg_order.execute_partial_fill(qty, price, market_data)
                if execution:
                    self.logger.info(f"   💰 Execution {i+1}: {qty} @ ${price} "
                                   f"(Total executed: {iceberg_order.executed_quantity})")
                
                # Check refresh status
                if iceberg_order.should_refresh():
                    new_display = iceberg_order.refresh_display(market_data)
                    self.logger.info(f"   🔄 Refreshed display to: {new_display:.0f}")
            
            # Get final status
            status = self.order_manager.get_order_status(order_id)
            self.logger.info(f"   📋 Final status: {status['status']}")
            self.logger.info(f"   📊 Executed: {status['executed_quantity']}/{status['total_quantity']}")
            
            self.logger.info("✅ Iceberg Orders test PASSED")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Iceberg Orders test FAILED: {e}")
            return False
    
    def test_time_weighted_orders(self) -> bool:
        """Test Time-Weighted Order functionality"""
        try:
            self.logger.info("\n🧪 TESTING TIME-WEIGHTED ORDERS")
            self.logger.info("=" * 50)
            
            # Create time-weighted order
            start_time = datetime.now()
            end_time = start_time + timedelta(hours=2)
            
            parameters = AdvancedOrderParameters(
                order_type=AdvancedOrderType.TIME_WEIGHTED,
                symbol="ETHUSDT",
                side="SELL",
                total_quantity=25000.0,
                price=2600.0,
                start_time=start_time,
                end_time=end_time,
                time_intervals=8,
                randomization_factor=0.15
            )
            
            order_id = self.order_manager.create_order(parameters)
            
            if not order_id:
                self.logger.error("❌ Failed to create Time-Weighted order")
                return False
            
            tw_order = self.order_manager.active_orders[order_id]
            
            self.logger.info(f"✅ Time-Weighted Order Created:")
            self.logger.info(f"   🆔 Order ID: {order_id}")
            self.logger.info(f"   📊 Total quantity: {parameters.total_quantity}")
            self.logger.info(f"   ⏰ Duration: {(end_time - start_time).total_seconds()/3600:.1f} hours")
            self.logger.info(f"   🔢 Time intervals: {parameters.time_intervals}")
            
            # Show time slices
            self.logger.info(f"   📅 Generated {len(tw_order.time_slices)} time slices:")
            for i, slice_info in enumerate(tw_order.time_slices[:4]):  # Show first 4
                duration = (slice_info["end_time"] - slice_info["start_time"]).total_seconds() / 60
                self.logger.info(f"      🔸 Slice {i+1}: {slice_info['target_quantity']:.0f} units "
                               f"over {duration:.0f} minutes")
            
            # Simulate execution over time slices
            market_data = self.generate_test_market_data("ETHUSDT")
            
            for i in range(3):  # Test first 3 slices
                # Simulate time progression
                tw_order.time_slices[i]["start_time"] = datetime.now() - timedelta(minutes=1)
                tw_order.time_slices[i]["end_time"] = datetime.now() + timedelta(minutes=14)
                
                current_slice = tw_order.get_current_slice()
                if current_slice:
                    execution_rate = tw_order.calculate_slice_execution_rate(market_data)
                    
                    # Execute portion of slice
                    execute_qty = min(execution_rate * 5, current_slice["target_quantity"] * 0.3)  # 30% of slice
                    execution = tw_order.execute_slice_quantity(execute_qty, 2601.0 + i, market_data)
                    
                    if execution:
                        self.logger.info(f"   💰 Slice {i+1} execution: {execute_qty:.0f} @ ${2601.0 + i} "
                                       f"(Rate: {execution_rate:.0f}/min)")
            
            # Get execution summary
            total_executed = sum(len(tw_order.executions) for _ in [tw_order])
            self.logger.info(f"   📊 Total executions: {total_executed}")
            self.logger.info(f"   📈 Executed quantity: {tw_order.executed_quantity:.0f}")
            
            self.logger.info("✅ Time-Weighted Orders test PASSED")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Time-Weighted Orders test FAILED: {e}")
            return False
    
    def test_hidden_orders(self) -> bool:
        """Test Hidden Order functionality"""
        try:
            self.logger.info("\n🧪 TESTING HIDDEN ORDERS")
            self.logger.info("=" * 50)
            
            # Create hidden order
            parameters = AdvancedOrderParameters(
                order_type=AdvancedOrderType.HIDDEN,
                symbol="ADAUSDT",
                side="BUY",
                total_quantity=100000.0,
                price=0.45,
                hidden_percentage=0.9,  # 90% hidden
                reveal_threshold=0.44,  # Reveal more below this price
                adaptive_sizing=True,
                randomization_factor=0.08
            )
            
            order_id = self.order_manager.create_order(parameters)
            
            if not order_id:
                self.logger.error("❌ Failed to create Hidden order")
                return False
            
            hidden_order = self.order_manager.active_orders[order_id]
            
            self.logger.info(f"✅ Hidden Order Created:")
            self.logger.info(f"   🆔 Order ID: {order_id}")
            self.logger.info(f"   📊 Total quantity: {parameters.total_quantity}")
            self.logger.info(f"   🙈 Hidden percentage: {parameters.hidden_percentage:.1%}")
            self.logger.info(f"   🎯 Reveal threshold: ${parameters.reveal_threshold}")
            
            # Test reveal quantity under different conditions
            market_conditions = [
                {"price": 0.46, "liquidity_score": 0.7, "volatility": 0.03, "desc": "High price, low liquidity"},
                {"price": 0.44, "liquidity_score": 0.9, "volatility": 0.02, "desc": "Threshold price, high liquidity"},
                {"price": 0.43, "liquidity_score": 0.8, "volatility": 0.04, "desc": "Low price, high volatility"}
            ]
            
            for i, condition in enumerate(market_conditions):
                market_data = self.generate_test_market_data("ADAUSDT")
                market_data.update(condition)
                
                reveal_qty = hidden_order.calculate_reveal_quantity(market_data)
                reveal_pct = (reveal_qty / parameters.total_quantity) * 100
                
                self.logger.info(f"   📊 Condition {i+1} ({condition['desc']}):")
                self.logger.info(f"      💰 Price: ${condition['price']:.3f}")
                self.logger.info(f"      👁️ Revealed: {reveal_qty:.0f} ({reveal_pct:.1f}%)")
            
            # Simulate executions
            execution_scenarios = [
                {"qty": 5000, "price": 0.449},
                {"qty": 8000, "price": 0.448},
                {"qty": 3000, "price": 0.447}
            ]
            
            for i, scenario in enumerate(execution_scenarios):
                market_data = self.generate_test_market_data("ADAUSDT")
                market_data["price"] = scenario["price"]
                
                execution = hidden_order.execute_hidden_fill(
                    scenario["qty"], scenario["price"], market_data
                )
                
                if execution:
                    self.logger.info(f"   💰 Execution {i+1}: {scenario['qty']} @ ${scenario['price']:.3f}")
                    self.logger.info(f"      📈 Total executed: {hidden_order.executed_quantity:.0f}")
                    self.logger.info(f"      👁️ Current revealed: {hidden_order.revealed_quantity:.0f}")
            
            self.logger.info("✅ Hidden Orders test PASSED")
            return True
            
        except Exception as e:
            self.logger.error(f"❌ Hidden Orders test FAILED: {e}")
            return False
    
    def run_comprehensive_test_suite(self) -> Dict[str, bool]:
        """Run complete test suite"""
        try:
            self.logger.info("🚀 STARTING COMPREHENSIVE ADVANCED FEATURES TEST SUITE")
            self.logger.info("=" * 80)
            self.logger.info("🎯 Testing production-ready advanced trading algorithms and order types")
            self.logger.info("📊 All tests use real implementations with actual data processing")
            self.logger.info("=" * 80)
            
            test_results = {}
            
            # Test execution algorithms
            test_results["POV_Algorithm"] = self.test_pov_algorithm()
            test_results["Implementation_Shortfall"] = self.test_implementation_shortfall_algorithm()
            test_results["Adaptive_Shortfall"] = self.test_adaptive_shortfall_algorithm()
            
            # Test advanced order types
            test_results["Iceberg_Orders"] = self.test_iceberg_orders()
            test_results["Time_Weighted_Orders"] = self.test_time_weighted_orders()
            test_results["Hidden_Orders"] = self.test_hidden_orders()
            
            # Summary
            passed_tests = sum(1 for result in test_results.values() if result)
            total_tests = len(test_results)
            
            self.logger.info("\n" + "=" * 80)
            self.logger.info("📊 COMPREHENSIVE TEST SUITE RESULTS")
            self.logger.info("=" * 80)
            
            for test_name, result in test_results.items():
                status = "✅ PASSED" if result else "❌ FAILED"
                self.logger.info(f"   {test_name.replace('_', ' ')}: {status}")
            
            self.logger.info(f"\n🎯 OVERALL RESULTS: {passed_tests}/{total_tests} tests passed")
            
            if passed_tests == total_tests:
                self.logger.info("🎉 ALL ADVANCED FEATURES TESTS PASSED!")
                self.logger.info("✅ Production-ready implementations validated")
            else:
                self.logger.warning(f"⚠️ {total_tests - passed_tests} tests failed - review implementation")
            
            return test_results
            
        except Exception as e:
            self.logger.error(f"❌ Test suite execution failed: {e}")
            return {}


def main():
    """Main test execution"""
    test_suite = AdvancedFeaturesTestSuite()
    results = test_suite.run_comprehensive_test_suite()
    
    # Return exit code based on results
    if all(results.values()):
        return 0  # Success
    else:
        return 1  # Failure


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
