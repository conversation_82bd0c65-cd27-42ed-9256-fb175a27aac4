"""
Test AI Models Integration - Verify all requested models are properly configured
"""

import asyncio
import logging
import time
from typing import Dict, List, Any
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from optimized_ai_config import OptimizedAIService

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class AIModelTester:
    """Test AI model configuration and availability"""
    
    def __init__(self):
        self.ai_service = OptimizedAIService()
        self.test_results = {}
        
    def test_model_configuration(self):
        """Test that all requested models are configured"""
        logger.info("🧪 Testing AI Model Configuration...")
        
        # List of all requested models from user requirements
        requested_models = [
            "marco-o1:7b",
            "magistral:24b", 
            "command-r:35b",
            "cogito:32b",
            "gemma3:27b",
            "mistral-small:24b",
            "falcon3:10b",
            "granite3.3:8b",
            "qwen3:32b",
            "deepseek-r1:32b",
            "phi4-reasoning:plus",
            "nemotron-mini:4b"
        ]
        
        configured_models = self.ai_service.get_all_available_models()
        
        logger.info(f"📊 Total configured models: {len(configured_models)}")
        logger.info(f"📋 Requested models: {len(requested_models)}")
        
        # Check each requested model
        missing_models = []
        found_models = []
        
        for model in requested_models:
            if model in configured_models:
                found_models.append(model)
                logger.info(f"  ✅ {model} - CONFIGURED")
            else:
                missing_models.append(model)
                logger.warning(f"  ❌ {model} - MISSING")
        
        # Summary
        coverage_rate = (len(found_models) / len(requested_models)) * 100
        logger.info(f"📈 Model Coverage: {coverage_rate:.1f}% ({len(found_models)}/{len(requested_models)})")
        
        if missing_models:
            logger.warning(f"⚠️ Missing models: {missing_models}")
        else:
            logger.info("🎉 All requested models are configured!")
        
        return {
            "total_requested": len(requested_models),
            "total_configured": len(found_models),
            "coverage_rate": coverage_rate,
            "missing_models": missing_models,
            "found_models": found_models
        }
    
    def test_model_parameters(self):
        """Test model-specific parameters"""
        logger.info("🧪 Testing Model Parameters...")
        
        test_models = [
            "nemotron-mini:4b",
            "granite3.3:8b", 
            "deepseek-r1:32b",
            "phi4-reasoning:plus",
            "magistral:24b",
            "command-r:35b"
        ]
        
        parameter_results = {}
        
        for model in test_models:
            config = self.ai_service.get_model_config(model)
            
            # Verify required parameters exist
            required_params = ["context_length", "temperature", "max_tokens", "timeout"]
            has_all_params = all(param in config for param in required_params)
            
            parameter_results[model] = {
                "config": config,
                "has_all_params": has_all_params,
                "context_length": config.get("context_length", 0),
                "timeout": config.get("timeout", 0)
            }
            
            status = "✅" if has_all_params else "❌"
            logger.info(f"  {status} {model}: Context={config.get('context_length', 'N/A')}, "
                       f"Timeout={config.get('timeout', 'N/A')}s")
        
        return parameter_results
    
    def test_task_model_rotation(self):
        """Test model rotation for different trading tasks"""
        logger.info("🧪 Testing Task-Model Rotation...")
        
        trading_tasks = [
            "real_time_analysis",
            "deep_analysis", 
            "risk_assessment",
            "market_prediction",
            "strategy_development",
            "compliance_monitoring",
            "performance_analysis",
            "technical_analysis",
            "portfolio_optimization"
        ]
        
        rotation_results = {}
        
        for task in trading_tasks:
            # Test different performance tiers
            tiers = ["ultra_fast", "fast", "standard", "advanced", "premium"]
            task_results = {}
            
            for tier in tiers:
                try:
                    selected_model = self.ai_service.get_model_for_task(task, tier)
                    task_results[tier] = selected_model
                    logger.info(f"  📋 {task} ({tier}): {selected_model}")
                except Exception as e:
                    task_results[tier] = f"ERROR: {e}"
                    logger.error(f"  ❌ {task} ({tier}): {e}")
            
            rotation_results[task] = task_results
        
        return rotation_results
    
    def test_performance_tiers(self):
        """Test performance tier distribution"""
        logger.info("🧪 Testing Performance Tiers...")
        
        tier_results = {}
        
        for tier_name, models in self.ai_service.model_tiers.items():
            tier_results[tier_name] = {
                "model_count": len(models),
                "models": models,
                "avg_context_length": 0
            }
            
            # Calculate average context length for tier
            context_lengths = []
            for model in models:
                config = self.ai_service.get_model_config(model)
                context_lengths.append(config.get("context_length", 4096))
            
            if context_lengths:
                tier_results[tier_name]["avg_context_length"] = sum(context_lengths) / len(context_lengths)
            
            logger.info(f"  🏆 {tier_name}: {len(models)} models, "
                       f"avg context: {tier_results[tier_name]['avg_context_length']:.0f}")
        
        return tier_results
    
    def generate_comprehensive_report(self):
        """Generate comprehensive AI models integration report"""
        logger.info("📊 Generating Comprehensive AI Models Report...")
        
        # Run all tests
        config_results = self.test_model_configuration()
        param_results = self.test_model_parameters()
        rotation_results = self.test_task_model_rotation()
        tier_results = self.test_performance_tiers()
        
        # Generate summary
        report = {
            "timestamp": time.strftime("%Y-%m-%d %H:%M:%S"),
            "model_configuration": config_results,
            "parameter_validation": param_results,
            "task_rotation": rotation_results,
            "performance_tiers": tier_results,
            "summary": {
                "total_models_configured": len(self.ai_service.get_all_available_models()),
                "requested_models_coverage": config_results["coverage_rate"],
                "performance_tiers_count": len(self.ai_service.model_tiers),
                "trading_tasks_supported": len(rotation_results),
                "integration_status": "COMPLETE" if config_results["coverage_rate"] >= 90 else "PARTIAL"
            }
        }
        
        # Print summary
        logger.info("=" * 80)
        logger.info("🎯 AI MODELS INTEGRATION SUMMARY")
        logger.info("=" * 80)
        logger.info(f"📊 Total Models Configured: {report['summary']['total_models_configured']}")
        logger.info(f"📈 Requested Models Coverage: {report['summary']['requested_models_coverage']:.1f}%")
        logger.info(f"🏆 Performance Tiers: {report['summary']['performance_tiers_count']}")
        logger.info(f"📋 Trading Tasks Supported: {report['summary']['trading_tasks_supported']}")
        logger.info(f"✅ Integration Status: {report['summary']['integration_status']}")
        logger.info("=" * 80)
        
        if report['summary']['integration_status'] == "COMPLETE":
            logger.info("🎉 ALL AI MODELS SUCCESSFULLY INTEGRATED!")
        else:
            logger.warning("⚠️ PARTIAL INTEGRATION - Some models may need attention")
        
        return report


def main():
    """Main test execution"""
    logger.info("🚀 Starting AI Models Integration Tests")
    logger.info("=" * 60)
    
    tester = AIModelTester()
    
    try:
        # Run comprehensive tests
        report = tester.generate_comprehensive_report()
        
        # Test specific model availability
        logger.info("\n🔍 Testing Specific Model Availability...")
        
        critical_models = [
            "nemotron-mini:4b",      # Ultra-fast
            "deepseek-r1:32b",       # Advanced reasoning  
            "phi4-reasoning:plus",   # Premium reasoning
            "magistral:24b",         # Newly integrated
            "qwen3:32b"              # Newly integrated
        ]
        
        for model in critical_models:
            available = tester.ai_service.validate_model_availability(model)
            status = "✅ AVAILABLE" if available else "❌ NOT AVAILABLE"
            logger.info(f"  {model}: {status}")
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 AI Models Integration Tests Completed!")
        
        return report
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        raise


if __name__ == "__main__":
    main()
