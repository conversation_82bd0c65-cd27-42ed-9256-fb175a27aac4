"""
Complete Advanced System Integration Test
Production-ready testing of all advanced features working together
"""

import asyncio
import logging
import time
import sys
import os
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.algorithms.advanced_execution import (
    AdvancedExecutionEngine, AdvancedAlgorithmType, AdvancedExecutionParameters, OrderUrgency
)
from src.orders.advanced_order_types import (
    AdvancedOrderManager, AdvancedOrderType, AdvancedOrderParameters
)
from src.ai.advanced_ml_models import (
    AdvancedMLModelManager, ModelType, TrainingData, PredictionHorizon
)
from src.portfolio.advanced_optimization import (
    AdvancedPortfolioOptimizer, OptimizationMethod, OptimizationConstraints
)

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


class CompleteAdvancedSystemTest:
    """Complete integration test for all advanced features"""
    
    def __init__(self):
        self.execution_engine = AdvancedExecutionEngine()
        self.order_manager = AdvancedOrderManager()
        self.ml_manager = AdvancedMLModelManager()
        self.portfolio_optimizer = AdvancedPortfolioOptimizer()
        self.test_results = {}
        self.logger = logging.getLogger(f"{__name__}.CompleteAdvancedSystemTest")
        
    def generate_comprehensive_market_data(self) -> Dict[str, Any]:
        """Generate comprehensive market data for testing"""
        symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT", "BNBUSDT"]
        
        market_data = {}
        for symbol in symbols:
            market_data[symbol] = {
                "symbol": symbol,
                "price": 100.0 + np.random.uniform(-10, 10),
                "volume": int(np.random.uniform(1000000, 10000000)),
                "bid": 99.5 + np.random.uniform(-10, 10),
                "ask": 100.5 + np.random.uniform(-10, 10),
                "volatility": np.random.uniform(0.01, 0.05),
                "liquidity_score": np.random.uniform(0.6, 1.0),
                "timestamp": datetime.now(),
                "trend": np.random.choice(["BULLISH", "BEARISH", "SIDEWAYS"])
            }
        
        return market_data
    
    def test_ml_models_integration(self) -> bool:
        """Test ML models with real data processing"""
        try:
            self.logger.info("🧠 TESTING ML MODELS INTEGRATION")
            self.logger.info("=" * 60)
            
            symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
            model_types = [
                ModelType.LSTM_PRICE_PREDICTOR,
                ModelType.RANDOM_FOREST_CLASSIFIER,
                ModelType.REINFORCEMENT_LEARNING_AGENT
            ]
            
            created_models = []
            
            # Create models for each symbol and type
            for symbol in symbols:
                for model_type in model_types:
                    model_id = self.ml_manager.create_model(model_type, symbol)
                    if model_id:
                        created_models.append((model_id, symbol, model_type))
                        self.logger.info(f"   ✅ Created {model_type.value} for {symbol}: {model_id}")
            
            # Generate training data
            n_samples = 100
            n_features = 10
            training_data = TrainingData(
                features=np.random.randn(n_samples, n_features),
                targets=np.random.randn(n_samples),
                timestamps=[datetime.now() - timedelta(days=i) for i in range(n_samples)],
                symbols=symbols * (n_samples // len(symbols) + 1),
                metadata={"data_source": "test_generation"}
            )
            
            # Train models
            trained_count = 0
            for model_id, symbol, model_type in created_models:
                if self.ml_manager.train_model(model_id, training_data):
                    trained_count += 1
                    self.logger.info(f"   🎓 Trained {model_type.value} for {symbol}")
            
            # Generate predictions
            market_data = self.generate_comprehensive_market_data()
            total_predictions = 0
            
            for symbol in symbols:
                predictions = self.ml_manager.get_predictions(symbol, market_data[symbol])
                total_predictions += len(predictions)
                
                if predictions:
                    self.logger.info(f"   🔮 {symbol}: {len(predictions)} predictions generated")
                    for pred in predictions:
                        self.logger.info(f"      📊 {pred.model_type.value}: {pred.prediction_value:.4f} "
                                       f"(confidence: {pred.confidence:.2f})")
                
                # Test ensemble prediction
                ensemble_pred = self.ml_manager.get_ensemble_prediction(symbol, market_data[symbol])
                if ensemble_pred:
                    self.logger.info(f"   🎯 Ensemble for {symbol}: {ensemble_pred.prediction_value:.4f} "
                                   f"(confidence: {ensemble_pred.confidence:.2f})")
            
            # Get model performance
            for model_id, symbol, model_type in created_models[:3]:  # Check first 3
                performance = self.ml_manager.get_model_performance(model_id)
                if performance.get("status") != "ERROR":
                    self.logger.info(f"   📈 {model_type.value} performance: "
                                   f"predictions: {performance.get('prediction_count', 0)}, "
                                   f"avg confidence: {performance.get('average_confidence', 0):.2f}")
            
            success = len(created_models) >= 6 and trained_count >= 3 and total_predictions >= 6
            
            if success:
                self.logger.info("✅ ML Models Integration test PASSED")
            else:
                self.logger.error("❌ ML Models Integration test FAILED")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ ML Models Integration test FAILED: {e}")
            return False
    
    def test_portfolio_optimization_integration(self) -> bool:
        """Test portfolio optimization with real calculations"""
        try:
            self.logger.info("\n💼 TESTING PORTFOLIO OPTIMIZATION INTEGRATION")
            self.logger.info("=" * 60)
            
            # Generate portfolio data
            n_assets = 5
            symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT", "SOLUSDT", "BNBUSDT"]
            
            # Create realistic expected returns and covariance matrix
            expected_returns = np.random.uniform(0.05, 0.15, n_assets)  # 5-15% annual returns
            
            # Generate positive definite covariance matrix
            A = np.random.randn(n_assets, n_assets)
            covariance_matrix = A @ A.T * 0.01  # Scale to reasonable volatilities
            
            # Market caps (for Black-Litterman)
            market_caps = np.random.uniform(1e9, 1e12, n_assets)
            
            # Returns history
            returns_history = np.random.multivariate_normal(
                expected_returns / 252,  # Daily returns
                covariance_matrix / 252,
                size=252
            )
            
            assets_data = {
                "expected_returns": expected_returns,
                "covariance_matrix": covariance_matrix,
                "market_caps": market_caps,
                "returns_history": returns_history,
                "symbols": symbols
            }
            
            # Test different optimization methods
            optimization_methods = [
                OptimizationMethod.MEAN_VARIANCE,
                OptimizationMethod.RISK_PARITY,
                OptimizationMethod.BLACK_LITTERMAN,
                OptimizationMethod.MINIMUM_VARIANCE
            ]
            
            constraints = OptimizationConstraints(
                max_weight=0.4,
                min_weight=0.05,
                max_turnover=0.5,
                max_leverage=1.0
            )
            
            optimization_results = []
            
            for method in optimization_methods:
                self.logger.info(f"   🔧 Testing {method.value} optimization...")
                
                # Method-specific parameters
                if method == OptimizationMethod.MEAN_VARIANCE:
                    kwargs = {"risk_aversion": 2.0}
                elif method == OptimizationMethod.BLACK_LITTERMAN:
                    # Add some views
                    views = {0: (0.12, 0.8), 1: (0.08, 0.6)}  # Asset 0: 12% return with 80% confidence
                    kwargs = {"views": views, "risk_aversion": 3.0}
                else:
                    kwargs = {}
                
                result = self.portfolio_optimizer.optimize_portfolio(
                    method, assets_data, constraints, **kwargs
                )
                
                if result:
                    optimization_results.append(result)
                    metrics = result.get("metrics", {})
                    
                    self.logger.info(f"      📊 Expected return: {metrics.get('expected_return', 0):.4f}")
                    self.logger.info(f"      📉 Volatility: {metrics.get('volatility', 0):.4f}")
                    self.logger.info(f"      📈 Sharpe ratio: {metrics.get('sharpe_ratio', 0):.3f}")
                    
                    # Show top 3 weights
                    weights = result.get("weights", {})
                    sorted_weights = sorted(weights.items(), key=lambda x: x[1], reverse=True)
                    top_weights = sorted_weights[:3]
                    self.logger.info(f"      🏆 Top weights: {top_weights}")
            
            # Test rebalancing
            if optimization_results:
                current_weights = {"asset_0": 0.3, "asset_1": 0.25, "asset_2": 0.2, "asset_3": 0.15, "asset_4": 0.1}
                target_weights = optimization_results[0]["weights"]
                
                rebalancing = self.portfolio_optimizer.rebalance_portfolio(
                    current_weights, target_weights, transaction_costs=0.001
                )
                
                if rebalancing:
                    self.logger.info(f"   🔄 Rebalancing: {len(rebalancing['trades'])} trades, "
                                   f"turnover: {rebalancing['total_turnover']:.3f}")
            
            # Get optimization history
            history = self.portfolio_optimizer.get_optimization_history(limit=3)
            self.logger.info(f"   📚 Optimization history: {len(history)} records")
            
            success = len(optimization_results) >= 3
            
            if success:
                self.logger.info("✅ Portfolio Optimization Integration test PASSED")
            else:
                self.logger.error("❌ Portfolio Optimization Integration test FAILED")
            
            return success
            
        except Exception as e:
            self.logger.error(f"❌ Portfolio Optimization Integration test FAILED: {e}")
            return False
    
    def test_complete_system_workflow(self) -> bool:
        """Test complete workflow: ML predictions → Portfolio optimization → Order execution"""
        try:
            self.logger.info("\n🔄 TESTING COMPLETE SYSTEM WORKFLOW")
            self.logger.info("=" * 60)
            
            symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
            market_data = self.generate_comprehensive_market_data()
            
            # Step 1: Generate ML predictions for all symbols
            self.logger.info("   🧠 Step 1: Generating ML predictions...")
            
            ml_predictions = {}
            for symbol in symbols:
                # Create and use ML models
                lstm_id = self.ml_manager.create_model(ModelType.LSTM_PRICE_PREDICTOR, symbol)
                rf_id = self.ml_manager.create_model(ModelType.RANDOM_FOREST_CLASSIFIER, symbol)
                
                predictions = self.ml_manager.get_predictions(symbol, market_data[symbol])
                ensemble_pred = self.ml_manager.get_ensemble_prediction(symbol, market_data[symbol])
                
                ml_predictions[symbol] = {
                    "individual": predictions,
                    "ensemble": ensemble_pred
                }
                
                if ensemble_pred:
                    self.logger.info(f"      📊 {symbol}: ML prediction {ensemble_pred.prediction_value:.4f} "
                                   f"(confidence: {ensemble_pred.confidence:.2f})")
            
            # Step 2: Use ML predictions for portfolio optimization
            self.logger.info("   💼 Step 2: Portfolio optimization with ML inputs...")
            
            # Convert ML predictions to expected returns
            expected_returns = []
            for symbol in symbols:
                ensemble_pred = ml_predictions[symbol]["ensemble"]
                if ensemble_pred and ensemble_pred.prediction_value != 0:
                    # Convert prediction to expected return
                    current_price = market_data[symbol]["price"]
                    predicted_price = ensemble_pred.prediction_value
                    if ensemble_pred.model_type.value == "RANDOM_FOREST_CLASSIFIER":
                        # Direction prediction: convert to return estimate
                        expected_return = predicted_price * 0.05  # Scale direction to return
                    else:
                        # Price prediction: convert to return
                        expected_return = (predicted_price - current_price) / current_price
                    expected_returns.append(expected_return)
                else:
                    expected_returns.append(0.05)  # Default 5% return
            
            expected_returns = np.array(expected_returns)
            
            # Generate covariance matrix
            n_assets = len(symbols)
            A = np.random.randn(n_assets, n_assets)
            covariance_matrix = A @ A.T * 0.01
            
            assets_data = {
                "expected_returns": expected_returns,
                "covariance_matrix": covariance_matrix,
                "returns_history": np.random.randn(100, n_assets) * 0.02,
                "symbols": symbols
            }
            
            constraints = OptimizationConstraints(max_weight=0.6, min_weight=0.1)
            
            portfolio_result = self.portfolio_optimizer.optimize_portfolio(
                OptimizationMethod.MEAN_VARIANCE,
                assets_data,
                constraints,
                risk_aversion=1.5
            )
            
            if portfolio_result:
                weights = portfolio_result["weights"]
                self.logger.info(f"      🎯 Optimized portfolio weights: {weights}")
            
            # Step 3: Execute trades using advanced algorithms
            self.logger.info("   ⚡ Step 3: Executing trades with advanced algorithms...")
            
            executed_orders = 0
            
            for i, symbol in enumerate(symbols):
                target_weight = list(weights.values())[i] if weights else 0.2
                
                if target_weight > 0.15:  # Only trade significant positions
                    # Create advanced execution algorithm
                    exec_params = AdvancedExecutionParameters(
                        algorithm_type=AdvancedAlgorithmType.IMPLEMENTATION_SHORTFALL,
                        total_quantity=target_weight * 100000,  # $100k portfolio
                        target_duration=timedelta(hours=1),
                        risk_aversion=0.7
                    )
                    
                    algorithm = self.execution_engine.create_algorithm(
                        AdvancedAlgorithmType.IMPLEMENTATION_SHORTFALL,
                        symbol,
                        "BUY",
                        exec_params
                    )
                    
                    if algorithm:
                        execution_plan = self.execution_engine.execute_algorithm(
                            f"IS_{symbol}_{int(time.time())}",
                            market_data[symbol]
                        )
                        
                        if execution_plan:
                            self.logger.info(f"      💰 {symbol}: {len(execution_plan)} execution slices planned")
                            executed_orders += 1
                    
                    # Create advanced order
                    order_params = AdvancedOrderParameters(
                        order_type=AdvancedOrderType.ICEBERG,
                        symbol=symbol,
                        side="BUY",
                        total_quantity=target_weight * 50000,
                        display_quantity=target_weight * 5000,
                        adaptive_sizing=True
                    )
                    
                    order_id = self.order_manager.create_order(order_params)
                    if order_id:
                        self.logger.info(f"      📋 {symbol}: Iceberg order created {order_id}")
            
            # Step 4: Monitor and update
            self.logger.info("   📊 Step 4: System monitoring and updates...")
            
            # Process order updates
            order_updates = self.order_manager.process_order_updates(market_data["BTCUSDT"])
            self.logger.info(f"      🔄 Processed {len(order_updates)} order updates")
            
            # Check algorithm status
            for algo_id in self.execution_engine.active_algorithms.keys():
                status = self.execution_engine.get_algorithm_status(algo_id)
                if status.get("status") == "ACTIVE":
                    self.logger.info(f"      ⚙️ Algorithm {algo_id}: {status.get('progress', 0):.1%} complete")
            
            # Workflow success criteria
            ml_success = len(ml_predictions) == len(symbols)
            portfolio_success = portfolio_result is not None
            execution_success = executed_orders > 0
            
            workflow_success = ml_success and portfolio_success and execution_success
            
            self.logger.info(f"   📈 Workflow Results:")
            self.logger.info(f"      🧠 ML Predictions: {'✅' if ml_success else '❌'}")
            self.logger.info(f"      💼 Portfolio Optimization: {'✅' if portfolio_success else '❌'}")
            self.logger.info(f"      ⚡ Order Execution: {'✅' if execution_success else '❌'}")
            
            if workflow_success:
                self.logger.info("✅ Complete System Workflow test PASSED")
            else:
                self.logger.error("❌ Complete System Workflow test FAILED")
            
            return workflow_success
            
        except Exception as e:
            self.logger.error(f"❌ Complete System Workflow test FAILED: {e}")
            return False
    
    def run_complete_system_test(self) -> Dict[str, bool]:
        """Run complete advanced system test"""
        try:
            self.logger.info("🚀 STARTING COMPLETE ADVANCED SYSTEM INTEGRATION TEST")
            self.logger.info("=" * 80)
            self.logger.info("🎯 Testing all advanced features working together")
            self.logger.info("📊 Real implementations with actual data processing and calculations")
            self.logger.info("=" * 80)
            
            # Run all integration tests
            test_results = {}
            
            test_results["ML_Models_Integration"] = self.test_ml_models_integration()
            test_results["Portfolio_Optimization_Integration"] = self.test_portfolio_optimization_integration()
            test_results["Complete_System_Workflow"] = self.test_complete_system_workflow()
            
            # Summary
            passed_tests = sum(1 for result in test_results.values() if result)
            total_tests = len(test_results)
            
            self.logger.info("\n" + "=" * 80)
            self.logger.info("📊 COMPLETE ADVANCED SYSTEM TEST RESULTS")
            self.logger.info("=" * 80)
            
            for test_name, result in test_results.items():
                status = "✅ PASSED" if result else "❌ FAILED"
                self.logger.info(f"   {test_name.replace('_', ' ')}: {status}")
            
            self.logger.info(f"\n🎯 OVERALL RESULTS: {passed_tests}/{total_tests} integration tests passed")
            
            if passed_tests == total_tests:
                self.logger.info("🎉 ALL ADVANCED SYSTEM INTEGRATION TESTS PASSED!")
                self.logger.info("✅ Complete production-ready system validated")
                self.logger.info("🚀 Advanced AI trading system ready for deployment")
            else:
                self.logger.warning(f"⚠️ {total_tests - passed_tests} integration tests failed")
            
            return test_results
            
        except Exception as e:
            self.logger.error(f"❌ Complete system test execution failed: {e}")
            return {}


def main():
    """Main test execution"""
    test_suite = CompleteAdvancedSystemTest()
    results = test_suite.run_complete_system_test()
    
    # Return exit code based on results
    if all(results.values()):
        return 0  # Success
    else:
        return 1  # Failure


if __name__ == "__main__":
    exit_code = main()
    sys.exit(exit_code)
