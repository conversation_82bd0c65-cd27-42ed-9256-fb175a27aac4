"""
Comprehensive Database Connections Test
Test Redis and ClickHouse integration with real data processing
"""

import asyncio
import logging
import time
from datetime import datetime, timedelta
from typing import Dict, List, Any
import sys
import os

# Add project root to path
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from src.db.redis import RedisManager, redis_manager, ping as redis_ping
from src.db.clickhouse import <PERSON><PERSON><PERSON>ouse<PERSON>ana<PERSON>, clickhouse_manager, ping as clickhouse_ping

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class DatabaseTester:
    """Comprehensive database testing suite"""
    
    def __init__(self):
        self.redis_manager = RedisManager()
        self.clickhouse_manager = ClickHouseManager()
        self.test_results = {}
        
    async def test_redis_connection(self) -> Dict[str, Any]:
        """Test Redis connection and basic operations"""
        logger.info("🧪 Testing Redis Connection...")
        
        results = {
            "connection_test": False,
            "ping_test": False,
            "basic_operations": False,
            "serialization_test": False,
            "error_messages": []
        }
        
        try:
            # Test basic ping
            ping_result = await redis_ping()
            results["ping_test"] = ping_result
            logger.info(f"  📡 Redis Ping: {'✅ SUCCESS' if ping_result else '❌ FAILED'}")
            
            # Test connection with diagnostics
            connection_result = await self.redis_manager.test_connection()
            results["connection_test"] = connection_result
            logger.info(f"  🔗 Redis Connection: {'✅ SUCCESS' if connection_result else '❌ FAILED'}")
            
            # Test basic operations
            if connection_result:
                # Test market data storage
                test_market_data = {
                    "symbol": "BTCUSDT",
                    "price": 50000.0,
                    "volume": 1000000,
                    "timestamp": datetime.now().isoformat()
                }
                
                store_result = await self.redis_manager.store_market_data("BTCUSDT", test_market_data, 60)
                retrieve_result = await self.redis_manager.get_market_data("BTCUSDT")
                
                if store_result and retrieve_result:
                    results["basic_operations"] = True
                    logger.info("  💾 Redis Basic Operations: ✅ SUCCESS")
                else:
                    results["basic_operations"] = False
                    logger.info("  💾 Redis Basic Operations: ❌ FAILED")
                
                # Test serialization
                test_object = {"complex": {"nested": "data"}, "list": [1, 2, 3]}
                serialized = self.redis_manager.serialize_trading_object(test_object)
                deserialized = self.redis_manager.deserialize_trading_object(serialized)
                
                if deserialized == test_object:
                    results["serialization_test"] = True
                    logger.info("  🔄 Redis Serialization: ✅ SUCCESS")
                else:
                    results["serialization_test"] = False
                    logger.info("  🔄 Redis Serialization: ❌ FAILED")
            
        except Exception as e:
            error_msg = f"Redis test error: {e}"
            results["error_messages"].append(error_msg)
            logger.error(f"  ❌ {error_msg}")
        
        return results
    
    async def test_clickhouse_connection(self) -> Dict[str, Any]:
        """Test ClickHouse connection and schema"""
        logger.info("🧪 Testing ClickHouse Connection...")
        
        results = {
            "connection_test": False,
            "ping_test": False,
            "schema_initialization": False,
            "data_insertion": False,
            "error_messages": []
        }
        
        try:
            # Test basic ping
            ping_result = clickhouse_ping()
            results["ping_test"] = ping_result
            logger.info(f"  📡 ClickHouse Ping: {'✅ SUCCESS' if ping_result else '❌ FAILED'}")
            
            # Test schema initialization
            schema_result = await self.clickhouse_manager.initialize_schema()
            results["schema_initialization"] = schema_result
            logger.info(f"  🏗️ ClickHouse Schema: {'✅ SUCCESS' if schema_result else '❌ FAILED'}")
            
            # Test data insertion
            if schema_result:
                # Test OHLCV data insertion
                test_ohlcv = [{
                    "symbol": "BTCUSDT",
                    "timestamp": datetime.now(),
                    "open": 50000.0,
                    "high": 50100.0,
                    "low": 49900.0,
                    "close": 50050.0,
                    "volume": 1000.0,
                    "interval": "1m"
                }]
                
                ohlcv_result = await self.clickhouse_manager.insert_ohlcv_batch(test_ohlcv)
                
                # Test trade data insertion
                test_trades = [{
                    "trade_id": f"test_trade_{int(time.time())}",
                    "symbol": "BTCUSDT",
                    "side": "BUY",
                    "quantity": 100.0,
                    "price": 50000.0,
                    "timestamp": datetime.now(),
                    "order_type": "MARKET",
                    "portfolio_id": "test_portfolio"
                }]
                
                trade_result = await self.clickhouse_manager.insert_trade_batch(test_trades)
                
                if ohlcv_result and trade_result:
                    results["data_insertion"] = True
                    logger.info("  💾 ClickHouse Data Insertion: ✅ SUCCESS")
                else:
                    results["data_insertion"] = False
                    logger.info("  💾 ClickHouse Data Insertion: ❌ FAILED")
            
            results["connection_test"] = ping_result and schema_result
            
        except Exception as e:
            error_msg = f"ClickHouse test error: {e}"
            results["error_messages"].append(error_msg)
            logger.error(f"  ❌ {error_msg}")
        
        return results
    
    async def test_real_time_data_flow(self) -> Dict[str, Any]:
        """Test real-time data flow between Redis and ClickHouse"""
        logger.info("🧪 Testing Real-Time Data Flow...")
        
        results = {
            "redis_to_clickhouse": False,
            "data_consistency": False,
            "performance_test": False,
            "error_messages": []
        }
        
        try:
            # Generate sample real-time data
            symbols = ["BTCUSDT", "ETHUSDT", "ADAUSDT"]
            start_time = time.time()
            
            # Store data in Redis (simulating real-time feeds)
            redis_operations = 0
            for symbol in symbols:
                for i in range(10):  # 10 data points per symbol
                    market_data = {
                        "symbol": symbol,
                        "price": 50000.0 + i * 10,
                        "volume": 1000 + i * 100,
                        "timestamp": datetime.now().isoformat()
                    }
                    
                    await self.redis_manager.store_market_data(symbol, market_data, 300)
                    await self.redis_manager.store_price_feed(symbol, market_data["price"], market_data["volume"])
                    redis_operations += 2
            
            # Retrieve data from Redis and store in ClickHouse
            clickhouse_operations = 0
            for symbol in symbols:
                # Get market data from Redis
                market_data = await self.redis_manager.get_market_data(symbol)
                if market_data:
                    # Convert to OHLCV format for ClickHouse
                    ohlcv_data = [{
                        "symbol": symbol,
                        "timestamp": datetime.now(),
                        "open": market_data["price"],
                        "high": market_data["price"] * 1.01,
                        "low": market_data["price"] * 0.99,
                        "close": market_data["price"],
                        "volume": market_data["volume"],
                        "interval": "1m"
                    }]
                    
                    await self.clickhouse_manager.insert_ohlcv_batch(ohlcv_data)
                    clickhouse_operations += 1
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            if redis_operations > 0 and clickhouse_operations > 0:
                results["redis_to_clickhouse"] = True
                results["data_consistency"] = True
                results["performance_test"] = processing_time < 5.0  # Should complete in under 5 seconds
                
                logger.info(f"  🔄 Data Flow: ✅ SUCCESS ({redis_operations} Redis ops, {clickhouse_operations} ClickHouse ops)")
                logger.info(f"  ⚡ Performance: {'✅ GOOD' if results['performance_test'] else '⚠️ SLOW'} ({processing_time:.2f}s)")
            else:
                logger.info("  🔄 Data Flow: ❌ FAILED")
            
        except Exception as e:
            error_msg = f"Data flow test error: {e}"
            results["error_messages"].append(error_msg)
            logger.error(f"  ❌ {error_msg}")
        
        return results
    
    async def generate_comprehensive_report(self) -> Dict[str, Any]:
        """Generate comprehensive database integration report"""
        logger.info("📊 Generating Database Integration Report...")
        
        # Run all tests
        redis_results = await self.test_redis_connection()
        clickhouse_results = await self.test_clickhouse_connection()
        data_flow_results = await self.test_real_time_data_flow()
        
        # Calculate overall scores
        redis_score = sum([
            redis_results["connection_test"],
            redis_results["ping_test"],
            redis_results["basic_operations"],
            redis_results["serialization_test"]
        ]) / 4 * 100
        
        clickhouse_score = sum([
            clickhouse_results["connection_test"],
            clickhouse_results["ping_test"],
            clickhouse_results["schema_initialization"],
            clickhouse_results["data_insertion"]
        ]) / 4 * 100
        
        data_flow_score = sum([
            data_flow_results["redis_to_clickhouse"],
            data_flow_results["data_consistency"],
            data_flow_results["performance_test"]
        ]) / 3 * 100
        
        overall_score = (redis_score + clickhouse_score + data_flow_score) / 3
        
        report = {
            "timestamp": datetime.now().isoformat(),
            "redis_tests": redis_results,
            "clickhouse_tests": clickhouse_results,
            "data_flow_tests": data_flow_results,
            "scores": {
                "redis_score": redis_score,
                "clickhouse_score": clickhouse_score,
                "data_flow_score": data_flow_score,
                "overall_score": overall_score
            },
            "integration_status": "EXCELLENT" if overall_score >= 90 else "GOOD" if overall_score >= 70 else "NEEDS_ATTENTION"
        }
        
        # Print summary
        logger.info("=" * 80)
        logger.info("🎯 DATABASE INTEGRATION SUMMARY")
        logger.info("=" * 80)
        logger.info(f"📊 Redis Score: {redis_score:.1f}%")
        logger.info(f"📊 ClickHouse Score: {clickhouse_score:.1f}%")
        logger.info(f"📊 Data Flow Score: {data_flow_score:.1f}%")
        logger.info(f"🎯 Overall Score: {overall_score:.1f}%")
        logger.info(f"✅ Integration Status: {report['integration_status']}")
        logger.info("=" * 80)
        
        if overall_score >= 90:
            logger.info("🎉 EXCELLENT DATABASE INTEGRATION!")
        elif overall_score >= 70:
            logger.info("👍 GOOD DATABASE INTEGRATION!")
        else:
            logger.warning("⚠️ DATABASE INTEGRATION NEEDS ATTENTION")
        
        return report


async def main():
    """Main test execution"""
    logger.info("🚀 Starting Database Connections Tests")
    logger.info("=" * 60)
    
    tester = DatabaseTester()
    
    try:
        # Run comprehensive tests
        report = await tester.generate_comprehensive_report()
        
        logger.info("\n" + "=" * 60)
        logger.info("🎉 Database Integration Tests Completed!")
        
        return report
        
    except Exception as e:
        logger.error(f"❌ Test execution failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
